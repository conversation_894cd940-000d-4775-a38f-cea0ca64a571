// React Hook for Market Data
// Provides easy access to live market data throughout the application

import { useState, useEffect, useCallback } from 'react';
import axios from 'axios';
import { Stock, OptionsChain, MarketAnalysis } from '../types';

interface MarketDataState {
  stocks: { [symbol: string]: Stock };
  options: { [symbol: string]: OptionsChain[] };
  analysis: { [symbol: string]: MarketAnalysis };
  account: any;
  marketOverview: {
    indices: any[];
    sectors: any[];
    isMarketOpen: boolean;
  };
  upcomingEvents: {
    earnings: any[];
    dividends: any[];
  };
  loading: boolean;
  error: string | null;
  lastUpdated: Date | null;
}

export function useMarketData(symbols: string[] = []) {
  const [data, setData] = useState<MarketDataState>({
    stocks: {},
    options: {},
    analysis: {},
    account: null,
    marketOverview: { indices: [], sectors: [], isMarketOpen: false },
    upcomingEvents: { earnings: [], dividends: [] },
    loading: false,
    error: null,
    lastUpdated: null
  });

  const [refreshInterval, setRefreshInterval] = useState<NodeJS.Timeout | null>(null);

  /**
   * Fetch stock data
   * Simple explanation: Get current stock prices and info
   */
  const fetchStocks = useCallback(async (symbolList?: string[]) => {
    try {
      const targetSymbols = symbolList || symbols;
      if (targetSymbols.length === 0) return;

      const response = await axios.get('/api/stocks', {
        params: { symbols: targetSymbols.join(',') }
      });

      if (response.data.success) {
        setData(prev => ({
          ...prev,
          stocks: { ...prev.stocks, ...response.data.data },
          lastUpdated: new Date(),
          error: null
        }));
      }
    } catch (error) {
      console.error('Error fetching stocks:', error);
      setData(prev => ({
        ...prev,
        error: 'Failed to fetch stock data'
      }));
    }
  }, [symbols]);

  /**
   * Fetch options data
   * Simple explanation: Get available option contracts
   */
  const fetchOptions = useCallback(async (symbolList?: string[]) => {
    try {
      const targetSymbols = symbolList || symbols;
      if (targetSymbols.length === 0) return;

      const response = await axios.get('/api/options', {
        params: { symbols: targetSymbols.join(',') }
      });

      if (response.data.success) {
        setData(prev => ({
          ...prev,
          options: { ...prev.options, ...response.data.data },
          lastUpdated: new Date(),
          error: null
        }));
      }
    } catch (error) {
      console.error('Error fetching options:', error);
      setData(prev => ({
        ...prev,
        error: 'Failed to fetch options data'
      }));
    }
  }, [symbols]);

  /**
   * Fetch market analysis
   * Simple explanation: Get market conditions and trading recommendations
   */
  const fetchAnalysis = useCallback(async (symbolList?: string[]) => {
    try {
      const targetSymbols = symbolList || symbols;
      if (targetSymbols.length === 0) return;

      const response = await axios.get('/api/market-analysis', {
        params: { symbols: targetSymbols.join(',') }
      });

      if (response.data.success) {
        setData(prev => ({
          ...prev,
          analysis: { ...prev.analysis, ...response.data.data.analysis },
          marketOverview: response.data.data.marketOverview,
          upcomingEvents: response.data.data.upcomingEvents,
          lastUpdated: new Date(),
          error: null
        }));
      }
    } catch (error) {
      console.error('Error fetching analysis:', error);
      setData(prev => ({
        ...prev,
        error: 'Failed to fetch market analysis'
      }));
    }
  }, [symbols]);

  /**
   * Fetch account information
   * Simple explanation: Get your account balance and positions
   */
  const fetchAccount = useCallback(async () => {
    try {
      const response = await axios.get('/api/account');

      if (response.data.success) {
        setData(prev => ({
          ...prev,
          account: response.data.data,
          lastUpdated: new Date(),
          error: null
        }));
      }
    } catch (error) {
      console.error('Error fetching account:', error);
      setData(prev => ({
        ...prev,
        error: 'Failed to fetch account data'
      }));
    }
  }, []);

  /**
   * Refresh all data
   * Simple explanation: Update all market information
   */
  const refreshAll = useCallback(async () => {
    setData(prev => ({ ...prev, loading: true, error: null }));

    try {
      await Promise.all([
        fetchStocks(),
        fetchOptions(),
        fetchAnalysis(),
        fetchAccount()
      ]);
    } catch (error) {
      console.error('Error refreshing data:', error);
    } finally {
      setData(prev => ({ ...prev, loading: false }));
    }
  }, [fetchStocks, fetchOptions, fetchAnalysis, fetchAccount]);

  /**
   * Start auto-refresh
   * Simple explanation: Automatically update data every few minutes
   */
  const startAutoRefresh = useCallback((intervalMs: number = 60000) => {
    if (refreshInterval) {
      clearInterval(refreshInterval);
    }

    const interval = setInterval(() => {
      refreshAll();
    }, intervalMs);

    setRefreshInterval(interval);
    return interval;
  }, [refreshAll, refreshInterval]);

  /**
   * Stop auto-refresh
   * Simple explanation: Stop automatic data updates
   */
  const stopAutoRefresh = useCallback(() => {
    if (refreshInterval) {
      clearInterval(refreshInterval);
      setRefreshInterval(null);
    }
  }, [refreshInterval]);

  /**
   * Place an order
   * Simple explanation: Buy or sell stocks
   */
  const placeOrder = useCallback(async (
    symbol: string,
    quantity: number,
    side: 'buy' | 'sell',
    type: 'market' | 'limit' = 'market',
    limitPrice?: number
  ) => {
    try {
      const response = await axios.post('/api/orders', {
        symbol,
        quantity,
        side,
        type,
        limitPrice
      });

      if (response.data.success) {
        // Refresh account data after placing order
        await fetchAccount();
        return response.data;
      } else {
        throw new Error(response.data.error || 'Failed to place order');
      }
    } catch (error) {
      console.error('Error placing order:', error);
      throw error;
    }
  }, [fetchAccount]);

  /**
   * Get order history
   * Simple explanation: See your past trades
   */
  const getOrderHistory = useCallback(async (limit: number = 50) => {
    try {
      const response = await axios.get('/api/orders', {
        params: { limit }
      });

      if (response.data.success) {
        return response.data.data;
      } else {
        throw new Error(response.data.error || 'Failed to fetch orders');
      }
    } catch (error) {
      console.error('Error fetching orders:', error);
      throw error;
    }
  }, []);

  // Initial data load
  useEffect(() => {
    if (symbols.length > 0) {
      refreshAll();
    }
  }, [symbols, refreshAll]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (refreshInterval) {
        clearInterval(refreshInterval);
      }
    };
  }, [refreshInterval]);

  return {
    // Data
    stocks: data.stocks,
    options: data.options,
    analysis: data.analysis,
    account: data.account,
    marketOverview: data.marketOverview,
    upcomingEvents: data.upcomingEvents,
    
    // State
    loading: data.loading,
    error: data.error,
    lastUpdated: data.lastUpdated,
    
    // Actions
    refreshAll,
    fetchStocks,
    fetchOptions,
    fetchAnalysis,
    fetchAccount,
    startAutoRefresh,
    stopAutoRefresh,
    placeOrder,
    getOrderHistory,
    
    // Utilities
    isMarketOpen: data.marketOverview.isMarketOpen,
    hasData: Object.keys(data.stocks).length > 0
  };
}
