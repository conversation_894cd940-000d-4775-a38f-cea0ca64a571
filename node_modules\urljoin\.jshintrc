{"camelcase": true, "curly": true, "eqeqeq": true, "forin": true, "latedef": true, "supernew": true, "sub": true, "scripturl": true, "proto": true, "notypeof": true, "multistr": true, "loopfunc": true, "evil": true, "eqnull": true, "unused": true, "undef": true, "quotmark": true, "noempty": true, "nonbsp": true, "noarg": true, "immed": true, "freeze": true, "funcscope": true, "lastsemic": true, "laxbreak": true, "laxcomma": true, "node": true}