"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/ibkr-test/page",{

/***/ "(app-pages-browser)/./app/ibkr-test/page.tsx":
/*!********************************!*\
  !*** ./app/ibkr-test/page.tsx ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ IBKRTestPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction IBKRTestPage() {\n    var _accountInfo_netLiquidation, _accountInfo_availableFunds, _accountInfo_buyingPower, _accountInfo_totalCashValue;\n    _s();\n    const [connectionStatus, setConnectionStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"Not Connected\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [accountInfo, setAccountInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const testConnection = async ()=>{\n        setLoading(true);\n        setError(\"\");\n        try {\n            console.log(\"\\uD83D\\uDD0C Testing IBKR connection...\");\n            const response = await fetch(\"/api/ibkr?action=connect\");\n            const result = await response.json();\n            if (result.success) {\n                setConnectionStatus(\"✅ Connected to IBKR!\");\n                console.log(\"✅ IBKR Connected successfully!\");\n                // Get account info\n                setTimeout(async ()=>{\n                    try {\n                        const accountResponse = await fetch(\"/api/ibkr?action=account\");\n                        const accountResult = await accountResponse.json();\n                        if (accountResult.success) {\n                            setAccountInfo(accountResult.data);\n                        }\n                    } catch (err) {\n                        console.log(\"Account info not available yet\");\n                    }\n                }, 2000);\n            } else {\n                setConnectionStatus(\"❌ Connection Failed\");\n                setError(result.message || \"Unknown error\");\n                console.error(\"❌ IBKR Connection failed:\", result.message);\n            }\n        } catch (err) {\n            setConnectionStatus(\"❌ Connection Error\");\n            setError(err instanceof Error ? err.message : \"Network error\");\n            console.error(\"❌ Connection error:\", err);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const checkStatus = async ()=>{\n        try {\n            const response = await fetch(\"/api/ibkr?action=status\");\n            const result = await response.json();\n            if (result.success) {\n                setConnectionStatus(result.data.connected ? \"✅ Connected\" : \"❌ Disconnected\");\n            }\n        } catch (err) {\n            setConnectionStatus(\"❌ Error checking status\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: \"20px\",\n            fontFamily: \"Arial\",\n            maxWidth: \"800px\",\n            margin: \"0 auto\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                style: {\n                    color: \"#1e40af\",\n                    marginBottom: \"20px\"\n                },\n                children: \"\\uD83C\\uDFE6 IBKR Connection Test\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: \"#f0f9ff\",\n                    padding: \"20px\",\n                    borderRadius: \"10px\",\n                    marginBottom: \"20px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        children: \"\\uD83D\\uDCCB Setup Steps (Web Interface)\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                        style: {\n                            lineHeight: \"1.6\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"In IBKR Client Portal:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" Go to Settings → API → Enable API Access\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Download IB Gateway:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" Lighter than TWS, easier to use\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Get your Account ID:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" Look for account number (starts with U or DU)\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Set Socket Port:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" 7497 for paper trading, 7496 for live\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Enable Paper Trading:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" Perfect for learning without risk!\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 77,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: \"#22c55e\",\n                            color: \"white\",\n                            padding: \"15px\",\n                            borderRadius: \"5px\",\n                            marginTop: \"15px\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"\\uD83D\\uDCA1 TIP:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 11\n                            }, this),\n                            \" Start with Paper Trading (DU account) - it's free and perfect for learning!\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                lineNumber: 70,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: \"#fffbeb\",\n                    border: \"1px solid #fed7aa\",\n                    padding: \"20px\",\n                    borderRadius: \"10px\",\n                    marginBottom: \"20px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        children: \"\\uD83D\\uDD0D Find Your Account ID\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"Your IBKR account ID should be visible in the Client Portal:\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        style: {\n                            lineHeight: \"1.6\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Live Account:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 15\n                                    }, this),\n                                    ' Starts with \"U\" (like U1234567)'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Paper Account:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 15\n                                    }, this),\n                                    ' Starts with \"DU\" (like DU1234567)'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Location:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" Usually at the top of your Client Portal page\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 91,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: \"white\",\n                            padding: \"10px\",\n                            borderRadius: \"5px\",\n                            marginTop: \"10px\",\n                            border: \"1px solid #d1d5db\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                style: {\n                                    display: \"block\",\n                                    marginBottom: \"5px\",\n                                    fontWeight: \"bold\"\n                                },\n                                children: \"Enter your Account ID:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                placeholder: \"e.g., DU1234567 or U1234567\",\n                                style: {\n                                    width: \"100%\",\n                                    padding: \"8px\",\n                                    border: \"1px solid #d1d5db\",\n                                    borderRadius: \"3px\"\n                                },\n                                onChange: (e)=>console.log(\"Account ID:\", e.target.value)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    fontSize: \"12px\",\n                                    color: \"#6b7280\",\n                                    marginTop: \"5px\"\n                                },\n                                children: \"We'll use this to connect to your IBKR account\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                lineNumber: 85,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: \"white\",\n                    padding: \"20px\",\n                    borderRadius: \"10px\",\n                    boxShadow: \"0 2px 4px rgba(0,0,0,0.1)\",\n                    marginBottom: \"20px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        children: \"Connection Status\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        style: {\n                            fontSize: \"18px\",\n                            fontWeight: \"bold\",\n                            color: connectionStatus.includes(\"✅\") ? \"#22c55e\" : \"#ef4444\"\n                        },\n                        children: connectionStatus\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 9\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: \"#fef2f2\",\n                            border: \"1px solid #fecaca\",\n                            padding: \"10px\",\n                            borderRadius: \"5px\",\n                            marginTop: \"10px\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            style: {\n                                color: \"#dc2626\",\n                                margin: 0\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"Error:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 56\n                                }, this),\n                                \" \",\n                                error\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            marginTop: \"15px\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: testConnection,\n                                disabled: loading,\n                                style: {\n                                    background: loading ? \"#9ca3af\" : \"#1e40af\",\n                                    color: \"white\",\n                                    padding: \"10px 20px\",\n                                    border: \"none\",\n                                    borderRadius: \"5px\",\n                                    cursor: loading ? \"not-allowed\" : \"pointer\",\n                                    marginRight: \"10px\",\n                                    fontSize: \"16px\"\n                                },\n                                children: loading ? \"\\uD83D\\uDD04 Connecting...\" : \"\\uD83D\\uDD0C Connect to IBKR\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 121,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: checkStatus,\n                                style: {\n                                    background: \"#6b7280\",\n                                    color: \"white\",\n                                    padding: \"10px 20px\",\n                                    border: \"none\",\n                                    borderRadius: \"5px\",\n                                    cursor: \"pointer\",\n                                    fontSize: \"16px\"\n                                },\n                                children: \"\\uD83D\\uDCCA Check Status\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                lineNumber: 108,\n                columnNumber: 7\n            }, this),\n            accountInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: \"#f0fdf4\",\n                    border: \"1px solid #bbf7d0\",\n                    padding: \"20px\",\n                    borderRadius: \"10px\",\n                    marginBottom: \"20px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        style: {\n                            color: \"#15803d\"\n                        },\n                        children: \"\\uD83D\\uDCB0 Account Information\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"grid\",\n                            gridTemplateColumns: \"repeat(auto-fit, minmax(200px, 1fr))\",\n                            gap: \"15px\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Net Liquidation:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 48\n                                    }, this),\n                                    \"$\",\n                                    ((_accountInfo_netLiquidation = accountInfo.netLiquidation) === null || _accountInfo_netLiquidation === void 0 ? void 0 : _accountInfo_netLiquidation.toLocaleString()) || \"N/A\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Available Funds:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 48\n                                    }, this),\n                                    \"$\",\n                                    ((_accountInfo_availableFunds = accountInfo.availableFunds) === null || _accountInfo_availableFunds === void 0 ? void 0 : _accountInfo_availableFunds.toLocaleString()) || \"N/A\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Buying Power:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 45\n                                    }, this),\n                                    \"$\",\n                                    ((_accountInfo_buyingPower = accountInfo.buyingPower) === null || _accountInfo_buyingPower === void 0 ? void 0 : _accountInfo_buyingPower.toLocaleString()) || \"N/A\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Total Cash:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 43\n                                    }, this),\n                                    \"$\",\n                                    ((_accountInfo_totalCashValue = accountInfo.totalCashValue) === null || _accountInfo_totalCashValue === void 0 ? void 0 : _accountInfo_totalCashValue.toLocaleString()) || \"N/A\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                lineNumber: 156,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: \"#fffbeb\",\n                    border: \"1px solid #fed7aa\",\n                    padding: \"20px\",\n                    borderRadius: \"10px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        style: {\n                            color: \"#d97706\"\n                        },\n                        children: \"\\uD83D\\uDD27 Troubleshooting\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            marginBottom: \"15px\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"If connection fails:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                style: {\n                                    marginTop: \"5px\",\n                                    lineHeight: \"1.6\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"Make sure TWS Gateway is running and logged in\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"Check that API is enabled: Configure → Settings → API → Settings\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"Verify port number: 7497 for paper trading, 7496 for live\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: 'Ensure \"Enable ActiveX and Socket Clients\" is checked'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"Try restarting TWS Gateway\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Common Error Messages:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                style: {\n                                    marginTop: \"5px\",\n                                    lineHeight: \"1.6\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Connection timeout:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 17\n                                            }, this),\n                                            \" TWS Gateway not running or wrong port\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Connection refused:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 17\n                                            }, this),\n                                            \" API not enabled in settings\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Authentication failed:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                                lineNumber: 198,\n                                                columnNumber: 17\n                                            }, this),\n                                            \" Wrong client ID or account\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                lineNumber: 179,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: \"#1e40af\",\n                    color: \"white\",\n                    padding: \"20px\",\n                    borderRadius: \"10px\",\n                    marginTop: \"20px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        children: \"\\uD83D\\uDE80 Quick Setup Links\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                        lineNumber: 204,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"grid\",\n                            gridTemplateColumns: \"repeat(auto-fit, minmax(250px, 1fr))\",\n                            gap: \"15px\",\n                            marginTop: \"15px\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    background: \"rgba(255,255,255,0.1)\",\n                                    padding: \"15px\",\n                                    borderRadius: \"5px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"\\uD83D\\uDCF1 Client Portal\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 46\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"https://www.interactivebrokers.com/sso/Login\",\n                                        target: \"_blank\",\n                                        style: {\n                                            color: \"#93c5fd\"\n                                        },\n                                        children: \"Enable API Access\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    background: \"rgba(255,255,255,0.1)\",\n                                    padding: \"15px\",\n                                    borderRadius: \"5px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"\\uD83D\\uDCBB Download IB Gateway\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 52\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"https://www.interactivebrokers.com/en/trading/ib-api.php\",\n                                        target: \"_blank\",\n                                        style: {\n                                            color: \"#93c5fd\"\n                                        },\n                                        children: \"Get IB Gateway\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    background: \"rgba(255,255,255,0.1)\",\n                                    padding: \"15px\",\n                                    borderRadius: \"5px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"\\uD83D\\uDCDA Paper Trading\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 46\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        style: {\n                                            color: \"#93c5fd\"\n                                        },\n                                        children: \"Enable in Client Portal Settings\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                        lineNumber: 205,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                lineNumber: 203,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: \"#22c55e\",\n                    color: \"white\",\n                    padding: \"15px\",\n                    borderRadius: \"10px\",\n                    marginTop: \"20px\",\n                    textAlign: \"center\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                            children: \"\\uD83D\\uDE80 Once connected, you'll have access to:\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 12\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"Professional options data • Real-time quotes • Advanced order types • Global markets\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                        lineNumber: 227,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                lineNumber: 225,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n        lineNumber: 67,\n        columnNumber: 5\n    }, this);\n}\n_s(IBKRTestPage, \"klk0zkIjsoflYpM8AFk84E2UPfQ=\");\n_c = IBKRTestPage;\nvar _c;\n$RefreshReg$(_c, \"IBKRTestPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/ibkr-test/page.tsx\n"));

/***/ })

});