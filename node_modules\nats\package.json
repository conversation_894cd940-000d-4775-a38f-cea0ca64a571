{"name": "nats", "version": "1.4.12", "description": "Node.js client for NATS, a lightweight, high-performance cloud native messaging system", "keywords": ["nats", "messaging", "pubsub", "publish", "subscribe", "queue", "distributed", "queueing"], "homepage": "https://nats.io", "repository": {"type": "git", "url": "**************:nats-io/nats.js.git"}, "bugs": {"url": "https://github.com/nats-io/nats.js/issues"}, "license": "Apache-2.0", "private": false, "author": {"name": "The NATS Authors"}, "contributors": [], "main": "./index.js", "scripts": {"depcheck": "dependency-check --no-dev package.json", "depcheck:unused": "dependency-check package.json --no-dev --entry ./**/*.js", "test:unit": "mkdir -p reports/ && NODE_ENV=test multi='spec=- xunit=reports/mocha-xunit.xml' nyc mocha --timeout 10000 --slow 750", "test": "npm run depcheck && npm run depcheck:unused && npm run lint && npm run test:typescript && npm run test:unit", "test:typescript": "./node_modules/typescript/bin/tsc --strict --noEmit tstest/main.ts", "coveralls": "mkdir coverage && nyc report --reporter=text-lcov > coverage/lcov.info", "cover": "nyc report --reporter=html && open coverage/index.html", "lint": "standard './**/*.js'", "fmt": "standard --fix './**/*.js'"}, "engines": {"node": ">= 8.0.0"}, "dependencies": {"nuid": "^1.1.4", "ts-nkeys": "^1.0.16"}, "devDependencies": {"@types/node": "^13.7.4", "coveralls": "^3.0.11", "dependency-check": "^4.1.0", "eslint": "^6.8.0", "minimist": ">=1.2.3", "mocha": "^7.1.1", "mocha-lcov-reporter": "1.3.0", "nyc": "^15.0.1", "should": "^13.2.3", "standard": "^14.3.3", "typescript": "^3.8.3"}, "typings": "./index.d.ts", "nyc": {"include": ["lib/**"], "exclude": ["test/**", "examples/**", "benchmark/**"]}, "bin": {"node-pub": "examples/node-pub", "node-sub": "examples/node-sub", "node-req": "examples/node-req", "node-reply": "examples/node-reply"}}