{"name": "nuid", "version": "1.1.6", "description": "NUID - A highly performant unique identifier generator.", "keywords": ["unique", "identifier", "generator"], "homepage": "https://nats.io", "repository": {"type": "git", "url": "**************:nats-io/node-nuid.git"}, "bugs": {"url": "https://github.com/nats-io/node-nuid/issues"}, "license": "Apache-2.0", "private": false, "author": {"name": "The NATS Authors"}, "contributors": [], "main": "./index.js", "scripts": {"depcheck": "dependency-check --no-dev package.json", "depcheck:unused": "dependency-check package.json --no-dev --entry ./**/*.js", "test:unit": "mkdir -p reports/ && NODE_ENV=test multi='spec=- xunit=reports/mocha-xunit.xml' nyc mocha --timeout 10000 --slow 750", "test": "npm run depcheck && npm run depcheck:unused && npm run lint && npm run test:unit", "coveralls": "nyc report --reporter=text-lcov | coveralls", "cover": "nyc report --reporter=html && open coverage/index.html", "lint": "standard './**/*.js'", "fmt": "standard --fix './**/*.js'"}, "engines": {"node": ">= 8.16.0"}, "devDependencies": {"coveralls": "^3.0.9", "dependency-check": "^4.1.0", "eslint": "^6.8.0", "mocha": "^7.0.1", "mocha-lcov-reporter": "1.3.0", "nyc": "^15.0.0", "should": "^13.2.3", "standard": "^14.3.1"}, "nyc": {"include": ["lib/**"], "exclude": ["test/**", "examples/**", "benchmark/**"]}, "typings": "./index.d.ts"}