// API Route for Account Information
// Provides account balance, positions, and trading data

import { NextRequest, NextResponse } from 'next/server';
import { MarketDataService } from '../../../lib/api/market-data-service';

const marketData = new MarketDataService();

export async function GET(request: NextRequest) {
  try {
    // Get account information and positions
    const accountInfo = await marketData.getAccountInfo();
    
    return NextResponse.json({
      success: true,
      data: accountInfo,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error in account API:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch account data',
        message: error instanceof Error ? error.message : 'Unknown error'
      }, 
      { status: 500 }
    );
  }
}
