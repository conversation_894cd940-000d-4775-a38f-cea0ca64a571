// Financial Modeling Prep API Client
// Provides comprehensive financial data, options chains, and company information

import axios from 'axios';
import { Stock, OptionContract, OptionsChain } from '../types';

export class FMPClient {
  private apiKey: string;
  private baseUrl: string;

  constructor() {
    this.apiKey = process.env.FMP_API_KEY || '';
    this.baseUrl = process.env.FMP_BASE_URL || 'https://financialmodelingprep.com/api';
  }

  private buildUrl(endpoint: string, params: Record<string, any> = {}): string {
    const url = new URL(`${this.baseUrl}${endpoint}`);
    url.searchParams.append('apikey', this.apiKey);
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        url.searchParams.append(key, value.toString());
      }
    });

    return url.toString();
  }

  /**
   * Get comprehensive stock information
   * Simple explanation: Get detailed info about companies including financials
   */
  async getStockInfo(symbols: string[]): Promise<{ [symbol: string]: Stock }> {
    try {
      const symbolsParam = symbols.join(',');
      
      // Get real-time quotes
      const quotesUrl = this.buildUrl('/v3/quote', { symbols: symbolsParam });
      const quotesResponse = await axios.get(quotesUrl);
      
      // Get company profiles for additional info
      const profilesUrl = this.buildUrl('/v3/profile', { symbols: symbolsParam });
      const profilesResponse = await axios.get(profilesUrl);
      
      const quotes = quotesResponse.data;
      const profiles = profilesResponse.data;
      
      const stocks: { [symbol: string]: Stock } = {};
      
      quotes.forEach((quote: any) => {
        const profile = profiles.find((p: any) => p.symbol === quote.symbol);
        
        stocks[quote.symbol] = {
          symbol: quote.symbol,
          name: profile?.companyName || quote.name || `${quote.symbol} Inc.`,
          price: quote.price || 0,
          change: quote.change || 0,
          changePercent: quote.changesPercentage || 0,
          volume: quote.volume || 0,
          marketCap: profile?.mktCap || 0,
          sector: profile?.sector || 'Unknown'
        };
      });

      return stocks;
    } catch (error) {
      console.error('Error fetching stock info from FMP:', error);
      throw new Error('Failed to fetch stock information');
    }
  }

  /**
   * Get options chain for a stock
   * Simple explanation: Get all available option contracts for a stock
   */
  async getOptionsChain(symbol: string): Promise<OptionsChain[]> {
    try {
      const url = this.buildUrl(`/v3/options-chain/${symbol}`);
      const response = await axios.get(url);
      
      const optionsData = response.data;
      const chainsByExpiration: { [expiration: string]: { calls: OptionContract[], puts: OptionContract[] } } = {};
      
      optionsData.forEach((option: any) => {
        const expiration = option.expiration;
        
        if (!chainsByExpiration[expiration]) {
          chainsByExpiration[expiration] = { calls: [], puts: [] };
        }
        
        const contract: OptionContract = {
          strike: option.strike,
          bid: option.bid || 0,
          ask: option.ask || 0,
          last: option.lastPrice || 0,
          volume: option.volume || 0,
          openInterest: option.openInterest || 0,
          impliedVolatility: option.impliedVolatility || 0,
          delta: option.delta || 0,
          gamma: option.gamma || 0,
          theta: option.theta || 0,
          vega: option.vega || 0,
          rho: option.rho || 0,
          intrinsicValue: this.calculateIntrinsicValue(option.type, option.strike, option.underlyingPrice),
          timeValue: (option.lastPrice || 0) - this.calculateIntrinsicValue(option.type, option.strike, option.underlyingPrice),
          daysToExpiration: this.calculateDaysToExpiration(option.expiration)
        };
        
        if (option.type === 'call') {
          chainsByExpiration[expiration].calls.push(contract);
        } else {
          chainsByExpiration[expiration].puts.push(contract);
        }
      });
      
      return Object.entries(chainsByExpiration).map(([expiration, chains]) => ({
        symbol,
        expiration,
        calls: chains.calls.sort((a, b) => a.strike - b.strike),
        puts: chains.puts.sort((a, b) => a.strike - b.strike)
      }));
    } catch (error) {
      console.error('Error fetching options chain from FMP:', error);
      throw new Error('Failed to fetch options chain');
    }
  }

  /**
   * Get historical stock prices for analysis
   * Simple explanation: Get past stock prices to analyze trends
   */
  async getHistoricalPrices(
    symbol: string, 
    period: '1day' | '5day' | '1month' | '3month' | '6month' | '1year' | '5year' = '1year'
  ): Promise<Array<{
    date: Date;
    open: number;
    high: number;
    low: number;
    close: number;
    volume: number;
  }>> {
    try {
      let endpoint = '/v3/historical-price-full';
      let params: any = {};
      
      if (period === '1day' || period === '5day') {
        endpoint = '/v3/historical-chart/1hour';
        params.from = this.getDateString(period === '1day' ? 1 : 5);
        params.to = this.getDateString(0);
      } else {
        params.from = this.getDateString(this.getPeriodDays(period));
        params.to = this.getDateString(0);
      }
      
      const url = this.buildUrl(`${endpoint}/${symbol}`, params);
      const response = await axios.get(url);
      
      const historical = response.data.historical || response.data;
      
      return historical.map((item: any) => ({
        date: new Date(item.date),
        open: item.open,
        high: item.high,
        low: item.low,
        close: item.close,
        volume: item.volume
      })).reverse(); // Most recent first
    } catch (error) {
      console.error('Error fetching historical prices from FMP:', error);
      throw new Error('Failed to fetch historical prices');
    }
  }

  /**
   * Get company financial ratios
   * Simple explanation: Get key financial metrics to evaluate company health
   */
  async getFinancialRatios(symbol: string): Promise<any> {
    try {
      const url = this.buildUrl(`/v3/ratios/${symbol}`, { limit: 1 });
      const response = await axios.get(url);
      
      return response.data[0] || {};
    } catch (error) {
      console.error('Error fetching financial ratios from FMP:', error);
      throw new Error('Failed to fetch financial ratios');
    }
  }

  /**
   * Get earnings calendar
   * Simple explanation: See when companies will report their earnings
   */
  async getEarningsCalendar(from?: string, to?: string): Promise<any[]> {
    try {
      const params: any = {};
      if (from) params.from = from;
      if (to) params.to = to;
      
      const url = this.buildUrl('/v3/earning_calendar', params);
      const response = await axios.get(url);
      
      return response.data.map((earning: any) => ({
        symbol: earning.symbol,
        date: earning.date,
        time: earning.time,
        epsEstimate: earning.epsEstimate,
        epsActual: earning.epsActual,
        revenueEstimate: earning.revenueEstimate,
        revenueActual: earning.revenueActual
      }));
    } catch (error) {
      console.error('Error fetching earnings calendar from FMP:', error);
      throw new Error('Failed to fetch earnings calendar');
    }
  }

  /**
   * Get dividend calendar
   * Simple explanation: See when companies will pay dividends
   */
  async getDividendCalendar(from?: string, to?: string): Promise<any[]> {
    try {
      const params: any = {};
      if (from) params.from = from;
      if (to) params.to = to;
      
      const url = this.buildUrl('/v3/stock_dividend_calendar', params);
      const response = await axios.get(url);
      
      return response.data.map((dividend: any) => ({
        symbol: dividend.symbol,
        date: dividend.date,
        recordDate: dividend.recordDate,
        paymentDate: dividend.paymentDate,
        declarationDate: dividend.declarationDate,
        dividend: dividend.dividend
      }));
    } catch (error) {
      console.error('Error fetching dividend calendar from FMP:', error);
      throw new Error('Failed to fetch dividend calendar');
    }
  }

  /**
   * Get market sector performance
   * Simple explanation: See how different market sectors are performing
   */
  async getSectorPerformance(): Promise<any[]> {
    try {
      const url = this.buildUrl('/v3/sector-performance');
      const response = await axios.get(url);
      
      return response.data.map((sector: any) => ({
        sector: sector.sector,
        changesPercentage: sector.changesPercentage
      }));
    } catch (error) {
      console.error('Error fetching sector performance from FMP:', error);
      throw new Error('Failed to fetch sector performance');
    }
  }

  /**
   * Get market indices (S&P 500, NASDAQ, etc.)
   * Simple explanation: See how the overall market is performing
   */
  async getMarketIndices(): Promise<any[]> {
    try {
      const indices = ['^GSPC', '^IXIC', '^DJI', '^RUT']; // S&P 500, NASDAQ, Dow Jones, Russell 2000
      const symbolsParam = indices.join(',');
      
      const url = this.buildUrl('/v3/quote', { symbols: symbolsParam });
      const response = await axios.get(url);
      
      return response.data.map((index: any) => ({
        symbol: index.symbol,
        name: this.getIndexName(index.symbol),
        price: index.price,
        change: index.change,
        changesPercentage: index.changesPercentage
      }));
    } catch (error) {
      console.error('Error fetching market indices from FMP:', error);
      throw new Error('Failed to fetch market indices');
    }
  }

  // Helper methods
  private calculateIntrinsicValue(type: string, strike: number, underlyingPrice: number): number {
    if (type === 'call') {
      return Math.max(0, underlyingPrice - strike);
    } else {
      return Math.max(0, strike - underlyingPrice);
    }
  }

  private calculateDaysToExpiration(expirationDate: string): number {
    const expiration = new Date(expirationDate);
    const today = new Date();
    const diffTime = expiration.getTime() - today.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }

  private getDateString(daysAgo: number): string {
    const date = new Date();
    date.setDate(date.getDate() - daysAgo);
    return date.toISOString().split('T')[0];
  }

  private getPeriodDays(period: string): number {
    switch (period) {
      case '1day': return 1;
      case '5day': return 5;
      case '1month': return 30;
      case '3month': return 90;
      case '6month': return 180;
      case '1year': return 365;
      case '5year': return 1825;
      default: return 365;
    }
  }

  private getIndexName(symbol: string): string {
    const names: { [key: string]: string } = {
      '^GSPC': 'S&P 500',
      '^IXIC': 'NASDAQ Composite',
      '^DJI': 'Dow Jones Industrial Average',
      '^RUT': 'Russell 2000'
    };
    return names[symbol] || symbol;
  }
}
