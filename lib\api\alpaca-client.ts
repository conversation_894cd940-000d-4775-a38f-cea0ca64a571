// Alpaca API Client for Live Market Data and Trading
// Provides real-time stock prices, options data, and portfolio management

import axios from 'axios';
import { Stock, Position, OptionContract } from '../types';

export class AlpacaClient {
  private apiKey: string;
  private secretKey: string;
  private baseUrl: string;
  private dataUrl: string;

  constructor() {
    this.apiKey = process.env.ALPACA_API_KEY || '';
    this.secretKey = process.env.ALPACA_SECRET_KEY || '';
    this.baseUrl = process.env.ALPACA_BASE_URL || 'https://paper-api.alpaca.markets';
    this.dataUrl = process.env.ALPACA_DATA_URL || 'https://data.alpaca.markets';
  }

  private getHeaders() {
    return {
      'APCA-API-KEY-ID': this.apiKey,
      'APCA-API-SECRET-KEY': this.secretKey,
      'Content-Type': 'application/json'
    };
  }

  /**
   * Get current stock prices for multiple symbols
   * Simple explanation: Get the latest prices for your watchlist stocks
   */
  async getStockPrices(symbols: string[]): Promise<{ [symbol: string]: Stock }> {
    try {
      const symbolsParam = symbols.join(',');
      const response = await axios.get(
        `${this.dataUrl}/v2/stocks/quotes/latest`,
        {
          headers: this.getHeaders(),
          params: {
            symbols: symbolsParam,
            feed: 'iex'
          }
        }
      );

      const quotes = response.data.quotes;
      const stocks: { [symbol: string]: Stock } = {};

      for (const symbol of symbols) {
        const quote = quotes[symbol];
        if (quote) {
          // Get additional stock info
          const barsResponse = await axios.get(
            `${this.dataUrl}/v2/stocks/bars`,
            {
              headers: this.getHeaders(),
              params: {
                symbols: symbol,
                timeframe: '1Day',
                limit: 2,
                feed: 'iex'
              }
            }
          );

          const bars = barsResponse.data.bars[symbol];
          const currentBar = bars?.[bars.length - 1];
          const previousBar = bars?.[bars.length - 2];

          const currentPrice = quote.ap || quote.bp || currentBar?.c || 0;
          const previousClose = previousBar?.c || currentPrice;
          const change = currentPrice - previousClose;
          const changePercent = previousClose > 0 ? (change / previousClose) * 100 : 0;

          stocks[symbol] = {
            symbol,
            name: await this.getCompanyName(symbol),
            price: currentPrice,
            change,
            changePercent,
            volume: currentBar?.v || 0,
            marketCap: 0, // Will be populated by FMP
            sector: 'Unknown' // Will be populated by FMP
          };
        }
      }

      return stocks;
    } catch (error) {
      console.error('Error fetching stock prices from Alpaca:', error);
      throw new Error('Failed to fetch stock prices');
    }
  }

  /**
   * Get account information and portfolio
   * Simple explanation: Check your account balance and current positions
   */
  async getAccount(): Promise<{
    equity: number;
    cash: number;
    buyingPower: number;
    dayTradeCount: number;
    portfolioValue: number;
  }> {
    try {
      const response = await axios.get(`${this.baseUrl}/v2/account`, {
        headers: this.getHeaders()
      });

      const account = response.data;
      return {
        equity: parseFloat(account.equity),
        cash: parseFloat(account.cash),
        buyingPower: parseFloat(account.buying_power),
        dayTradeCount: parseInt(account.daytrade_count),
        portfolioValue: parseFloat(account.portfolio_value)
      };
    } catch (error) {
      console.error('Error fetching account info from Alpaca:', error);
      throw new Error('Failed to fetch account information');
    }
  }

  /**
   * Get current positions from Alpaca
   * Simple explanation: See all your current stock and option positions
   */
  async getPositions(): Promise<any[]> {
    try {
      const response = await axios.get(`${this.baseUrl}/v2/positions`, {
        headers: this.getHeaders()
      });

      return response.data.map((position: any) => ({
        symbol: position.symbol,
        quantity: parseInt(position.qty),
        side: position.side,
        marketValue: parseFloat(position.market_value),
        costBasis: parseFloat(position.cost_basis),
        unrealizedPL: parseFloat(position.unrealized_pl),
        unrealizedPLPercent: parseFloat(position.unrealized_plpc) * 100,
        currentPrice: parseFloat(position.current_price),
        averageEntryPrice: parseFloat(position.avg_entry_price)
      }));
    } catch (error) {
      console.error('Error fetching positions from Alpaca:', error);
      throw new Error('Failed to fetch positions');
    }
  }

  /**
   * Get historical price data for analysis
   * Simple explanation: Get past prices to analyze trends and patterns
   */
  async getHistoricalData(
    symbol: string, 
    timeframe: '1Min' | '5Min' | '15Min' | '1Hour' | '1Day' = '1Day',
    limit: number = 100
  ): Promise<Array<{
    timestamp: Date;
    open: number;
    high: number;
    low: number;
    close: number;
    volume: number;
  }>> {
    try {
      const response = await axios.get(`${this.dataUrl}/v2/stocks/bars`, {
        headers: this.getHeaders(),
        params: {
          symbols: symbol,
          timeframe,
          limit,
          feed: 'iex'
        }
      });

      const bars = response.data.bars[symbol] || [];
      return bars.map((bar: any) => ({
        timestamp: new Date(bar.t),
        open: bar.o,
        high: bar.h,
        low: bar.l,
        close: bar.c,
        volume: bar.v
      }));
    } catch (error) {
      console.error('Error fetching historical data from Alpaca:', error);
      throw new Error('Failed to fetch historical data');
    }
  }

  /**
   * Place a stock order
   * Simple explanation: Buy or sell stocks through your account
   */
  async placeStockOrder(
    symbol: string,
    quantity: number,
    side: 'buy' | 'sell',
    type: 'market' | 'limit' = 'market',
    limitPrice?: number
  ): Promise<any> {
    try {
      const orderData: any = {
        symbol,
        qty: quantity,
        side,
        type,
        time_in_force: 'day'
      };

      if (type === 'limit' && limitPrice) {
        orderData.limit_price = limitPrice;
      }

      const response = await axios.post(`${this.baseUrl}/v2/orders`, orderData, {
        headers: this.getHeaders()
      });

      return response.data;
    } catch (error) {
      console.error('Error placing stock order:', error);
      throw new Error('Failed to place stock order');
    }
  }

  /**
   * Get order history
   * Simple explanation: See all your past trades and their status
   */
  async getOrders(status: 'open' | 'closed' | 'all' = 'all', limit: number = 50): Promise<any[]> {
    try {
      const response = await axios.get(`${this.baseUrl}/v2/orders`, {
        headers: this.getHeaders(),
        params: {
          status,
          limit,
          direction: 'desc'
        }
      });

      return response.data.map((order: any) => ({
        id: order.id,
        symbol: order.symbol,
        quantity: parseInt(order.qty),
        side: order.side,
        type: order.type,
        status: order.status,
        filledQuantity: parseInt(order.filled_qty || 0),
        filledPrice: parseFloat(order.filled_avg_price || 0),
        limitPrice: order.limit_price ? parseFloat(order.limit_price) : null,
        submittedAt: new Date(order.submitted_at),
        filledAt: order.filled_at ? new Date(order.filled_at) : null
      }));
    } catch (error) {
      console.error('Error fetching orders from Alpaca:', error);
      throw new Error('Failed to fetch orders');
    }
  }

  /**
   * Get company name for a symbol
   * Simple explanation: Convert stock symbol to company name (AAPL -> Apple Inc.)
   */
  private async getCompanyName(symbol: string): Promise<string> {
    // This would typically come from a separate API or database
    // For now, return the symbol as a fallback
    const commonNames: { [key: string]: string } = {
      'AAPL': 'Apple Inc.',
      'MSFT': 'Microsoft Corporation',
      'GOOGL': 'Alphabet Inc.',
      'AMZN': 'Amazon.com Inc.',
      'TSLA': 'Tesla Inc.',
      'META': 'Meta Platforms Inc.',
      'NVDA': 'NVIDIA Corporation',
      'JPM': 'JPMorgan Chase & Co.',
      'JNJ': 'Johnson & Johnson',
      'V': 'Visa Inc.'
    };

    return commonNames[symbol] || `${symbol} Inc.`;
  }

  /**
   * Check if markets are open
   * Simple explanation: See if you can trade right now
   */
  async isMarketOpen(): Promise<boolean> {
    try {
      const response = await axios.get(`${this.baseUrl}/v2/clock`, {
        headers: this.getHeaders()
      });

      return response.data.is_open;
    } catch (error) {
      console.error('Error checking market status:', error);
      return false;
    }
  }

  /**
   * Get market calendar
   * Simple explanation: See when markets are open/closed
   */
  async getMarketCalendar(start?: string, end?: string): Promise<any[]> {
    try {
      const params: any = {};
      if (start) params.start = start;
      if (end) params.end = end;

      const response = await axios.get(`${this.baseUrl}/v2/calendar`, {
        headers: this.getHeaders(),
        params
      });

      return response.data;
    } catch (error) {
      console.error('Error fetching market calendar:', error);
      throw new Error('Failed to fetch market calendar');
    }
  }
}
