"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/ibkr-test/page",{

/***/ "(app-pages-browser)/./app/ibkr-test/page.tsx":
/*!********************************!*\
  !*** ./app/ibkr-test/page.tsx ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ IBKRTestPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction IBKRTestPage() {\n    var _accountInfo_netLiquidation, _accountInfo_availableFunds, _accountInfo_buyingPower, _accountInfo_totalCashValue;\n    _s();\n    const [connectionStatus, setConnectionStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"Not Connected\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [accountInfo, setAccountInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const testConnection = async ()=>{\n        setLoading(true);\n        setError(\"\");\n        try {\n            console.log(\"\\uD83D\\uDD0C Testing IBKR connection...\");\n            const response = await fetch(\"/api/ibkr?action=connect\");\n            const result = await response.json();\n            if (result.success) {\n                setConnectionStatus(\"✅ Connected to IBKR!\");\n                console.log(\"✅ IBKR Connected successfully!\");\n                // Get account info\n                setTimeout(async ()=>{\n                    try {\n                        const accountResponse = await fetch(\"/api/ibkr?action=account\");\n                        const accountResult = await accountResponse.json();\n                        if (accountResult.success) {\n                            setAccountInfo(accountResult.data);\n                        }\n                    } catch (err) {\n                        console.log(\"Account info not available yet\");\n                    }\n                }, 2000);\n            } else {\n                setConnectionStatus(\"❌ Connection Failed\");\n                setError(result.message || \"Unknown error\");\n                console.error(\"❌ IBKR Connection failed:\", result.message);\n            }\n        } catch (err) {\n            setConnectionStatus(\"❌ Connection Error\");\n            setError(err instanceof Error ? err.message : \"Network error\");\n            console.error(\"❌ Connection error:\", err);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const checkStatus = async ()=>{\n        try {\n            const response = await fetch(\"/api/ibkr?action=status\");\n            const result = await response.json();\n            if (result.success) {\n                setConnectionStatus(result.data.connected ? \"✅ Connected\" : \"❌ Disconnected\");\n            }\n        } catch (err) {\n            setConnectionStatus(\"❌ Error checking status\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: \"20px\",\n            fontFamily: \"Arial\",\n            maxWidth: \"800px\",\n            margin: \"0 auto\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                style: {\n                    color: \"#1e40af\",\n                    marginBottom: \"20px\"\n                },\n                children: \"\\uD83C\\uDFE6 IBKR Connection Test\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: \"#ef4444\",\n                    color: \"white\",\n                    padding: \"20px\",\n                    borderRadius: \"10px\",\n                    marginBottom: \"20px\",\n                    textAlign: \"center\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            margin: \"0 0 10px 0\"\n                        },\n                        children: \"\\uD83D\\uDEA8 IMPORTANT: NO API KEY NEEDED!\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        style: {\n                            margin: 0,\n                            fontSize: \"16px\"\n                        },\n                        children: \"IBKR doesn't use API keys! You just need: ✅ Your login ✅ IB Gateway ✅ Account number\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                lineNumber: 70,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: \"#f0f9ff\",\n                    padding: \"20px\",\n                    borderRadius: \"10px\",\n                    marginBottom: \"20px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        children: \"\\uD83D\\uDCCB Setup Steps (Web Interface)\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                        style: {\n                            lineHeight: \"1.6\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"In IBKR Client Portal:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" Go to Settings → API → Enable API Access\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Download IB Gateway:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" Lighter than TWS, easier to use\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Get your Account ID:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 82,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" Look for account number (starts with U or DU)\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Set Socket Port:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" 7497 for paper trading, 7496 for live\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Enable Paper Trading:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 84,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" Perfect for learning without risk!\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: \"#22c55e\",\n                            color: \"white\",\n                            padding: \"15px\",\n                            borderRadius: \"5px\",\n                            marginTop: \"15px\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"\\uD83D\\uDCA1 TIP:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 11\n                            }, this),\n                            \" Start with Paper Trading (DU account) - it's free and perfect for learning!\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                lineNumber: 77,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: \"#fffbeb\",\n                    border: \"1px solid #fed7aa\",\n                    padding: \"20px\",\n                    borderRadius: \"10px\",\n                    marginBottom: \"20px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        children: \"\\uD83D\\uDD0D Find Your Account ID\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"Your IBKR account ID should be visible in the Client Portal:\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        style: {\n                            lineHeight: \"1.6\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Live Account:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 15\n                                    }, this),\n                                    ' Starts with \"U\" (like U1234567)'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Paper Account:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 15\n                                    }, this),\n                                    ' Starts with \"DU\" (like DU1234567)'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Location:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" Usually at the top of your Client Portal page\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: \"white\",\n                            padding: \"10px\",\n                            borderRadius: \"5px\",\n                            marginTop: \"10px\",\n                            border: \"1px solid #d1d5db\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                style: {\n                                    display: \"block\",\n                                    marginBottom: \"5px\",\n                                    fontWeight: \"bold\"\n                                },\n                                children: \"Enter your Account ID:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                placeholder: \"e.g., DU1234567 or U1234567\",\n                                style: {\n                                    width: \"100%\",\n                                    padding: \"8px\",\n                                    border: \"1px solid #d1d5db\",\n                                    borderRadius: \"3px\"\n                                },\n                                onChange: (e)=>console.log(\"Account ID:\", e.target.value)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    fontSize: \"12px\",\n                                    color: \"#6b7280\",\n                                    marginTop: \"5px\"\n                                },\n                                children: \"We'll use this to connect to your IBKR account\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                lineNumber: 92,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: \"white\",\n                    padding: \"20px\",\n                    borderRadius: \"10px\",\n                    boxShadow: \"0 2px 4px rgba(0,0,0,0.1)\",\n                    marginBottom: \"20px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        children: \"Connection Status\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        style: {\n                            fontSize: \"18px\",\n                            fontWeight: \"bold\",\n                            color: connectionStatus.includes(\"✅\") ? \"#22c55e\" : \"#ef4444\"\n                        },\n                        children: connectionStatus\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 9\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: \"#fef2f2\",\n                            border: \"1px solid #fecaca\",\n                            padding: \"10px\",\n                            borderRadius: \"5px\",\n                            marginTop: \"10px\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            style: {\n                                color: \"#dc2626\",\n                                margin: 0\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"Error:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 56\n                                }, this),\n                                \" \",\n                                error\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            marginTop: \"15px\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: testConnection,\n                                disabled: loading,\n                                style: {\n                                    background: loading ? \"#9ca3af\" : \"#1e40af\",\n                                    color: \"white\",\n                                    padding: \"10px 20px\",\n                                    border: \"none\",\n                                    borderRadius: \"5px\",\n                                    cursor: loading ? \"not-allowed\" : \"pointer\",\n                                    marginRight: \"10px\",\n                                    fontSize: \"16px\"\n                                },\n                                children: loading ? \"\\uD83D\\uDD04 Connecting...\" : \"\\uD83D\\uDD0C Connect to IBKR\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: checkStatus,\n                                style: {\n                                    background: \"#6b7280\",\n                                    color: \"white\",\n                                    padding: \"10px 20px\",\n                                    border: \"none\",\n                                    borderRadius: \"5px\",\n                                    cursor: \"pointer\",\n                                    fontSize: \"16px\"\n                                },\n                                children: \"\\uD83D\\uDCCA Check Status\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                lineNumber: 115,\n                columnNumber: 7\n            }, this),\n            accountInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: \"#f0fdf4\",\n                    border: \"1px solid #bbf7d0\",\n                    padding: \"20px\",\n                    borderRadius: \"10px\",\n                    marginBottom: \"20px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        style: {\n                            color: \"#15803d\"\n                        },\n                        children: \"\\uD83D\\uDCB0 Account Information\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"grid\",\n                            gridTemplateColumns: \"repeat(auto-fit, minmax(200px, 1fr))\",\n                            gap: \"15px\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Net Liquidation:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 48\n                                    }, this),\n                                    \"$\",\n                                    ((_accountInfo_netLiquidation = accountInfo.netLiquidation) === null || _accountInfo_netLiquidation === void 0 ? void 0 : _accountInfo_netLiquidation.toLocaleString()) || \"N/A\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Available Funds:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 48\n                                    }, this),\n                                    \"$\",\n                                    ((_accountInfo_availableFunds = accountInfo.availableFunds) === null || _accountInfo_availableFunds === void 0 ? void 0 : _accountInfo_availableFunds.toLocaleString()) || \"N/A\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Buying Power:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 45\n                                    }, this),\n                                    \"$\",\n                                    ((_accountInfo_buyingPower = accountInfo.buyingPower) === null || _accountInfo_buyingPower === void 0 ? void 0 : _accountInfo_buyingPower.toLocaleString()) || \"N/A\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 174,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Total Cash:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 43\n                                    }, this),\n                                    \"$\",\n                                    ((_accountInfo_totalCashValue = accountInfo.totalCashValue) === null || _accountInfo_totalCashValue === void 0 ? void 0 : _accountInfo_totalCashValue.toLocaleString()) || \"N/A\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                lineNumber: 163,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: \"#fffbeb\",\n                    border: \"1px solid #fed7aa\",\n                    padding: \"20px\",\n                    borderRadius: \"10px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        style: {\n                            color: \"#d97706\"\n                        },\n                        children: \"\\uD83D\\uDD27 Troubleshooting\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            marginBottom: \"15px\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"If connection fails:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                style: {\n                                    marginTop: \"5px\",\n                                    lineHeight: \"1.6\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"Make sure TWS Gateway is running and logged in\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"Check that API is enabled: Configure → Settings → API → Settings\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"Verify port number: 7497 for paper trading, 7496 for live\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: 'Ensure \"Enable ActiveX and Socket Clients\" is checked'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"Try restarting TWS Gateway\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                        lineNumber: 189,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Common Error Messages:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                style: {\n                                    marginTop: \"5px\",\n                                    lineHeight: \"1.6\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Connection timeout:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 17\n                                            }, this),\n                                            \" TWS Gateway not running or wrong port\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Connection refused:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 17\n                                            }, this),\n                                            \" API not enabled in settings\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Authentication failed:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                                lineNumber: 205,\n                                                columnNumber: 17\n                                            }, this),\n                                            \" Wrong client ID or account\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                        lineNumber: 200,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                lineNumber: 186,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: \"#1e40af\",\n                    color: \"white\",\n                    padding: \"20px\",\n                    borderRadius: \"10px\",\n                    marginTop: \"20px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        children: \"\\uD83D\\uDE80 Quick Setup Links\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"grid\",\n                            gridTemplateColumns: \"repeat(auto-fit, minmax(250px, 1fr))\",\n                            gap: \"15px\",\n                            marginTop: \"15px\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    background: \"rgba(255,255,255,0.1)\",\n                                    padding: \"15px\",\n                                    borderRadius: \"5px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"\\uD83D\\uDCF1 Client Portal\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 46\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"https://www.interactivebrokers.com/sso/Login\",\n                                        target: \"_blank\",\n                                        style: {\n                                            color: \"#93c5fd\"\n                                        },\n                                        children: \"Enable API Access\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    background: \"rgba(255,255,255,0.1)\",\n                                    padding: \"15px\",\n                                    borderRadius: \"5px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"\\uD83D\\uDCBB Download IB Gateway\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 52\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"https://www.interactivebrokers.com/en/trading/ib-api.php\",\n                                        target: \"_blank\",\n                                        style: {\n                                            color: \"#93c5fd\"\n                                        },\n                                        children: \"Get IB Gateway\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    background: \"rgba(255,255,255,0.1)\",\n                                    padding: \"15px\",\n                                    borderRadius: \"5px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"\\uD83D\\uDCDA Paper Trading\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 46\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        style: {\n                                            color: \"#93c5fd\"\n                                        },\n                                        children: \"Enable in Client Portal Settings\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 227,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                lineNumber: 210,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: \"#22c55e\",\n                    color: \"white\",\n                    padding: \"15px\",\n                    borderRadius: \"10px\",\n                    marginTop: \"20px\",\n                    textAlign: \"center\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                            children: \"\\uD83D\\uDE80 Once connected, you'll have access to:\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                            lineNumber: 233,\n                            columnNumber: 12\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                        lineNumber: 233,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"Professional options data • Real-time quotes • Advanced order types • Global markets\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                        lineNumber: 234,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                lineNumber: 232,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n        lineNumber: 67,\n        columnNumber: 5\n    }, this);\n}\n_s(IBKRTestPage, \"klk0zkIjsoflYpM8AFk84E2UPfQ=\");\n_c = IBKRTestPage;\nvar _c;\n$RefreshReg$(_c, \"IBKRTestPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/ibkr-test/page.tsx\n"));

/***/ })

});