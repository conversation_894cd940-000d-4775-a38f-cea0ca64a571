{"name": "dotenv", "version": "6.2.0", "description": "Loads environment variables from .env file", "main": "lib/main.js", "scripts": {"flow": "flow", "lint": "standard", "postlint": "standard-markdown", "pretest": "npm run lint", "test": "tap tests/*.js --100"}, "repository": {"type": "git", "url": "git://github.com/motdotla/dotenv.git"}, "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "readmeFilename": "README.md", "license": "BSD-2-<PERSON><PERSON>", "devDependencies": {"decache": "^4.5.0", "flow-bin": "^0.84.0", "sinon": "^6.3.5", "standard": "^12.0.1", "standard-markdown": "^5.0.1", "tap": "^12.0.1"}, "dependencies": {}, "engines": {"node": ">=6"}, "standard": {"ignore": ["flow-typed/"]}}