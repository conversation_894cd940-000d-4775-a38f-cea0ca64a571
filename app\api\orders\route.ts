// API Route for Order Management
// Handle placing orders and getting order history

import { NextRequest, NextResponse } from 'next/server';
import { MarketDataService } from '../../../lib/api/market-data-service';

const marketData = new MarketDataService();

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '50');
    
    // Get order history
    const orders = await marketData.getOrderHistory(limit);
    
    return NextResponse.json({
      success: true,
      data: orders,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error in orders GET API:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch order history',
        message: error instanceof Error ? error.message : 'Unknown error'
      }, 
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { symbol, quantity, side, type, limitPrice } = body;
    
    // Validate required fields
    if (!symbol || !quantity || !side) {
      return NextResponse.json({ 
        error: 'Missing required fields: symbol, quantity, side' 
      }, { status: 400 });
    }

    if (!['buy', 'sell'].includes(side)) {
      return NextResponse.json({ 
        error: 'Side must be "buy" or "sell"' 
      }, { status: 400 });
    }

    if (type && !['market', 'limit'].includes(type)) {
      return NextResponse.json({ 
        error: 'Type must be "market" or "limit"' 
      }, { status: 400 });
    }

    if (type === 'limit' && !limitPrice) {
      return NextResponse.json({ 
        error: 'Limit price required for limit orders' 
      }, { status: 400 });
    }

    // Place the order
    const order = await marketData.placeOrder(
      symbol.toUpperCase(),
      parseInt(quantity),
      side,
      type || 'market',
      limitPrice ? parseFloat(limitPrice) : undefined
    );
    
    return NextResponse.json({
      success: true,
      data: order,
      message: `${side.toUpperCase()} order for ${quantity} shares of ${symbol.toUpperCase()} placed successfully`,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error in orders POST API:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to place order',
        message: error instanceof Error ? error.message : 'Unknown error'
      }, 
      { status: 500 }
    );
  }
}
