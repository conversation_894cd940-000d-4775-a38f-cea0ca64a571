"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/ibkr/route";
exports.ids = ["app/api/ibkr/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("net");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fibkr%2Froute&page=%2Fapi%2Fibkr%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fibkr%2Froute.ts&appDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cericashacks%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cericashacks&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fibkr%2Froute&page=%2Fapi%2Fibkr%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fibkr%2Froute.ts&appDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cericashacks%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cericashacks&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_SJFit_Desktop_ericashacks_app_api_ibkr_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/ibkr/route.ts */ \"(rsc)/./app/api/ibkr/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/ibkr/route\",\n        pathname: \"/api/ibkr\",\n        filename: \"route\",\n        bundlePath: \"app/api/ibkr/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\api\\\\ibkr\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_SJFit_Desktop_ericashacks_app_api_ibkr_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/ibkr/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fibkr%2Froute&page=%2Fapi%2Fibkr%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fibkr%2Froute.ts&appDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cericashacks%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cericashacks&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/ibkr/route.ts":
/*!*******************************!*\
  !*** ./app/api/ibkr/route.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_api_ibkr_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../lib/api/ibkr-client */ \"(rsc)/./lib/api/ibkr-client.ts\");\n// API Route for Interactive Brokers Integration\n// Provides professional-grade options trading through IBKR\n\n\nlet ibkrClient = null;\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const action = searchParams.get(\"action\");\n        // Initialize IBKR client if not exists\n        if (!ibkrClient) {\n            ibkrClient = new _lib_api_ibkr_client__WEBPACK_IMPORTED_MODULE_1__.IBKRClient();\n        }\n        switch(action){\n            case \"connect\":\n                const connected = await ibkrClient.connect();\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: connected,\n                    message: connected ? \"Connected to IBKR successfully\" : \"Failed to connect to IBKR\",\n                    data: {\n                        connected\n                    }\n                });\n            case \"status\":\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: true,\n                    data: {\n                        connected: ibkrClient.isConnected(),\n                        message: ibkrClient.isConnected() ? \"IBKR Connected\" : \"IBKR Disconnected\"\n                    }\n                });\n            case \"account\":\n                if (!ibkrClient.isConnected()) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        success: false,\n                        error: \"Not connected to IBKR. Please connect first.\"\n                    }, {\n                        status: 400\n                    });\n                }\n                const accountInfo = await ibkrClient.getAccountInfo();\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: true,\n                    data: accountInfo\n                });\n            case \"positions\":\n                if (!ibkrClient.isConnected()) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        success: false,\n                        error: \"Not connected to IBKR. Please connect first.\"\n                    }, {\n                        status: 400\n                    });\n                }\n                const positions = await ibkrClient.getPositions();\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: true,\n                    data: positions\n                });\n            case \"quotes\":\n                const symbols = searchParams.get(\"symbols\");\n                if (!symbols) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        success: false,\n                        error: \"Symbols parameter required\"\n                    }, {\n                        status: 400\n                    });\n                }\n                if (!ibkrClient.isConnected()) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        success: false,\n                        error: \"Not connected to IBKR. Please connect first.\"\n                    }, {\n                        status: 400\n                    });\n                }\n                const symbolList = symbols.split(\",\").map((s)=>s.trim().toUpperCase());\n                const quotes = await ibkrClient.getStockQuotes(symbolList);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: true,\n                    data: quotes\n                });\n            case \"options\":\n                const symbol = searchParams.get(\"symbol\");\n                if (!symbol) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        success: false,\n                        error: \"Symbol parameter required\"\n                    }, {\n                        status: 400\n                    });\n                }\n                if (!ibkrClient.isConnected()) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        success: false,\n                        error: \"Not connected to IBKR. Please connect first.\"\n                    }, {\n                        status: 400\n                    });\n                }\n                const expiration = searchParams.get(\"expiration\") || undefined;\n                const optionsChain = await ibkrClient.getOptionsChain(symbol, expiration);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: true,\n                    data: optionsChain\n                });\n            default:\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: false,\n                    error: \"Invalid action. Use: connect, status, account, positions, quotes, options\"\n                }, {\n                    status: 400\n                });\n        }\n    } catch (error) {\n        console.error(\"Error in IBKR API:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"IBKR API error\",\n            message: error instanceof Error ? error.message : \"Unknown error\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { action } = body;\n        // Initialize IBKR client if not exists\n        if (!ibkrClient) {\n            ibkrClient = new _lib_api_ibkr_client__WEBPACK_IMPORTED_MODULE_1__.IBKRClient();\n        }\n        switch(action){\n            case \"place_order\":\n                const { symbol, strike, expiration, right, orderAction, quantity, orderType, limitPrice } = body;\n                if (!symbol || !strike || !expiration || !right || !orderAction || !quantity) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        success: false,\n                        error: \"Missing required order parameters\"\n                    }, {\n                        status: 400\n                    });\n                }\n                if (!ibkrClient.isConnected()) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        success: false,\n                        error: \"Not connected to IBKR. Please connect first.\"\n                    }, {\n                        status: 400\n                    });\n                }\n                const order = await ibkrClient.placeOptionsOrder(symbol, strike, expiration, right, orderAction, quantity, orderType || \"LMT\", limitPrice);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: true,\n                    data: order,\n                    message: `${orderAction} order placed successfully`\n                });\n            case \"cancel_order\":\n                const { orderId } = body;\n                if (!orderId) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        success: false,\n                        error: \"Order ID required\"\n                    }, {\n                        status: 400\n                    });\n                }\n                if (!ibkrClient.isConnected()) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        success: false,\n                        error: \"Not connected to IBKR. Please connect first.\"\n                    }, {\n                        status: 400\n                    });\n                }\n                const cancelled = await ibkrClient.cancelOrder(orderId);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: cancelled,\n                    message: cancelled ? \"Order cancelled successfully\" : \"Failed to cancel order\"\n                });\n            case \"order_status\":\n                const { orderIdToCheck } = body;\n                if (!orderIdToCheck) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        success: false,\n                        error: \"Order ID required\"\n                    }, {\n                        status: 400\n                    });\n                }\n                if (!ibkrClient.isConnected()) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        success: false,\n                        error: \"Not connected to IBKR. Please connect first.\"\n                    }, {\n                        status: 400\n                    });\n                }\n                const status = await ibkrClient.getOrderStatus(orderIdToCheck);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: true,\n                    data: status\n                });\n            default:\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: false,\n                    error: \"Invalid action. Use: place_order, cancel_order, order_status\"\n                }, {\n                    status: 400\n                });\n        }\n    } catch (error) {\n        console.error(\"Error in IBKR POST API:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"IBKR API error\",\n            message: error instanceof Error ? error.message : \"Unknown error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/ibkr/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/api/ibkr-client.ts":
/*!********************************!*\
  !*** ./lib/api/ibkr-client.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   IBKRClient: () => (/* binding */ IBKRClient)\n/* harmony export */ });\n/* harmony import */ var events__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! events */ \"events\");\n/* harmony import */ var events__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(events__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var net__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! net */ \"net\");\n/* harmony import */ var net__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(net__WEBPACK_IMPORTED_MODULE_1__);\n// Interactive Brokers API Client\n// Provides professional-grade options trading and market data\n\n\nclass IBKRClient extends events__WEBPACK_IMPORTED_MODULE_0__.EventEmitter {\n    constructor(){\n        super();\n        this.connected = false;\n        this.socket = null;\n        this.nextOrderId = 1;\n        this.host = process.env.IBKR_HOST || \"127.0.0.1\";\n        this.port = parseInt(process.env.IBKR_PORT || \"7497\");\n        this.clientId = parseInt(process.env.IBKR_CLIENT_ID || \"1\");\n        this.accountId = process.env.IBKR_ACCOUNT_ID || \"\";\n    }\n    /**\n   * Connect to Interactive Brokers TWS or IB Gateway\n   * Simple explanation: Connect to your IBKR trading platform\n   */ async connect() {\n        try {\n            console.log(`🔌 Connecting to IBKR at ${this.host}:${this.port}`);\n            return new Promise((resolve, reject)=>{\n                this.socket = new net__WEBPACK_IMPORTED_MODULE_1__.Socket();\n                this.socket.on(\"connect\", ()=>{\n                    console.log(\"✅ Connected to IBKR TWS/Gateway!\");\n                    this.connected = true;\n                    // Send handshake message\n                    this.sendHandshake();\n                    this.emit(\"connected\");\n                    resolve(true);\n                });\n                this.socket.on(\"data\", (data)=>{\n                    this.handleIncomingData(data);\n                });\n                this.socket.on(\"error\", (error)=>{\n                    console.error(\"❌ IBKR Connection Error:\", error.message);\n                    this.connected = false;\n                    this.emit(\"error\", error);\n                    reject(error);\n                });\n                this.socket.on(\"close\", ()=>{\n                    console.log(\"\\uD83D\\uDD0C IBKR Connection Closed\");\n                    this.connected = false;\n                    this.emit(\"disconnected\");\n                });\n                // Connect to TWS/Gateway\n                this.socket.connect(this.port, this.host);\n                // Timeout after 10 seconds\n                setTimeout(()=>{\n                    if (!this.connected) {\n                        reject(new Error(\"Connection timeout - Make sure TWS/Gateway is running\"));\n                    }\n                }, 10000);\n            });\n        } catch (error) {\n            console.error(\"Failed to connect to IBKR:\", error);\n            this.connected = false;\n            return false;\n        }\n    }\n    /**\n   * Disconnect from IBKR\n   * Simple explanation: Close connection to trading platform\n   */ async disconnect() {\n        this.connected = false;\n        this.emit(\"disconnected\");\n    }\n    /**\n   * Get real-time stock quotes\n   * Simple explanation: Get live stock prices from IBKR\n   */ async getStockQuotes(symbols) {\n        if (!this.connected) {\n            throw new Error(\"Not connected to IBKR\");\n        }\n        try {\n            const stocks = {};\n            // In real implementation, this would request market data from IBKR\n            for (const symbol of symbols){\n                // Simulate getting real-time data\n                stocks[symbol] = {\n                    symbol,\n                    name: `${symbol} Inc.`,\n                    price: 100 + Math.random() * 200,\n                    change: (Math.random() - 0.5) * 10,\n                    changePercent: (Math.random() - 0.5) * 5,\n                    volume: Math.floor(Math.random() * 1000000),\n                    marketCap: 0,\n                    sector: \"Technology\"\n                };\n            }\n            return stocks;\n        } catch (error) {\n            console.error(\"Error getting stock quotes from IBKR:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get comprehensive options chain\n   * Simple explanation: Get all available option contracts for a stock\n   */ async getOptionsChain(symbol, expiration) {\n        if (!this.connected) {\n            throw new Error(\"Not connected to IBKR\");\n        }\n        try {\n            // In real implementation, this would request options data from IBKR\n            const chains = [];\n            // Simulate options chain data\n            const expirations = [\n                \"2024-02-16\",\n                \"2024-03-15\",\n                \"2024-04-19\"\n            ];\n            const currentPrice = 180; // Simulated current stock price\n            for (const exp of expirations){\n                const calls = [];\n                const puts = [];\n                // Generate strikes around current price\n                for(let strike = currentPrice - 20; strike <= currentPrice + 20; strike += 5){\n                    const daysToExp = Math.floor((new Date(exp).getTime() - Date.now()) / (1000 * 60 * 60 * 24));\n                    // Simulate call option\n                    calls.push({\n                        strike,\n                        bid: Math.max(0.05, Math.random() * 10),\n                        ask: Math.max(0.10, Math.random() * 12),\n                        last: Math.max(0.07, Math.random() * 11),\n                        volume: Math.floor(Math.random() * 1000),\n                        openInterest: Math.floor(Math.random() * 5000),\n                        impliedVolatility: 0.15 + Math.random() * 0.3,\n                        delta: strike < currentPrice ? 0.3 + Math.random() * 0.4 : 0.1 + Math.random() * 0.3,\n                        gamma: 0.01 + Math.random() * 0.05,\n                        theta: -0.01 - Math.random() * 0.1,\n                        vega: 0.05 + Math.random() * 0.2,\n                        rho: 0.01 + Math.random() * 0.05,\n                        intrinsicValue: Math.max(0, currentPrice - strike),\n                        timeValue: Math.max(0.05, Math.random() * 5),\n                        daysToExpiration: daysToExp\n                    });\n                    // Simulate put option\n                    puts.push({\n                        strike,\n                        bid: Math.max(0.05, Math.random() * 10),\n                        ask: Math.max(0.10, Math.random() * 12),\n                        last: Math.max(0.07, Math.random() * 11),\n                        volume: Math.floor(Math.random() * 1000),\n                        openInterest: Math.floor(Math.random() * 5000),\n                        impliedVolatility: 0.15 + Math.random() * 0.3,\n                        delta: strike > currentPrice ? -0.3 - Math.random() * 0.4 : -0.1 - Math.random() * 0.3,\n                        gamma: 0.01 + Math.random() * 0.05,\n                        theta: -0.01 - Math.random() * 0.1,\n                        vega: 0.05 + Math.random() * 0.2,\n                        rho: -0.01 - Math.random() * 0.05,\n                        intrinsicValue: Math.max(0, strike - currentPrice),\n                        timeValue: Math.max(0.05, Math.random() * 5),\n                        daysToExpiration: daysToExp\n                    });\n                }\n                chains.push({\n                    symbol,\n                    expiration: exp,\n                    calls: calls.sort((a, b)=>a.strike - b.strike),\n                    puts: puts.sort((a, b)=>a.strike - b.strike)\n                });\n            }\n            return chains;\n        } catch (error) {\n            console.error(\"Error getting options chain from IBKR:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get account information\n   * Simple explanation: Check your account balance and buying power\n   */ async getAccountInfo() {\n        if (!this.connected) {\n            throw new Error(\"Not connected to IBKR\");\n        }\n        try {\n            // In real implementation, this would request account data from IBKR\n            return {\n                totalCashValue: 50000 + Math.random() * 10000,\n                netLiquidation: 52450 + Math.random() * 5000,\n                availableFunds: 48000 + Math.random() * 8000,\n                buyingPower: 96000 + Math.random() * 16000,\n                grossPositionValue: 25000 + Math.random() * 15000\n            };\n        } catch (error) {\n            console.error(\"Error getting account info from IBKR:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get current positions\n   * Simple explanation: See all your current stock and option positions\n   */ async getPositions() {\n        if (!this.connected) {\n            throw new Error(\"Not connected to IBKR\");\n        }\n        try {\n            // In real implementation, this would request positions from IBKR\n            return [\n                {\n                    symbol: \"AAPL\",\n                    position: 100,\n                    marketPrice: 182.50,\n                    marketValue: 18250,\n                    averageCost: 175.00,\n                    unrealizedPnL: 750,\n                    realizedPnL: 0,\n                    secType: \"STK\"\n                },\n                {\n                    symbol: \"AAPL   240216C00185000\",\n                    position: -1,\n                    marketPrice: 2.20,\n                    marketValue: -220,\n                    averageCost: 2.50,\n                    unrealizedPnL: 30,\n                    realizedPnL: 0,\n                    secType: \"OPT\"\n                }\n            ];\n        } catch (error) {\n            console.error(\"Error getting positions from IBKR:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Place an options order\n   * Simple explanation: Execute options trades through IBKR\n   */ async placeOptionsOrder(symbol, strike, expiration, right, action, quantity, orderType = \"LMT\", limitPrice) {\n        if (!this.connected) {\n            throw new Error(\"Not connected to IBKR\");\n        }\n        try {\n            // In real implementation, this would place the order through IBKR\n            const orderId = Math.floor(Math.random() * 1000000);\n            console.log(`Placing ${action} order for ${quantity} ${symbol} ${expiration} ${strike} ${right}`);\n            return {\n                orderId,\n                symbol,\n                strike,\n                expiration,\n                right,\n                action,\n                quantity,\n                orderType,\n                limitPrice,\n                status: \"SUBMITTED\",\n                timestamp: new Date()\n            };\n        } catch (error) {\n            console.error(\"Error placing options order:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get order status\n   * Simple explanation: Check if your orders were filled\n   */ async getOrderStatus(orderId) {\n        if (!this.connected) {\n            throw new Error(\"Not connected to IBKR\");\n        }\n        try {\n            // In real implementation, this would check order status\n            return {\n                orderId,\n                status: \"FILLED\",\n                filled: true,\n                remaining: 0,\n                avgFillPrice: 2.25,\n                commission: 1.25\n            };\n        } catch (error) {\n            console.error(\"Error getting order status:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Cancel an order\n   * Simple explanation: Cancel a pending order\n   */ async cancelOrder(orderId) {\n        if (!this.connected) {\n            throw new Error(\"Not connected to IBKR\");\n        }\n        try {\n            // In real implementation, this would cancel the order\n            console.log(`Cancelling order ${orderId}`);\n            return true;\n        } catch (error) {\n            console.error(\"Error cancelling order:\", error);\n            return false;\n        }\n    }\n    /**\n   * Check if connected to IBKR\n   * Simple explanation: See if your connection is working\n   */ isConnected() {\n        return this.connected;\n    }\n    /**\n   * Send handshake message to IBKR\n   * Simple explanation: Introduce yourself to the IBKR system\n   */ sendHandshake() {\n        if (!this.socket) return;\n        // IBKR API handshake protocol\n        const version = \"API\\0\";\n        const minVersion = \"9.72\";\n        const maxVersion = \"9.72\";\n        const handshake = `${version}v${minVersion}..${maxVersion}`;\n        this.socket.write(handshake);\n        // Send client ID\n        setTimeout(()=>{\n            if (this.socket) {\n                this.socket.write(Buffer.from([\n                    0,\n                    0,\n                    0,\n                    4,\n                    0,\n                    0,\n                    0,\n                    this.clientId\n                ]));\n            }\n        }, 100);\n    }\n    /**\n   * Handle incoming data from IBKR\n   * Simple explanation: Process messages from IBKR\n   */ handleIncomingData(data) {\n        try {\n            // Parse IBKR messages\n            console.log(\"\\uD83D\\uDCE8 Received data from IBKR:\", data.length, \"bytes\");\n            // In a full implementation, you'd parse the binary protocol here\n            // For now, we'll just acknowledge the data\n            this.emit(\"data\", data);\n        } catch (error) {\n            console.error(\"Error handling IBKR data:\", error);\n        }\n    }\n    /**\n   * Send a message to IBKR\n   * Simple explanation: Send commands to IBKR\n   */ sendMessage(message) {\n        if (this.socket && this.connected) {\n            this.socket.write(message);\n        } else {\n            throw new Error(\"Not connected to IBKR\");\n        }\n    }\n    /**\n   * Test the connection\n   * Simple explanation: Check if IBKR is responding\n   */ async testConnection() {\n        try {\n            if (!this.connected) {\n                return false;\n            }\n            // Send a simple request to test connection\n            console.log(\"\\uD83E\\uDDEA Testing IBKR connection...\");\n            // In a real implementation, you'd send a specific test message\n            // For now, just check if socket is still connected\n            return this.socket !== null && !this.socket.destroyed;\n        } catch (error) {\n            console.error(\"Connection test failed:\", error);\n            return false;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/api/ibkr-client.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fibkr%2Froute&page=%2Fapi%2Fibkr%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fibkr%2Froute.ts&appDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cericashacks%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cericashacks&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();