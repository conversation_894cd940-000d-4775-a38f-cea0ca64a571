"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ HomePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction HomePage() {\n    _s();\n    const [selectedStrategy, setSelectedStrategy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [liveData, setLiveData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleStrategyClick = async (strategy)=>{\n        setSelectedStrategy(strategy);\n        setLoading(true);\n        try {\n            // Get live market data for actionable trades\n            const response = await fetch(\"/api/stocks?symbols=AAPL,MSFT,GOOGL,AMZN,TSLA,META,NVDA,SPY,QQQ,IWM\");\n            const result = await response.json();\n            if (result.success) {\n                setLiveData(result.data);\n            }\n        } catch (error) {\n            console.error(\"Error fetching live data:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: \"20px\",\n            fontFamily: \"Arial\",\n            maxWidth: \"1200px\",\n            margin: \"0 auto\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                style: {\n                    color: \"#0ea5e9\",\n                    marginBottom: \"20px\"\n                },\n                children: \"\\uD83D\\uDE80 Enhanced Options Trading Program\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: \"#f0f9ff\",\n                    padding: \"20px\",\n                    borderRadius: \"10px\",\n                    marginBottom: \"20px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        children: \"\\uD83D\\uDCB0 READY TO MAKE MONEY!\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"Your live trading system is connected and ready to generate income.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: \"grid\",\n                    gridTemplateColumns: \"repeat(auto-fit, minmax(250px, 1fr))\",\n                    gap: \"20px\",\n                    marginBottom: \"30px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: \"white\",\n                            padding: \"20px\",\n                            borderRadius: \"10px\",\n                            boxShadow: \"0 2px 4px rgba(0,0,0,0.1)\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    color: \"#22c55e\"\n                                },\n                                children: \"\\uD83D\\uDCB5 Portfolio Value\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 41,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    fontSize: \"24px\",\n                                    fontWeight: \"bold\"\n                                },\n                                children: \"$50,000\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 42,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: \"white\",\n                            padding: \"20px\",\n                            borderRadius: \"10px\",\n                            boxShadow: \"0 2px 4px rgba(0,0,0,0.1)\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    color: \"#22c55e\"\n                                },\n                                children: \"\\uD83D\\uDCC8 Total Profit\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 46,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    fontSize: \"24px\",\n                                    fontWeight: \"bold\",\n                                    color: \"#22c55e\"\n                                },\n                                children: \"+$2,450\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 47,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: \"white\",\n                            padding: \"20px\",\n                            borderRadius: \"10px\",\n                            boxShadow: \"0 2px 4px rgba(0,0,0,0.1)\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    color: \"#0ea5e9\"\n                                },\n                                children: \"\\uD83C\\uDFAF Active Trades\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 51,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    fontSize: \"24px\",\n                                    fontWeight: \"bold\"\n                                },\n                                children: \"8\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 52,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: \"white\",\n                            padding: \"20px\",\n                            borderRadius: \"10px\",\n                            boxShadow: \"0 2px 4px rgba(0,0,0,0.1)\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    color: \"#f59e0b\"\n                                },\n                                children: \"\\uD83D\\uDEE1️ Risk Level\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    fontSize: \"18px\",\n                                    fontWeight: \"bold\",\n                                    color: \"#f59e0b\"\n                                },\n                                children: \"MEDIUM\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 57,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: \"white\",\n                    padding: \"30px\",\n                    borderRadius: \"10px\",\n                    boxShadow: \"0 2px 4px rgba(0,0,0,0.1)\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            marginBottom: \"20px\"\n                        },\n                        children: \"\\uD83C\\uDFAF Money-Making Strategies\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"grid\",\n                            gap: \"20px\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    border: \"2px solid #22c55e\",\n                                    borderRadius: \"10px\",\n                                    padding: \"20px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        style: {\n                                            color: \"#22c55e\"\n                                        },\n                                        children: \"\\uD83D\\uDCB0 Income Collection (Covered Calls)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"What it does:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 67,\n                                                columnNumber: 16\n                                            }, this),\n                                            \" Collect $100-500+ per month from stocks you own\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 67,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"How it works:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 68,\n                                                columnNumber: 16\n                                            }, this),\n                                            \" Sell call options on your stocks - get paid immediately\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Success rate:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 69,\n                                                columnNumber: 16\n                                            }, this),\n                                            \" 85% profitable\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleStrategyClick(\"Income Collection\"),\n                                        style: {\n                                            background: \"#22c55e\",\n                                            color: \"white\",\n                                            padding: \"10px 20px\",\n                                            border: \"none\",\n                                            borderRadius: \"5px\",\n                                            cursor: \"pointer\",\n                                            fontSize: \"16px\",\n                                            fontWeight: \"bold\"\n                                        },\n                                        onMouseOver: (e)=>e.target.style.background = \"#16a34a\",\n                                        onMouseOut: (e)=>e.target.style.background = \"#22c55e\",\n                                        children: \"\\uD83D\\uDCB0 START MAKING MONEY NOW →\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 70,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    border: \"2px solid #8b5cf6\",\n                                    borderRadius: \"10px\",\n                                    padding: \"20px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        style: {\n                                            color: \"#8b5cf6\"\n                                        },\n                                        children: \"\\uD83C\\uDFE6 Poor Man's Covered Call (PMCC)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"What it does:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 82,\n                                                columnNumber: 16\n                                            }, this),\n                                            \" Get covered call income with 90% less capital\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 82,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"How it works:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 83,\n                                                columnNumber: 16\n                                            }, this),\n                                            \" Buy LEAPS, sell short calls against it\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Example:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 84,\n                                                columnNumber: 16\n                                            }, this),\n                                            \" Control $18,000 of AAPL stock with just $1,800\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 84,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleStrategyClick(\"Poor Mans Covered Call\"),\n                                        style: {\n                                            background: \"#8b5cf6\",\n                                            color: \"white\",\n                                            padding: \"10px 20px\",\n                                            border: \"none\",\n                                            borderRadius: \"5px\",\n                                            cursor: \"pointer\",\n                                            fontSize: \"16px\",\n                                            fontWeight: \"bold\"\n                                        },\n                                        onMouseOver: (e)=>e.target.style.background = \"#7c3aed\",\n                                        onMouseOut: (e)=>e.target.style.background = \"#8b5cf6\",\n                                        children: \"\\uD83C\\uDFE6 START PMCC NOW →\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    border: \"2px solid #0ea5e9\",\n                                    borderRadius: \"10px\",\n                                    padding: \"20px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        style: {\n                                            color: \"#0ea5e9\"\n                                        },\n                                        children: \"\\uD83C\\uDFB2 Bear Call Spread (Range Betting)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"What it does:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 97,\n                                                columnNumber: 16\n                                            }, this),\n                                            \" Bet stocks stay below a level - collect money if right\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Success rate:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 98,\n                                                columnNumber: 16\n                                            }, this),\n                                            \" 70-80% win rate\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Example:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 99,\n                                                columnNumber: 16\n                                            }, this),\n                                            \" MSFT at $350? Bet it stays below $360, collect $240\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleStrategyClick(\"Bear Call Spread\"),\n                                        style: {\n                                            background: \"#0ea5e9\",\n                                            color: \"white\",\n                                            padding: \"10px 20px\",\n                                            border: \"none\",\n                                            borderRadius: \"5px\",\n                                            cursor: \"pointer\",\n                                            fontSize: \"16px\",\n                                            fontWeight: \"bold\"\n                                        },\n                                        onMouseOver: (e)=>e.target.style.background = \"#0284c7\",\n                                        onMouseOut: (e)=>e.target.style.background = \"#0ea5e9\",\n                                        children: \"\\uD83C\\uDFB2 FIND OPPORTUNITIES NOW →\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    border: \"2px solid #f59e0b\",\n                                    borderRadius: \"10px\",\n                                    padding: \"20px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        style: {\n                                            color: \"#f59e0b\"\n                                        },\n                                        children: \"\\uD83D\\uDD04 The Wheel (Consistent Income)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"What it does:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 112,\n                                                columnNumber: 16\n                                            }, this),\n                                            \" Generate 12-20% annual returns consistently\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"How it works:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 113,\n                                                columnNumber: 16\n                                            }, this),\n                                            \" Cycle between selling puts and calls automatically\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Best part:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 114,\n                                                columnNumber: 16\n                                            }, this),\n                                            \" Works in any market condition\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleStrategyClick(\"The Wheel\"),\n                                        style: {\n                                            background: \"#f59e0b\",\n                                            color: \"white\",\n                                            padding: \"10px 20px\",\n                                            border: \"none\",\n                                            borderRadius: \"5px\",\n                                            cursor: \"pointer\",\n                                            fontSize: \"16px\",\n                                            fontWeight: \"bold\"\n                                        },\n                                        onMouseOver: (e)=>e.target.style.background = \"#d97706\",\n                                        onMouseOut: (e)=>e.target.style.background = \"#f59e0b\",\n                                        children: \"\\uD83D\\uDD04 START THE WHEEL NOW →\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 110,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    border: \"2px solid #ef4444\",\n                                    borderRadius: \"10px\",\n                                    padding: \"20px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        style: {\n                                            color: \"#ef4444\"\n                                        },\n                                        children: \"\\uD83D\\uDCB8 Cash-Secured Puts (Buy Stocks Cheap)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"What it does:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 16\n                                            }, this),\n                                            \" Get paid to buy stocks at discount prices\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"How it works:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 128,\n                                                columnNumber: 16\n                                            }, this),\n                                            \" Sell puts, collect premium, maybe get cheap stock\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Example:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 129,\n                                                columnNumber: 16\n                                            }, this),\n                                            \" AAPL at $180? Get paid $300 to buy it at $175\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleStrategyClick(\"Cash Secured Puts\"),\n                                        style: {\n                                            background: \"#ef4444\",\n                                            color: \"white\",\n                                            padding: \"10px 20px\",\n                                            border: \"none\",\n                                            borderRadius: \"5px\",\n                                            cursor: \"pointer\",\n                                            fontSize: \"16px\",\n                                            fontWeight: \"bold\"\n                                        },\n                                        onMouseOver: (e)=>e.target.style.background = \"#dc2626\",\n                                        onMouseOut: (e)=>e.target.style.background = \"#ef4444\",\n                                        children: \"\\uD83D\\uDCB8 GET PAID TO BUY →\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    border: \"2px solid #10b981\",\n                                    borderRadius: \"10px\",\n                                    padding: \"20px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        style: {\n                                            color: \"#10b981\"\n                                        },\n                                        children: \"⏰ Calendar Spreads (Time Decay)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"What it does:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 142,\n                                                columnNumber: 16\n                                            }, this),\n                                            \" Profit from time passing on sideways stocks\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"How it works:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 16\n                                            }, this),\n                                            \" Sell short-term, buy long-term at same strike\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Best for:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 144,\n                                                columnNumber: 16\n                                            }, this),\n                                            \" Stocks stuck in a range for weeks\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleStrategyClick(\"Calendar Spreads\"),\n                                        style: {\n                                            background: \"#10b981\",\n                                            color: \"white\",\n                                            padding: \"10px 20px\",\n                                            border: \"none\",\n                                            borderRadius: \"5px\",\n                                            cursor: \"pointer\",\n                                            fontSize: \"16px\",\n                                            fontWeight: \"bold\"\n                                        },\n                                        onMouseOver: (e)=>e.target.style.background = \"#059669\",\n                                        onMouseOut: (e)=>e.target.style.background = \"#10b981\",\n                                        children: \"⏰ PROFIT FROM TIME →\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    border: \"2px solid #6366f1\",\n                                    borderRadius: \"10px\",\n                                    padding: \"20px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        style: {\n                                            color: \"#6366f1\"\n                                        },\n                                        children: \"\\uD83D\\uDE80 LEAPS (Long-Term Leverage)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"What it does:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 16\n                                            }, this),\n                                            \" Control $15,000 of stock with $1,500\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"How it works:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 16\n                                            }, this),\n                                            \" Buy long-term options for massive leverage\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Example:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 159,\n                                                columnNumber: 16\n                                            }, this),\n                                            \" 10x leverage on your favorite stocks\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleStrategyClick(\"LEAPS\"),\n                                        style: {\n                                            background: \"#6366f1\",\n                                            color: \"white\",\n                                            padding: \"10px 20px\",\n                                            border: \"none\",\n                                            borderRadius: \"5px\",\n                                            cursor: \"pointer\",\n                                            fontSize: \"16px\",\n                                            fontWeight: \"bold\"\n                                        },\n                                        onMouseOver: (e)=>e.target.style.background = \"#4f46e5\",\n                                        onMouseOut: (e)=>e.target.style.background = \"#6366f1\",\n                                        children: \"\\uD83D\\uDE80 GET LEVERAGE NOW →\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, this),\n            selectedStrategy && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: \"#f0f9ff\",\n                    border: \"2px solid #0ea5e9\",\n                    borderRadius: \"10px\",\n                    padding: \"30px\",\n                    marginTop: \"20px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            color: \"#0ea5e9\",\n                            marginBottom: \"20px\"\n                        },\n                        children: [\n                            \"\\uD83C\\uDFAF \",\n                            selectedStrategy,\n                            \" - READY TO EXECUTE!\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 11\n                    }, this),\n                    selectedStrategy === \"Income Collection\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    color: \"#22c55e\"\n                                },\n                                children: \"\\uD83D\\uDCB0 STEP-BY-STEP COVERED CALLS:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                style: {\n                                    fontSize: \"16px\",\n                                    lineHeight: \"1.6\",\n                                    marginLeft: \"20px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Own 100 shares\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" of a stock you like (AAPL, MSFT, GOOGL)\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Sell a call option\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 181,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" above current price - collect $100-500 immediately\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 181,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Set strike 5-10% above\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 182,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" current price for safety\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"If stock stays below strike\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" - keep all premium and repeat\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"If stock goes above strike\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" - sell shares for profit + keep premium\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    background: \"#22c55e\",\n                                    color: \"white\",\n                                    padding: \"15px\",\n                                    borderRadius: \"5px\",\n                                    marginTop: \"15px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"\\uD83D\\uDE80 EXAMPLE:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 17\n                                    }, this),\n                                    \" Own AAPL at $180, sell $190 call for $275. If AAPL stays below $190, keep $275. If above, sell at $190 + keep $275 = $1,275 profit!\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 13\n                    }, this),\n                    selectedStrategy === \"Poor Mans Covered Call\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    color: \"#8b5cf6\"\n                                },\n                                children: \"\\uD83C\\uDFE6 STEP-BY-STEP POOR MAN'S COVERED CALL:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                style: {\n                                    fontSize: \"16px\",\n                                    lineHeight: \"1.6\",\n                                    marginLeft: \"20px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Buy a LEAPS call\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" (1+ year expiration) deep in-the-money\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Sell short-term calls\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" against it monthly for income\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Use 90% less capital\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 198,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" than buying 100 shares\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Collect monthly premiums\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" while LEAPS appreciates\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Roll or close\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" if short call gets threatened\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    background: \"#8b5cf6\",\n                                    color: \"white\",\n                                    padding: \"15px\",\n                                    borderRadius: \"5px\",\n                                    marginTop: \"15px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"\\uD83D\\uDE80 EXAMPLE:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 17\n                                    }, this),\n                                    \" Instead of buying $18,000 of AAPL stock, buy $1,800 LEAPS. Sell monthly calls for $200-400. Same income, 90% less capital!\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 13\n                    }, this),\n                    selectedStrategy === \"Bear Call Spread\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    color: \"#0ea5e9\"\n                                },\n                                children: \"\\uD83C\\uDFB2 STEP-BY-STEP BEAR CALL SPREAD:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                style: {\n                                    fontSize: \"16px\",\n                                    lineHeight: \"1.6\",\n                                    marginLeft: \"20px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Pick a stock near resistance\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 212,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" that you think won't break higher\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 212,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Sell a call\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 213,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" at the resistance level (collect premium)\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Buy a higher call\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 214,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" for protection (spend less premium)\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Keep the difference\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" if stock stays below your sold strike\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Max profit in 2-4 weeks\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 216,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" if you're right about direction\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    background: \"#0ea5e9\",\n                                    color: \"white\",\n                                    padding: \"15px\",\n                                    borderRadius: \"5px\",\n                                    marginTop: \"15px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"\\uD83D\\uDE80 EXAMPLE:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 17\n                                    }, this),\n                                    \" MSFT at $350, sell $360 call for $400, buy $370 call for $150. Net credit $250. If MSFT stays below $360, keep all $250!\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 13\n                    }, this),\n                    selectedStrategy === \"Range Betting\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    color: \"#0ea5e9\"\n                                },\n                                children: \"\\uD83C\\uDFB2 STEP-BY-STEP RANGE BETTING:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 226,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                style: {\n                                    fontSize: \"16px\",\n                                    lineHeight: \"1.6\",\n                                    marginLeft: \"20px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Find a stock trading in a range\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" - like MSFT between $350-380\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Sell options at both ends\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 229,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" - collect premium betting it stays in range\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Collect $150-300+ immediately\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" - money hits your account\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 230,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"If stock stays in range\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 231,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" - keep all the money (70-80% of the time)\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"If stock breaks out\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 232,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" - limited loss, defined risk\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    background: \"#0ea5e9\",\n                                    color: \"white\",\n                                    padding: \"15px\",\n                                    borderRadius: \"5px\",\n                                    marginTop: \"15px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"\\uD83D\\uDE80 NEXT STEP:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 17\n                                    }, this),\n                                    \" Check your trading platform for current range-bound stocks!\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 234,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                        lineNumber: 225,\n                        columnNumber: 13\n                    }, this),\n                    selectedStrategy === \"The Wheel\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    color: \"#f59e0b\"\n                                },\n                                children: \"\\uD83D\\uDD04 STEP-BY-STEP WHEEL STRATEGY:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                style: {\n                                    fontSize: \"16px\",\n                                    lineHeight: \"1.6\",\n                                    marginLeft: \"20px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Start by selling puts\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 244,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" on stocks you want to own at lower prices\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 244,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Collect premium immediately\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 245,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" - $100-400+ per month\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"If assigned stock\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" - now you own it at a discount\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Sell calls on the stock\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 247,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" - collect more premium\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Repeat the cycle\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 248,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" - consistent 12-20% annual returns\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    background: \"#f59e0b\",\n                                    color: \"white\",\n                                    padding: \"15px\",\n                                    borderRadius: \"5px\",\n                                    marginTop: \"15px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"\\uD83D\\uDE80 EXAMPLE:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 17\n                                    }, this),\n                                    \" Sell AAPL $175 put for $300. If assigned, own AAPL at $175. Sell $185 call for $250. If called away, profit $1,550 + premiums!\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                        lineNumber: 241,\n                        columnNumber: 13\n                    }, this),\n                    selectedStrategy === \"Cash Secured Puts\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    color: \"#ef4444\"\n                                },\n                                children: \"\\uD83D\\uDCB8 STEP-BY-STEP CASH-SECURED PUTS:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 258,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                style: {\n                                    fontSize: \"16px\",\n                                    lineHeight: \"1.6\",\n                                    marginLeft: \"20px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Pick a stock you want to own\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 260,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" at a lower price\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 260,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Sell a put option\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 261,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" below current price - collect premium\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Set aside cash\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 262,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" to buy 100 shares if assigned\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"If stock stays above strike\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" - keep premium, repeat\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 263,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"If stock drops below\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 264,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" - buy stock at discount + keep premium\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 264,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 259,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    background: \"#ef4444\",\n                                    color: \"white\",\n                                    padding: \"15px\",\n                                    borderRadius: \"5px\",\n                                    marginTop: \"15px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"\\uD83D\\uDE80 EXAMPLE:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 17\n                                    }, this),\n                                    \" AAPL at $180, sell $175 put for $300. If AAPL stays above $175, keep $300. If below, buy AAPL at $175 (5% discount) + keep $300!\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 266,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                        lineNumber: 257,\n                        columnNumber: 13\n                    }, this),\n                    selectedStrategy === \"Calendar Spreads\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    color: \"#10b981\"\n                                },\n                                children: \"⏰ STEP-BY-STEP CALENDAR SPREADS:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 274,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                style: {\n                                    fontSize: \"16px\",\n                                    lineHeight: \"1.6\",\n                                    marginLeft: \"20px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Find a sideways stock\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" trading in a tight range\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 276,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Sell a short-term option\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 277,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" at current price (collect premium)\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 277,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Buy a long-term option\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 278,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" at same strike (protection)\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Profit from time decay\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" as short option expires faster\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 279,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Repeat monthly\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" for consistent income\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 280,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 275,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    background: \"#10b981\",\n                                    color: \"white\",\n                                    padding: \"15px\",\n                                    borderRadius: \"5px\",\n                                    marginTop: \"15px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"\\uD83D\\uDE80 EXAMPLE:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 17\n                                    }, this),\n                                    \" MSFT stuck at $350. Sell 2-week $350 call for $200, buy 3-month $350 call for $150. Net $50 profit if MSFT stays near $350!\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 282,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                        lineNumber: 273,\n                        columnNumber: 13\n                    }, this),\n                    selectedStrategy === \"LEAPS\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    color: \"#6366f1\"\n                                },\n                                children: \"\\uD83D\\uDE80 STEP-BY-STEP LEAPS STRATEGY:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 290,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                style: {\n                                    fontSize: \"16px\",\n                                    lineHeight: \"1.6\",\n                                    marginLeft: \"20px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Pick your favorite stock\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 292,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" for long-term growth\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 292,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Buy LEAPS calls\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 293,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" (1-2 years out) for massive leverage\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 293,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Use 10% of capital\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 294,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" vs buying shares outright\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Get 10x exposure\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" to stock movements\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 295,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Sell anytime in profit\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" or hold for big gains\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 296,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 291,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    background: \"#6366f1\",\n                                    color: \"white\",\n                                    padding: \"15px\",\n                                    borderRadius: \"5px\",\n                                    marginTop: \"15px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"\\uD83D\\uDE80 EXAMPLE:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 17\n                                    }, this),\n                                    \" Instead of buying $15,000 of AAPL stock, buy $1,500 LEAPS. If AAPL goes up 20%, your LEAPS might go up 200%! Same upside, 90% less capital.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 298,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                        lineNumber: 289,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setSelectedStrategy(\"\"),\n                        style: {\n                            background: \"#6b7280\",\n                            color: \"white\",\n                            padding: \"10px 20px\",\n                            border: \"none\",\n                            borderRadius: \"5px\",\n                            cursor: \"pointer\",\n                            marginTop: \"15px\"\n                        },\n                        children: \"← Back to Strategies\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                        lineNumber: 304,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                lineNumber: 173,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: \"#1e40af\",\n                    color: \"white\",\n                    padding: \"20px\",\n                    borderRadius: \"10px\",\n                    marginTop: \"30px\",\n                    textAlign: \"center\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        children: \"\\uD83C\\uDFE6 PROFESSIONAL TRADING WITH INTERACTIVE BROKERS!\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                        lineNumber: 314,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"✅ Best Options Data • ✅ $0.65 Per Contract • ✅ Professional Platform\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                        lineNumber: 315,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                            children: \"Your system now supports IBKR for professional-grade options trading!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                            lineNumber: 316,\n                            columnNumber: 12\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                        lineNumber: 316,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: \"rgba(255,255,255,0.1)\",\n                            padding: \"15px\",\n                            borderRadius: \"5px\",\n                            marginTop: \"15px\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"\\uD83D\\uDE80 NEXT STEP:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 318,\n                                        columnNumber: 14\n                                    }, this),\n                                    \" Set up your IBKR API connection\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 318,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    fontSize: \"14px\",\n                                    marginTop: \"5px\"\n                                },\n                                children: \"1. Enable API in your IBKR account • 2. Download TWS/Gateway • 3. Start trading!\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 319,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                        lineNumber: 317,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                lineNumber: 313,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: \"#22c55e\",\n                    color: \"white\",\n                    padding: \"20px\",\n                    borderRadius: \"10px\",\n                    marginTop: \"20px\",\n                    textAlign: \"center\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        children: \"\\uD83D\\uDE80 YOUR SYSTEM IS LIVE AND READY!\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                        lineNumber: 326,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"Connected to Alpaca • FMP Data • IBKR Ready • AI-Powered Strategies\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                        lineNumber: 327,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                            children: \"Start making money today with proven strategies!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                            lineNumber: 328,\n                            columnNumber: 12\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                        lineNumber: 328,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        style: {\n                            marginTop: \"10px\",\n                            fontSize: \"14px\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Network Access:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 330,\n                                columnNumber: 11\n                            }, this),\n                            \" Other computers can access at http://192.168.1.73:3003\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                        lineNumber: 329,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                lineNumber: 325,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this);\n}\n_s(HomePage, \"ovJrq+w6DchUH+Yrk40Ze7eM3HM=\");\n_c = HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});