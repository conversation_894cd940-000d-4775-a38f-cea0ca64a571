import { RawOptionTrade, RawOptionQuote, AlpacaOptionTrade, AlpacaOptionQuote } from "./entityv2";
import { AlpacaWebsocket as Websocket, WebsocketOptions } from "./websocket";
interface OptionWebsocketOptions extends WebsocketOptions {
    feed?: string;
}
export declare class AlpacaOptionClient extends Websocket {
    constructor(options: OptionWebsocketOptions);
    subscribeForTrades(trades: Array<string>): void;
    subscribeForQuotes(quotes: Array<string>): void;
    subscribe(symbols: {
        trades?: Array<string>;
        quotes?: Array<string>;
    }): void;
    subscribeAll(): void;
    unsubscribeFromTrades(trades: Array<string>): void;
    unsubscribeFromQuotes(quotes: Array<string>): void;
    unsubscribe(symbols: {
        trades?: Array<string>;
        quotes?: Array<string>;
    }): void;
    updateSubscriptions(msg: {
        trades: Array<string>;
        quotes: Array<string>;
    }): void;
    onOptionTrade(fn: (tarde: AlpacaOptionTrade) => void): void;
    onOptionQuote(fn: (quote: AlpacaOptionQuote) => void): void;
    dataHandler(data: Array<RawOptionTrade | RawOptionQuote>): void;
}
export {};
