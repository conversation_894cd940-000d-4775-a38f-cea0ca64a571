'use client';

import { useState } from 'react';

export default function IBKRTestPage() {
  const [connectionStatus, setConnectionStatus] = useState('Not Connected');
  const [loading, setLoading] = useState(false);
  const [accountInfo, setAccountInfo] = useState(null);
  const [error, setError] = useState('');

  const testConnection = async () => {
    setLoading(true);
    setError('');
    
    try {
      console.log('🔌 Testing IBKR connection...');
      
      const response = await fetch('/api/ibkr?action=connect');
      const result = await response.json();
      
      if (result.success) {
        setConnectionStatus('✅ Connected to IBKR!');
        console.log('✅ IBKR Connected successfully!');
        
        // Get account info
        setTimeout(async () => {
          try {
            const accountResponse = await fetch('/api/ibkr?action=account');
            const accountResult = await accountResponse.json();
            
            if (accountResult.success) {
              setAccountInfo(accountResult.data);
            }
          } catch (err) {
            console.log('Account info not available yet');
          }
        }, 2000);
        
      } else {
        setConnectionStatus('❌ Connection Failed');
        setError(result.message || 'Unknown error');
        console.error('❌ IBKR Connection failed:', result.message);
      }
    } catch (err) {
      setConnectionStatus('❌ Connection Error');
      setError(err instanceof Error ? err.message : 'Network error');
      console.error('❌ Connection error:', err);
    } finally {
      setLoading(false);
    }
  };

  const checkStatus = async () => {
    try {
      const response = await fetch('/api/ibkr?action=status');
      const result = await response.json();
      
      if (result.success) {
        setConnectionStatus(result.data.connected ? '✅ Connected' : '❌ Disconnected');
      }
    } catch (err) {
      setConnectionStatus('❌ Error checking status');
    }
  };

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial', maxWidth: '800px', margin: '0 auto' }}>
      <h1 style={{ color: '#1e40af', marginBottom: '20px' }}>🏦 IBKR Connection Test</h1>

      <div style={{ background: '#ef4444', color: 'white', padding: '20px', borderRadius: '10px', marginBottom: '20px', textAlign: 'center' }}>
        <h2 style={{ margin: '0 0 10px 0' }}>🚨 IMPORTANT: NO API KEY NEEDED!</h2>
        <p style={{ margin: 0, fontSize: '16px' }}>
          IBKR doesn't use API keys! You just need: ✅ Your login ✅ IB Gateway ✅ Account number
        </p>
      </div>
      
      <div style={{ background: '#f0f9ff', padding: '20px', borderRadius: '10px', marginBottom: '20px' }}>
        <h2>📋 SUPER SIMPLE 3-STEP SETUP</h2>
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '20px', marginTop: '15px' }}>

          <div style={{ background: 'white', padding: '15px', borderRadius: '8px', border: '2px solid #22c55e' }}>
            <h3 style={{ color: '#22c55e', margin: '0 0 10px 0' }}>1️⃣ Enable API</h3>
            <p style={{ margin: '0 0 10px 0', fontSize: '14px' }}>In Client Portal:</p>
            <ul style={{ margin: 0, fontSize: '14px', lineHeight: '1.4' }}>
              <li>Settings → API</li>
              <li>Check "Enable API"</li>
              <li>Port: 7497</li>
            </ul>
          </div>

          <div style={{ background: 'white', padding: '15px', borderRadius: '8px', border: '2px solid '#0ea5e9' }}>
            <h3 style={{ color: '#0ea5e9', margin: '0 0 10px 0' }}>2️⃣ Download Gateway</h3>
            <p style={{ margin: '0 0 10px 0', fontSize: '14px' }}>Get IB Gateway:</p>
            <ul style={{ margin: 0, fontSize: '14px', lineHeight: '1.4' }}>
              <li>Download from IBKR</li>
              <li>Install & run</li>
              <li>Login normally</li>
            </ul>
          </div>

          <div style={{ background: 'white', padding: '15px', borderRadius: '8px', border: '2px solid #f59e0b' }}>
            <h3 style={{ color: '#f59e0b', margin: '0 0 10px 0' }}>3️⃣ Get Account #</h3>
            <p style={{ margin: '0 0 10px 0', fontSize: '14px' }}>Find your number:</p>
            <ul style={{ margin: 0, fontSize: '14px', lineHeight: '1.4' }}>
              <li>DU123456 (paper)</li>
              <li>U123456 (live)</li>
              <li>Top of Client Portal</li>
            </ul>
          </div>

        </div>

        <div style={{ background: '#22c55e', color: 'white', padding: '15px', borderRadius: '5px', marginTop: '20px', textAlign: 'center' }}>
          <strong>🎉 THAT'S IT! NO API KEYS, NO TOKENS, NO BULLSHIT!</strong>
        </div>
      </div>

      <div style={{ background: '#fffbeb', border: '1px solid #fed7aa', padding: '20px', borderRadius: '10px', marginBottom: '20px' }}>
        <h3>🔍 Find Your Account ID</h3>
        <p>Your IBKR account ID should be visible in the Client Portal:</p>
        <ul style={{ lineHeight: '1.6' }}>
          <li><strong>Live Account:</strong> Starts with "U" (like U1234567)</li>
          <li><strong>Paper Account:</strong> Starts with "DU" (like DU1234567)</li>
          <li><strong>Location:</strong> Usually at the top of your Client Portal page</li>
        </ul>

        <div style={{ background: 'white', padding: '10px', borderRadius: '5px', marginTop: '10px', border: '1px solid #d1d5db' }}>
          <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>Enter your Account ID:</label>
          <input
            type="text"
            placeholder="e.g., DU1234567 or U1234567"
            style={{ width: '100%', padding: '8px', border: '1px solid #d1d5db', borderRadius: '3px' }}
            onChange={(e) => console.log('Account ID:', e.target.value)}
          />
          <p style={{ fontSize: '12px', color: '#6b7280', marginTop: '5px' }}>
            We'll use this to connect to your IBKR account
          </p>
        </div>
      </div>

      <div style={{ background: 'white', padding: '20px', borderRadius: '10px', boxShadow: '0 2px 4px rgba(0,0,0,0.1)', marginBottom: '20px' }}>
        <h3>Connection Status</h3>
        <p style={{ fontSize: '18px', fontWeight: 'bold', color: connectionStatus.includes('✅') ? '#22c55e' : '#ef4444' }}>
          {connectionStatus}
        </p>
        
        {error && (
          <div style={{ background: '#fef2f2', border: '1px solid #fecaca', padding: '10px', borderRadius: '5px', marginTop: '10px' }}>
            <p style={{ color: '#dc2626', margin: 0 }}><strong>Error:</strong> {error}</p>
          </div>
        )}
        
        <div style={{ marginTop: '15px' }}>
          <button 
            onClick={testConnection}
            disabled={loading}
            style={{ 
              background: loading ? '#9ca3af' : '#1e40af', 
              color: 'white', 
              padding: '10px 20px', 
              border: 'none', 
              borderRadius: '5px', 
              cursor: loading ? 'not-allowed' : 'pointer',
              marginRight: '10px',
              fontSize: '16px'
            }}
          >
            {loading ? '🔄 Connecting...' : '🔌 Connect to IBKR'}
          </button>
          
          <button 
            onClick={checkStatus}
            style={{ 
              background: '#6b7280', 
              color: 'white', 
              padding: '10px 20px', 
              border: 'none', 
              borderRadius: '5px', 
              cursor: 'pointer',
              fontSize: '16px'
            }}
          >
            📊 Check Status
          </button>
        </div>
      </div>

      {accountInfo && (
        <div style={{ background: '#f0fdf4', border: '1px solid #bbf7d0', padding: '20px', borderRadius: '10px', marginBottom: '20px' }}>
          <h3 style={{ color: '#15803d' }}>💰 Account Information</h3>
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '15px' }}>
            <div>
              <strong>Net Liquidation:</strong><br />
              ${accountInfo.netLiquidation?.toLocaleString() || 'N/A'}
            </div>
            <div>
              <strong>Available Funds:</strong><br />
              ${accountInfo.availableFunds?.toLocaleString() || 'N/A'}
            </div>
            <div>
              <strong>Buying Power:</strong><br />
              ${accountInfo.buyingPower?.toLocaleString() || 'N/A'}
            </div>
            <div>
              <strong>Total Cash:</strong><br />
              ${accountInfo.totalCashValue?.toLocaleString() || 'N/A'}
            </div>
          </div>
        </div>
      )}

      <div style={{ background: '#fffbeb', border: '1px solid #fed7aa', padding: '20px', borderRadius: '10px' }}>
        <h3 style={{ color: '#d97706' }}>🔧 Troubleshooting</h3>
        
        <div style={{ marginBottom: '15px' }}>
          <strong>If connection fails:</strong>
          <ol style={{ marginTop: '5px', lineHeight: '1.6' }}>
            <li>Make sure TWS Gateway is running and logged in</li>
            <li>Check that API is enabled: Configure → Settings → API → Settings</li>
            <li>Verify port number: 7497 for paper trading, 7496 for live</li>
            <li>Ensure "Enable ActiveX and Socket Clients" is checked</li>
            <li>Try restarting TWS Gateway</li>
          </ol>
        </div>
        
        <div>
          <strong>Common Error Messages:</strong>
          <ul style={{ marginTop: '5px', lineHeight: '1.6' }}>
            <li><strong>Connection timeout:</strong> TWS Gateway not running or wrong port</li>
            <li><strong>Connection refused:</strong> API not enabled in settings</li>
            <li><strong>Authentication failed:</strong> Wrong client ID or account</li>
          </ul>
        </div>
      </div>

      <div style={{ background: '#1e40af', color: 'white', padding: '20px', borderRadius: '10px', marginTop: '20px' }}>
        <h3>🚀 Quick Setup Links</h3>
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '15px', marginTop: '15px' }}>
          <div style={{ background: 'rgba(255,255,255,0.1)', padding: '15px', borderRadius: '5px' }}>
            <strong>📱 Client Portal</strong><br />
            <a href="https://www.interactivebrokers.com/sso/Login" target="_blank" style={{ color: '#93c5fd' }}>
              Enable API Access
            </a>
          </div>
          <div style={{ background: 'rgba(255,255,255,0.1)', padding: '15px', borderRadius: '5px' }}>
            <strong>💻 Download IB Gateway</strong><br />
            <a href="https://www.interactivebrokers.com/en/trading/ib-api.php" target="_blank" style={{ color: '#93c5fd' }}>
              Get IB Gateway
            </a>
          </div>
          <div style={{ background: 'rgba(255,255,255,0.1)', padding: '15px', borderRadius: '5px' }}>
            <strong>📚 Paper Trading</strong><br />
            <span style={{ color: '#93c5fd' }}>Enable in Client Portal Settings</span>
          </div>
        </div>
      </div>

      <div style={{ background: '#22c55e', color: 'white', padding: '15px', borderRadius: '10px', marginTop: '20px', textAlign: 'center' }}>
        <p><strong>🚀 Once connected, you'll have access to:</strong></p>
        <p>Professional options data • Real-time quotes • Advanced order types • Global markets</p>
      </div>
    </div>
  );
}
