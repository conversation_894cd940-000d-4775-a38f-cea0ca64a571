"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.mergeCorporateActions = exports.getCorporateActionsSize = exports.convertCorporateActions = exports.NewTimeframe = exports.TimeFrameUnit = exports.AlpacaNews = exports.AlpacaOptionSnapshotV1Beta1 = exports.AlpacaOptionQuoteV1Beta1 = exports.AlpacaOptionTradeV1Beta1 = exports.AlpacaOptionBarV1Beta1 = exports.AlpacaCryptoOrderbook = exports.AlpacaCryptoSnapshot = exports.AlpacaCryptoBar = exports.AlpacaCryptoQuote = exports.AlpacaCryptoTrade = exports.AlpacaCorrectionV2 = exports.AlpacaCancelErrorV2 = exports.AlpacaLuldV2 = exports.AlpacaStatusV2 = exports.AlpacaSnapshotV2 = exports.AlpacaBarV2 = exports.AlpacaQuoteV2 = exports.AlpacaTradeV2 = void 0;
const mapKeys_1 = __importDefault(require("lodash/mapKeys"));
const mapValues_1 = __importDefault(require("lodash/mapValues"));
const trade_mapping_v2 = {
    S: "Symbol",
    i: "ID",
    x: "Exchange",
    p: "Price",
    s: "Size",
    t: "Timestamp",
    c: "Conditions",
    z: "Tape",
};
const quote_mapping_v2 = {
    S: "Symbol",
    bx: "BidExchange",
    bp: "BidPrice",
    bs: "BidSize",
    ax: "AskExchange",
    ap: "AskPrice",
    as: "AskSize",
    t: "Timestamp",
    c: "Conditions",
    z: "Tape",
};
const bar_mapping_v2 = {
    S: "Symbol",
    o: "OpenPrice",
    h: "HighPrice",
    l: "LowPrice",
    c: "ClosePrice",
    v: "Volume",
    t: "Timestamp",
    vw: "VWAP",
    n: "TradeCount",
};
const snapshot_mapping_v2 = {
    symbol: "symbol",
    latestTrade: "LatestTrade",
    latestQuote: "LatestQuote",
    minuteBar: "MinuteBar",
    dailyBar: "DailyBar",
    prevDailyBar: "PrevDailyBar",
};
const status_mapping_v2 = {
    S: "Symbol",
    sc: "StatusCode",
    sm: "StatusMessage",
    rc: "ReasonCode",
    rm: "ReasonMessage",
    t: "Timestamp",
    z: "Tape",
};
const luld_mapping_v2 = {
    S: "Symbol",
    u: "LimitUpPrice",
    d: "LimitDownPrice",
    i: "Indicator",
    t: "Timestamp",
    z: "Tape",
};
const cancel_error_mapping_v2 = {
    S: "Symbol",
    i: "ID",
    x: "Exchange",
    p: "Price",
    s: "Size",
    a: "CancelErrorAction",
    z: "Tape",
    t: "Timestamp",
};
const correction_mapping_v2 = {
    S: "Symbol",
    x: "Exchange",
    oi: "OriginalID",
    op: "OriginalPrice",
    os: "OriginalSize",
    oc: "OriginalConditions",
    ci: "CorrectedID",
    cp: "CorrectedPrice",
    cs: "CorrectedSize",
    cc: "CorrectedConditions",
    z: "Tape",
    t: "Timestamp",
};
const crypto_trade_mapping = {
    S: "Symbol",
    t: "Timestamp",
    x: "Exchange",
    p: "Price",
    s: "Size",
    tks: "TakerSide",
    i: "ID",
};
const crypto_quote_mapping = {
    t: "Timestamp",
    bp: "BidPrice",
    bs: "BidSize",
    ap: "AskPrice",
    as: "AskSize",
};
const crypto_bar_mapping = {
    t: "Timestamp",
    o: "Open",
    h: "High",
    l: "Low",
    c: "Close",
    v: "Volume",
    vw: "VWAP",
    n: "TradeCount",
};
const crypto_snapshot_mapping = {
    latestTrade: "LatestTrade",
    latestQuote: "LatestQuote",
    minuteBar: "MinuteBar",
    dailyBar: "DailyBar",
    prevDailyBar: "PrevDailyBar",
};
const crypto_orderbook_entry_mapping = {
    p: "Price",
    s: "Size",
};
const crypto_orderbook_mapping = {
    t: "Timestamp",
    b: "Bids",
    a: "Asks",
};
const news_image_mapping = {
    size: "Size",
    url: "URL",
};
const news_mapping = {
    id: "ID",
    author: "Author",
    created_at: "CreatedAt",
    updated_at: "UpdatedAt",
    headline: "Headline",
    summary: "Summary",
    content: "Content",
    images: "Images",
    url: "URL",
    symbols: "Symbols",
    source: "Source",
};
const option_bar_mapping = {
    S: "Symbol",
    o: "Open",
    h: "High",
    l: "Low",
    c: "Close",
    v: "Volume",
    t: "Timestamp",
    vw: "VWAP",
    n: "TradeCount",
};
const option_tarde_mapping = {
    S: "Symbol",
    x: "Exchange",
    p: "Price",
    s: "Size",
    t: "Timestamp",
    c: "Condition",
};
const option_quote_mapping = {
    S: "Symbol",
    bx: "BidExchange",
    bp: "BidPrice",
    bs: "BidSize",
    ax: "AskExchange",
    ap: "AskPrice",
    as: "AskSize",
    t: "Timestamp",
    c: "Conditions",
    z: "Tape",
};
const option_snapshot_mapping = {
    symbol: "symbol",
    latestTrade: "LatestTrade",
    latestQuote: "LatestQuote",
    impliedVolatility: "ImpliedVolatility",
    greeks: "Greeks",
};
function AlpacaTradeV2(data) {
    return aliasObjectKey(data, trade_mapping_v2);
}
exports.AlpacaTradeV2 = AlpacaTradeV2;
function AlpacaQuoteV2(data) {
    return aliasObjectKey(data, quote_mapping_v2);
}
exports.AlpacaQuoteV2 = AlpacaQuoteV2;
function AlpacaBarV2(data) {
    return aliasObjectKey(data, bar_mapping_v2);
}
exports.AlpacaBarV2 = AlpacaBarV2;
function AlpacaSnapshotV2(data) {
    const snapshot = aliasObjectKey(data, snapshot_mapping_v2);
    return (0, mapValues_1.default)(snapshot, (value, key) => {
        return convertSnapshotData(key, value, false);
    });
}
exports.AlpacaSnapshotV2 = AlpacaSnapshotV2;
function AlpacaStatusV2(data) {
    return aliasObjectKey(data, status_mapping_v2);
}
exports.AlpacaStatusV2 = AlpacaStatusV2;
function AlpacaLuldV2(data) {
    return aliasObjectKey(data, luld_mapping_v2);
}
exports.AlpacaLuldV2 = AlpacaLuldV2;
function AlpacaCancelErrorV2(data) {
    return aliasObjectKey(data, cancel_error_mapping_v2);
}
exports.AlpacaCancelErrorV2 = AlpacaCancelErrorV2;
function AlpacaCorrectionV2(data) {
    return aliasObjectKey(data, correction_mapping_v2);
}
exports.AlpacaCorrectionV2 = AlpacaCorrectionV2;
function AlpacaCryptoTrade(data) {
    return aliasObjectKey(data, crypto_trade_mapping);
}
exports.AlpacaCryptoTrade = AlpacaCryptoTrade;
function AlpacaCryptoQuote(data) {
    return aliasObjectKey(data, crypto_quote_mapping);
}
exports.AlpacaCryptoQuote = AlpacaCryptoQuote;
function AlpacaCryptoBar(data) {
    return aliasObjectKey(data, crypto_bar_mapping);
}
exports.AlpacaCryptoBar = AlpacaCryptoBar;
function AlpacaCryptoSnapshot(data) {
    const snapshot = aliasObjectKey(data, crypto_snapshot_mapping);
    return (0, mapValues_1.default)(snapshot, (value, key) => {
        return convertSnapshotData(key, value, true);
    });
}
exports.AlpacaCryptoSnapshot = AlpacaCryptoSnapshot;
function AlpacaCryptoOrderbook(data) {
    const mapFn = (entries) => entries.map((entry) => aliasObjectKey(entry, crypto_orderbook_entry_mapping));
    const orderbook = aliasObjectKey(data, crypto_orderbook_mapping);
    return Object.assign(Object.assign({}, orderbook), { Bids: mapFn(orderbook.Bids), Asks: mapFn(orderbook.Asks) });
}
exports.AlpacaCryptoOrderbook = AlpacaCryptoOrderbook;
function AlpacaOptionBarV1Beta1(data) {
    return aliasObjectKey(data, option_bar_mapping);
}
exports.AlpacaOptionBarV1Beta1 = AlpacaOptionBarV1Beta1;
function AlpacaOptionTradeV1Beta1(data) {
    return aliasObjectKey(data, option_tarde_mapping);
}
exports.AlpacaOptionTradeV1Beta1 = AlpacaOptionTradeV1Beta1;
function AlpacaOptionQuoteV1Beta1(data) {
    return aliasObjectKey(data, option_quote_mapping);
}
exports.AlpacaOptionQuoteV1Beta1 = AlpacaOptionQuoteV1Beta1;
function AlpacaOptionSnapshotV1Beta1(data) {
    const snapshot = aliasObjectKey(data, option_snapshot_mapping);
    return (0, mapValues_1.default)(snapshot, (value, key) => {
        return convertOptionSnapshotData(key, value);
    });
}
exports.AlpacaOptionSnapshotV1Beta1 = AlpacaOptionSnapshotV1Beta1;
function aliasObjectKey(data, mapping) {
    return (0, mapKeys_1.default)(data, (_value, key) => {
        return Object.hasOwn(mapping, key) ? mapping[key] : key;
    });
}
function convertSnapshotData(key, data, isCrypto) {
    switch (key) {
        case "LatestTrade":
            return isCrypto ? AlpacaCryptoTrade(data) : AlpacaTradeV2(data);
        case "LatestQuote":
            return isCrypto ? AlpacaCryptoQuote(data) : AlpacaQuoteV2(data);
        case "MinuteBar":
        case "DailyBar":
        case "PrevDailyBar":
            return isCrypto ? AlpacaCryptoBar(data) : AlpacaBarV2(data);
        default:
            return data;
    }
}
function convertOptionSnapshotData(key, data) {
    switch (key) {
        case "LatestTrade":
            return AlpacaOptionTradeV1Beta1(data);
        case "LatestQuote":
            return AlpacaOptionQuoteV1Beta1(data);
        default:
            return data;
    }
}
function AlpacaNews(data) {
    const mappedNews = aliasObjectKey(data, news_mapping);
    if (mappedNews.Images) {
        mappedNews.Images.forEach((element) => {
            return aliasObjectKey(element, news_image_mapping);
        });
    }
    return mappedNews;
}
exports.AlpacaNews = AlpacaNews;
var TimeFrameUnit;
(function (TimeFrameUnit) {
    TimeFrameUnit["MIN"] = "Min";
    TimeFrameUnit["HOUR"] = "Hour";
    TimeFrameUnit["DAY"] = "Day";
    TimeFrameUnit["WEEK"] = "Week";
    TimeFrameUnit["MONTH"] = "Month";
})(TimeFrameUnit || (exports.TimeFrameUnit = TimeFrameUnit = {}));
function NewTimeframe(amount, unit) {
    if (amount <= 0) {
        throw new Error("amount must be a positive integer value");
    }
    if (unit == TimeFrameUnit.MIN && amount > 59) {
        throw new Error("minute timeframe can only be used with amount between 1-59");
    }
    if (unit == TimeFrameUnit.HOUR && amount > 23) {
        throw new Error("hour timeframe can only be used with amounts 1-23");
    }
    if ((unit == TimeFrameUnit.DAY || unit == TimeFrameUnit.WEEK) && amount != 1) {
        throw new Error("day and week timeframes can only be used with amount 1");
    }
    if (unit == TimeFrameUnit.MONTH && ![1, 2, 3, 6, 12].includes(amount)) {
        throw new Error("month timeframe can only be used with amount 1, 2, 3, 6 and 12");
    }
    return `${amount}${unit}`;
}
exports.NewTimeframe = NewTimeframe;
const cash_dividend_mapping = {
    ex_date: "ExDate",
    foreign: "Foreign",
    payable_date: "PayableDate",
    process_date: "ProcessDate",
    rate: "Rate",
    record_date: "RecordDate",
    special: "Special",
    symbol: "Symbol",
};
const reverse_split_mapping = {
    ex_date: "ExDate",
    new_rate: "NewRate",
    old_rate: "OldRate",
    payable_date: "PayableDate",
    process_date: "ProcessDate",
    record_date: "RecordDate",
    symbol: "Symbol",
};
const forward_split_mapping = {
    due_bill_redemption_date: "DueBillRedemptionDate",
    ex_date: "ExDate",
    new_rate: "NewRate",
    old_rate: "OldRate",
    payable_date: "PayableDate",
    process_date: "ProcessDate",
    record_date: "RecordDate",
    symbol: "Symbol",
};
const unit_split_mapping = {
    alternate_rate: "AlternateRate",
    alternate_symbol: "AlternateSymbol",
    effective_date: "EffectiveDate",
    new_rate: "NewRate",
    new_symbol: "NewSymbol",
    old_rate: "OldRate",
    old_symbol: "OldSymbol",
    process_date: "ProcessDate",
};
const cash_merger_mapping = {
    acquiree_symbol: "AcquireeSymbol",
    acquirer_symbol: "AcquirerSymbol",
    effective_date: "EffectiveDate",
    process_date: "ProcessDate",
    rate: "Rate",
};
const stock_merger_mapping = {
    acquiree_rate: "AcquireeRate",
    acquiree_symbol: "AcquireeSymbol",
    acquirer_rate: "AcquirerRate",
    acquirer_symbol: "AcquirerSymbol",
    effective_date: "EffectiveDate",
    payable_date: "PayableDate",
    process_date: "ProcessDate",
};
const stock_and_cash_merger_mapping = {
    stock_merger_mapping,
    cash_rate: "CashRate",
};
const stock_dividends_mapping = {
    ex_date: "ExDate",
    payable_date: "PayableDate",
    process_date: "ProcessDate",
    rate: "Rate",
    record_date: "RecordDate",
    symbol: "Symbol",
};
const redemption_mapping = {
    payable_date: "PayableDate",
    process_date: "ProcessDate",
    rate: "Rate",
    symbol: "Symbol",
};
const spin_off_mapping = {
    ex_date: "ExDate",
    new_rate: "NewRate",
    new_symbol: "NewSymbol",
    process_date: "ProcessDate",
    record_date: "RecordDate",
    source_rate: "Rate",
    source_symbol: "SourceSymbol",
};
const name_change_mapping = {
    new_symbol: "NewSymbol",
    old_symbol: "OldSymbol",
    process_date: "ProcessDate",
};
const worthless_removal_mapping = {
    symbol: "Symbol",
    process_date: "ProcessDate",
};
const rights_distribution_mapping = {
    source_symbol: "SourceSymbol",
    new_symbol: "NewSymbol",
    rate: "Rate",
    process_date: "ProcessDate",
    ex_date: "ExDate",
    payable_date: "PayableDate",
    record_date: "RecordDate",
    expiration_date: "ExpirationDate",
};
function convertCorporateActions(data) {
    var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o;
    let cas = {};
    if (((_a = data.cash_dividends) === null || _a === void 0 ? void 0 : _a.length) > 0) {
        cas.CashDividends = cas.CashDividends ? cas.CashDividends : Array();
        data.cash_dividends.forEach((cd) => {
            cas.CashDividends.push(aliasObjectKey(cd, cash_dividend_mapping));
        });
    }
    if (((_b = data.reverse_splits) === null || _b === void 0 ? void 0 : _b.length) > 0) {
        cas.ReverseSplits = cas.ReverseSplits ? cas.ReverseSplits : Array();
        data.reverse_splits.forEach((rs) => {
            cas.ReverseSplits.push(aliasObjectKey(rs, reverse_split_mapping));
        });
    }
    if (((_c = data.forward_splits) === null || _c === void 0 ? void 0 : _c.length) > 0) {
        cas.ForwardSplits = cas.ForwardSplits ? cas.ForwardSplits : Array();
        data.forward_splits.forEach((fs) => {
            cas.ForwardSplits.push(aliasObjectKey(fs, forward_split_mapping));
        });
    }
    if (((_d = data.unit_splits) === null || _d === void 0 ? void 0 : _d.length) > 0) {
        cas.UnitSplits = cas.UnitSplits ? cas.UnitSplits : Array();
        data.unit_splits.forEach((fs) => {
            cas.UnitSplits.push(aliasObjectKey(fs, unit_split_mapping));
        });
    }
    if (((_e = data.cash_mergers) === null || _e === void 0 ? void 0 : _e.length) > 0) {
        cas.CashMergers = cas.CashMergers ? cas.CashMergers : Array();
        data.cash_mergers.forEach((cm) => {
            cas.CashMergers.push(aliasObjectKey(cm, cash_merger_mapping));
        });
    }
    if (((_f = data.stock_mergers) === null || _f === void 0 ? void 0 : _f.length) > 0) {
        cas.StockMergers = cas.StockMergers ? cas.StockMergers : Array();
        data.stock_mergers.forEach((sm) => {
            cas.StockMergers.push(aliasObjectKey(sm, stock_merger_mapping));
        });
    }
    if (((_g = data.stock_and_cash_mergers) === null || _g === void 0 ? void 0 : _g.length) > 0) {
        cas.StockAndCashMerger = cas.StockAndCashMerger
            ? cas.StockAndCashMerger
            : Array();
        data.stock_and_cash_mergers.forEach((scm) => {
            cas.StockAndCashMerger.push(aliasObjectKey(scm, stock_and_cash_merger_mapping));
        });
    }
    if (((_h = data.stock_dividends) === null || _h === void 0 ? void 0 : _h.length) > 0) {
        cas.StockDividends = cas.StockDividends
            ? cas.StockDividends
            : Array();
        data.stock_dividends.forEach((sd) => {
            cas.StockDividends.push(aliasObjectKey(sd, stock_dividends_mapping));
        });
    }
    if (((_j = data.redemptions) === null || _j === void 0 ? void 0 : _j.length) > 0) {
        cas.Redemptions = cas.Redemptions ? cas.Redemptions : Array();
        data.redemptions.forEach((r) => {
            cas.Redemptions.push(aliasObjectKey(r, redemption_mapping));
        });
    }
    if (((_k = data.spin_offs) === null || _k === void 0 ? void 0 : _k.length) > 0) {
        cas.SpinOffs = cas.SpinOffs ? cas.SpinOffs : Array();
        data.spin_offs.forEach((so) => {
            cas.SpinOffs.push(aliasObjectKey(so, spin_off_mapping));
        });
    }
    if (((_l = data.name_changes) === null || _l === void 0 ? void 0 : _l.length) > 0) {
        cas.NameChanges = cas.NameChanges ? cas.NameChanges : Array();
        data.name_changes.forEach((nc) => {
            cas.NameChanges.push(aliasObjectKey(nc, name_change_mapping));
        });
    }
    if (((_m = data.worthless_removals) === null || _m === void 0 ? void 0 : _m.length) > 0) {
        cas.WorthlessRemovals = cas.WorthlessRemovals
            ? cas.WorthlessRemovals
            : Array();
        data.worthless_removals.forEach((wr) => {
            cas.WorthlessRemovals.push(aliasObjectKey(wr, worthless_removal_mapping));
        });
    }
    if (((_o = data.rights_distributions) === null || _o === void 0 ? void 0 : _o.length) > 0) {
        cas.RightsDistributions = cas.RightsDistributions
            ? cas.RightsDistributions
            : Array();
        data.rights_distributions.forEach((rd) => {
            cas.RightsDistributions.push(aliasObjectKey(rd, rights_distribution_mapping));
        });
    }
    return cas;
}
exports.convertCorporateActions = convertCorporateActions;
function getCorporateActionsSize(cas) {
    let sum = 0;
    for (const key in cas) {
        sum += cas[key]
            ? cas[key].length
            : 0;
    }
    return sum;
}
exports.getCorporateActionsSize = getCorporateActionsSize;
function mergeCorporateActions(ca1, ca2) {
    return {
        CashDividends: (ca1.CashDividends || []).concat(ca2.CashDividends || []),
        ReverseSplits: (ca1.ReverseSplits || []).concat(ca2.ReverseSplits || []),
        ForwardSplits: (ca1.ForwardSplits || []).concat(ca2.ForwardSplits || []),
        UnitSplits: (ca1.UnitSplits || []).concat(ca2.UnitSplits || []),
        CashMergers: (ca1.CashMergers || []).concat(ca2.CashMergers || []),
        StockMergers: (ca1.StockMergers || []).concat(ca2.StockMergers || []),
        StockAndCashMerger: (ca1.StockAndCashMerger || []).concat(ca2.StockAndCashMerger || []),
        StockDividends: (ca1.StockDividends || []).concat(ca2.StockDividends || []),
        Redemptions: (ca1.Redemptions || []).concat(ca2.Redemptions || []),
        SpinOffs: (ca1.SpinOffs || []).concat(ca2.SpinOffs || []),
        NameChanges: (ca1.NameChanges || []).concat(ca2.NameChanges || []),
        WorthlessRemovals: (ca1.WorthlessRemovals || []).concat(ca2.WorthlessRemovals || []),
        RightsDistributions: (ca1.RightsDistributions || []).concat(ca2.RightsDistributions || []),
    };
}
exports.mergeCorporateActions = mergeCorporateActions;
