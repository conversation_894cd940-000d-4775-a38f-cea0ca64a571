"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AlpacaCryptoClient = void 0;
const entityv2_1 = require("./entityv2");
const websocket_1 = require("./websocket");
const eventTypeMap = new Map([
    ["t", { event: websocket_1.EVENT.TRADES, parse: entityv2_1.AlpacaCryptoTrade }],
    ["q", { event: websocket_1.EVENT.QUOTES, parse: entityv2_1.AlpacaCryptoQuote }],
    ["b", { event: websocket_1.EVENT.BARS, parse: entityv2_1.AlpacaCryptoBar }],
    ["u", { event: websocket_1.EVENT.UPDATED_BARS, parse: entityv2_1.AlpacaCryptoBar }],
    ["d", { event: websocket_1.EVENT.DAILY_BARS, parse: entityv2_1.AlpacaCryptoBar }],
    ["o", { event: websocket_1.EVENT.ORDERBOOKS, parse: entityv2_1.AlpacaCryptoOrderbook }],
]);
class AlpacaCryptoClient extends websocket_1.AlpacaWebsocket {
    constructor(options) {
        options.url = options.url.replace("https", "wss") + "/v1beta3/crypto/us";
        options.subscriptions = {
            trades: [],
            quotes: [],
            bars: [],
            updatedBars: [],
            dailyBars: [],
            orderbooks: [],
        };
        super(options);
    }
    subscribeForTrades(trades) {
        this.session.subscriptions.trades.push(...trades);
        this.subscribe({ trades });
    }
    subscribeForQuotes(quotes) {
        this.session.subscriptions.quotes.push(...quotes);
        this.subscribe({ quotes });
    }
    subscribeForBars(bars) {
        this.session.subscriptions.bars.push(...bars);
        this.subscribe({ bars });
    }
    subscribeForUpdatedBars(updatedBars) {
        this.session.subscriptions.updatedBars.push(...updatedBars);
        this.subscribe({ updatedBars });
    }
    subscribeForDailyBars(dailyBars) {
        this.session.subscriptions.dailyBars.push(...dailyBars);
        this.subscribe({ dailyBars });
    }
    subscribeForOrderbooks(orderbooks) {
        this.session.subscriptions.orderbooks.push(...orderbooks);
        this.subscribe({ orderbooks });
    }
    subscribe(symbols) {
        var _a, _b, _c, _d, _e, _f;
        const subMsg = {
            action: "subscribe",
            trades: (_a = symbols.trades) !== null && _a !== void 0 ? _a : [],
            quotes: (_b = symbols.quotes) !== null && _b !== void 0 ? _b : [],
            bars: (_c = symbols.bars) !== null && _c !== void 0 ? _c : [],
            updatedBars: (_d = symbols.updatedBars) !== null && _d !== void 0 ? _d : [],
            dailyBars: (_e = symbols.dailyBars) !== null && _e !== void 0 ? _e : [],
            orderbooks: (_f = symbols.orderbooks) !== null && _f !== void 0 ? _f : [],
        };
        this.conn.send(this.msgpack.encode(subMsg));
    }
    subscribeAll() {
        this.subscribe(this.session.subscriptions);
    }
    unsubscribeFromTrades(trades) {
        this.session.subscriptions.trades = this.session.subscriptions.trades.filter((trade) => !trades.includes(trade));
        this.unsubscribe({ trades });
    }
    unsubscribeFromQuotes(quotes) {
        this.session.subscriptions.quotes = this.session.subscriptions.quotes.filter((quote) => !quotes.includes(quote));
        this.unsubscribe({ quotes });
    }
    unsubscribeFromBars(bars) {
        this.session.subscriptions.bars = this.session.subscriptions.bars.filter((bar) => !bars.includes(bar));
        this.unsubscribe({ bars });
    }
    unsubscribeFromUpdatedBars(updatedBars) {
        this.session.subscriptions.updatedBars =
            this.session.subscriptions.updatedBars.filter((updatedBar) => !updatedBars.includes(updatedBar));
        this.unsubscribe({ updatedBars });
    }
    unsubscriceFromDailyBars(dailyBars) {
        this.session.subscriptions.dailyBars = this.session.subscriptions.dailyBars.filter((dailyBar) => !dailyBars.includes(dailyBar));
        this.unsubscribe({ dailyBars });
    }
    unsubscribeFromOrderbooks(orderbooks) {
        this.session.subscriptions.orderbooks = this.session.subscriptions.orderbooks.filter((orderbook) => !orderbooks.includes(orderbook));
        this.unsubscribe({ orderbooks });
    }
    unsubscribe(symbols) {
        var _a, _b, _c, _d, _e, _f;
        const unsubMsg = {
            action: "unsubscribe",
            trades: (_a = symbols.trades) !== null && _a !== void 0 ? _a : [],
            quotes: (_b = symbols.quotes) !== null && _b !== void 0 ? _b : [],
            bars: (_c = symbols.bars) !== null && _c !== void 0 ? _c : [],
            updatedBars: (_d = symbols.updatedBars) !== null && _d !== void 0 ? _d : [],
            dailyBars: (_e = symbols.dailyBars) !== null && _e !== void 0 ? _e : [],
            orderbooks: (_f = symbols.orderbooks) !== null && _f !== void 0 ? _f : [],
        };
        this.conn.send(this.msgpack.encode(unsubMsg));
    }
    updateSubscriptions(msg) {
        this.session.subscriptions = {
            trades: msg.trades,
            quotes: msg.quotes,
            bars: msg.bars,
            updatedBars: msg.updatedBars,
            dailyBars: msg.dailyBars,
            orderbooks: msg.orderbooks,
        };
        this.log(`listening to streams:
        ${JSON.stringify(this.session.subscriptions)}`);
    }
    onCryptoTrade(fn) {
        this.on(websocket_1.EVENT.TRADES, (trade) => fn(trade));
    }
    onCryptoQuote(fn) {
        this.on(websocket_1.EVENT.QUOTES, (quote) => fn(quote));
    }
    onCryptoBar(fn) {
        this.on(websocket_1.EVENT.BARS, (bar) => fn(bar));
    }
    onCryptoUpdatedBar(fn) {
        this.on(websocket_1.EVENT.UPDATED_BARS, (updatedBar) => fn(updatedBar));
    }
    onCryptoDailyBar(fn) {
        this.on(websocket_1.EVENT.DAILY_BARS, (dailyBar) => fn(dailyBar));
    }
    onCryptoOrderbook(fn) {
        this.on(websocket_1.EVENT.ORDERBOOKS, (orderbook) => fn(orderbook));
    }
    dataHandler(data) {
        data.forEach((element) => {
            if ("T" in element) {
                const eventType = eventTypeMap.get(element.T);
                if (eventType) {
                    this.emit(eventType.event, eventType.parse(element));
                }
                else {
                    this.emit(websocket_1.EVENT.CLIENT_ERROR, websocket_1.ERROR.UNEXPECTED_MESSAGE);
                }
            }
        });
    }
}
exports.AlpacaCryptoClient = AlpacaCryptoClient;
