### Poor Man's Covered Call (Diagonal Spread)

The Poor Man's Covered Call, also known as a diagonal spread, is a cost-effective alternative to a traditional covered call strategy. It allows traders to generate income from premium collection without owning the underlying stock outright, using a long-term equity anticipation security (LEAPS) call as collateral instead. This strategy leverages inefficiencies in options pricing chains to create a low-capital entry point, making it accessible for smaller accounts. It's mildly bullish or neutral, profiting from time decay (theta) on the short call while the long LEAPS provides leverage for moderate upside moves. The key benefit is that the stock doesn't need to rise significantly for profitability over time, as premiums from repeated short calls can recover the LEAPS cost.

#### How to Use It
1. **Buy a Cheap LEAPS Call**: Select a deep in-the-money (ITM) LEAPS call with an expiration of at least one year, ideally inexpensive (e.g., low premium relative to strike). Choose on down days for better pricing, aiming for pricing inefficiencies where long-term options are undervalued. The difference between this LEAPS strike and planned short calls should fit your account's collateral needs.
2. **Sell a Short-Term Call Against the LEAPS**: Pick a strike price you don't expect the stock to reach by expiration (out-of-the-money or OTM). Sell on up days for higher premiums. This creates the diagonal spread.
3. **Set Stock Alarms**: Notify when the stock rises to $1-2 below the short call's strike to manage risk.
4. **Manage Based on Price Movement**:
   - **If Price Dips Well Below Short Call**: Roll the short call in (lower strike) or out (later expiration) for more premium, or let it expire worthless and sell another. Repeat the process with new alarms.
   - **If Price Rises Toward Short Call Strike**: Roll up (higher strike) and out, or buy 100 shares to cover, turning it into a covered call. Consider selling another call to restart, or sell the LEAPS for profit if no longer in spread.
5. **Exit/Recovery**: If LEAPS doesn't profit, sell calls against it until expiration to recover costs. Sell LEAPS anytime in profit by buying a replacement call first.

#### Risks and Rewards
- **Rewards**: Low capital requirement (vs. buying 100 shares), ongoing income from premiums, potential LEAPS appreciation. Example: Buy LEAPS for $72, sell short call for $71; net cost $1, but repeat sales yield 100% returns on net investment.
- **Risks**: Max loss is LEAPS cost minus premiums collected. Assignment on short call requires covering with shares, potentially at a loss if stock drops further. Time decay on LEAPS if held long-term without profits.
- **Best Market**: Neutral to mildly bullish, moderate volatility for premium inefficiencies.

### Bear Call Spread

The Bear Call Spread is a credit spread strategy for bearish or neutral outlooks, involving selling a call at a lower strike and buying a higher strike call for protection. It's a vertical spread profiting if the stock stays below the lower strike by expiration, allowing the trader to keep the net premium. The focus is on defined risk and avoiding assignment, with management via alarms and conversions to covered calls if needed. Advanced variations include turning it into "Erica’s Bullish Spread" if the stock rises.

#### How to Use It
1. **Set Up the Spread**: Buy an inexpensive upper leg call (protection) at a strike exceeding expected stock reach. Sell a lower leg call at a strike you don't think the stock will hit.
2. **Set Stock Alarm**: $1 below lower leg for $75-200 stocks, $2.50 for volatile/higher-priced, $5 for very high-priced. Aim to avoid buying shares.
3. **Manage Notifications**:
   - **No Alarm**: Wait for expiration or close for small cost if price lower.
   - **Alarm Triggers**: Buy 100 shares to cover lower leg (becomes covered call) or sell upper leg for profit.
4. **At Expiration**:
   - **Price Lower**: Allow covered call to expire, sell new one Monday, or rollout.
   - **Price Higher**: Allow shares called away if above strike; then expire or rollout.
5. **Advanced**: If stock doesn't rise, it can become a Bear Call Spread; if rises, Erica’s Bullish Spread. Sell upper leg for small premiums, keep shares for covered calls, or capture cap gains on assignment.

#### Risks and Rewards
- **Rewards**: Credit received upfront (e.g., net $240 on $350 sell/$200 buy); defined max profit ~60-70% POP in bearish setups. Multiple income streams: setup premium, upper leg sale, cap gains.
- **Risks**: Defined max loss (strike difference minus credit, e.g., $760). Assignment risk if breached, leading to covered call with potential further loss if stock drops.
- **Best Market**: Bearish/neutral, high IV for credits, avoid extreme volatility.

### Rollout (and In)

Rollout (and Roll In) is an adjustment technique for managing options positions, particularly covered calls or spreads. It involves closing the current contract and opening a new one with a higher strike ("up"), later expiration ("out"), lower strike ("in"), or combinations, often as a single multi-leg order. The goal is to collect more premium, extend time, or lock profits while reducing risk. Roll In is still called "rollout" in software but moves closer in time/price.

#### How to Use It
1. **Identify Need**: Use when stock rises (roll up/out to capture upside), near ex-dividend (extend for payout), or to adjust losing positions (roll in for more premium).
2. **Execute as One Order**: Select "Rollout" in broker software. Buy to close current (pay premium), sell to open new (receive premium). Aim for net credit.
3. **Rollout Types**:
   - **Out**: Extend expiration for more time/premium (e.g., from 3 weeks to another 3).
   - **Up**: Raise strike for upside potential.
   - **In**: Lower strike or closer expiration for aggressive plays or premium collection (e.g., from $122 to $120 on drop).
4. **Examples**:
   - Bought shares $120, sold $122 call for $275 (3 weeks). Stock drops to $119 after 2 weeks: Roll in to $120, collect $65 more.
   - Nearing expiration, low value: Roll out 2-3 weeks on green day for higher premium.
   - Avoid if deep ITM (buy to close too costly, e.g., >$1000): Accept loss.
5. **Volatility/Theta Considerations**: Volatile stocks offer flexibility; roll near expiration to leverage theta decay.

#### Risks and Rewards
- **Rewards**: Turns potential losses into winners, collects additional premium (e.g., $93 net credit). Extends profitable trades.
- **Risks**: Commissions, net debit if premiums unfavorable. Stuck in declining stock if over-rolled.
- **Best Market**: Any, as adjustment tool; volatile for premium flexibility.

### LEAPS (Long Term Equity Anticipation Securities)

LEAPS are long-term call options with expirations over one year, providing leverage on stock movements without owning shares. They stand for Long Term Equity Anticipation Securities, offering less initial time decay than short-term calls. Used standalone for long-term bullish bets or as bases for spreads like Poor Man's Covered Call. Profit without hitting strike; sell before expiration. The "P" origin is a mystery.

#### How to Use It
1. **Buy LEAPS**: Select call with >1 year expiration, strike based on bullish outlook. Buy on down days for better pricing.
2. **Strategies**:
   - **Standalone**: Hold for appreciation, sell in profit.
   - **Calendar Spread Backup**: If no profit, sell short calls against until expiration to recover cost.
   - **Earnings Trade**: Buy before earnings, hold after if positive, sell on quick rise.
   - **Volatility Trade**: Buy/sell for quick profits (e.g., $100-500 gain on $2-10 stock move).
3. **Exit**: Sell anytime in profit; hedge losses with predetermined exit (e.g., limit order). For spreads, buy replacement call first.
4. **Hedge Losses**: Sell calls against declining LEAPS to recover; use >500-day for less theta.
5. **Examples**: AAPL LEAPS $2,628 buy, sell at $3,300 (+25.5% in month). AMZN $1,935 buy, drops to $1,875 (-3.1%).

#### Risks and Rewards
- **Rewards**: Leverage (e.g., $1800 for $15000 stock exposure), large profits on less capital (8 LEAPS = 1 lot gains).
- **Risks**: Full loss if expires worthless; theta near end; bearish markets cause value drop.
- **Best Market**: Long-term bullish, low IV entry.

### The Wheel

The Wheel is a popular income strategy combining cash-secured puts and covered calls in a cycle. Traders sell puts (potentially getting assigned shares), then covered calls on those shares until called away, repeating for ongoing premiums. Enter via put or call; ideal for stocks you'd own. Challenging entry but simple cycle.

#### How to Use It
1. **Enter via Put**: Sell cash-secured put on desired stock. If expires OTM: Collect premium, repeat. If assigned: Own shares, proceed to calls.
2. **Enter via Call**: Buy shares, sell covered call. If expires OTM: Collect premium, repeat. If called away: Sell put with proceeds.
3. **Cycle**: Sell calls on assigned shares until called away, then puts again. Use rollouts if needed.
4. **Market Pairing**: Best in slowly rising/range-bound; avoid declining (losses). Creative: Buy on down days, sell calls on pops for cap gains.
5. **Risks if Drops**: Lower premiums; paper losses on assignment. Enter via put in bull markets to avoid assignment delays.

#### Risks and Rewards
- **Rewards**: Consistent income (premiums + cap gains); recycling effect.
- **Risks**: Capital tie-up, losses on declining stocks. Volatile markets amplify assignment risk.
- **Best Market**: Neutral-bullish, choppy for range trades.

### Wealth Building (From Income to Wealth)

This strategy focuses on reinvesting options premiums to transition from short-term income to long-term wealth. Master compounding by allocating profits to diversified assets, balancing risk and growth.

#### How to Use It
1. **Fund More Options**: Reinvest to build positions (e.g., to 100 shares for calls/LEAPS). High risk/volatility; expect portfolio swings. Best for cash flow seekers.
2. **Buy Long-Term Holds**: Purchase quality stocks (e.g., MSFT, AAPL) with strong 5-year charts, preferably dividends. Allocate 80% dividend growth, 20% non-dividend. Review earnings; sell after 2 bad calls. Trade lightly (OTM calls).
3. **Build Dividend Portfolio**: Buy high-yield stocks/ETFs (e.g., O 5.16%, JEPQ 10%). Monthly/quarterly income; lower taxes on qualified dividends. Rotate for steady payouts.
4. **Build Cash Reserves**: Hold cash as position for opportunities/bear markets. Acts as backup to trade lows.
5. **Taxes/Allocation**: Set aside taxes quarterly; diversify to mitigate risk.

#### Risks and Rewards
- **Rewards**: Compounded growth (e.g., 5% premium +54% stock gain). Passive income from dividends.
- **Risks**: Market-dependent; high volatility in options funding. Caps gains in dividends.
- **Best Market**: Any; adjust allocation per tolerance.

### Calendar Spreads

Calendar spreads are time-based (horizontal/diagonal) strategies profiting from theta differential between short-term sell and long-term buy at same strike. Neutral, range-bound; evolve with LEAPS for Poor Man's Covered Call.

#### How to Use It
1. **Setup**: Buy long-term call (e.g., LEAPS), sell short-term at same strike.
2. **Strategy**: Profit from faster short leg decay. Set alarms to avoid assignment.
3. **Buy to Cover**: If short leg threatened, buy shares or close.
4. **Evolving LEAPS**: Use as backup if LEAPS unprofitable; sell shorts against.
5. **Exit Options**: Repurchase short, buy new upper, or buy shares (becomes covered call).
6. **Example**: Buy $250 AAPL 1/19/25 for $72, sell $190 12/22/23 for $71 (net $1); repeat shorts for 100% returns.

#### Risks and Rewards
- **Rewards**: Low net cost, recurring premiums; recycle long leg.
- **Risks**: Net debit if unfavorable; assignment requires capital.
- **Best Market**: Sideways, high front IV.

### Cash-Secured Puts

Cash-secured puts involve selling puts backed by cash for assignment (100 shares). Bullish/neutral; generate income or acquire stocks at discount. Willingness to own is key.

#### How to Use It
1. **Sell Put**: On stock you'd buy, reserve cash = strike x100. Choose strike/expiration for premium.
2. **Strategies**:
   - **Fast Money**: Sell near current price for high premium, aim for assignment.
   - **Ideal Price**: Sell at desired buy price; roll if not assigned.
   - **Wheel Entry**: Sell to get assigned, then covered calls.
   - **Avoidance**: Low delta (.10-.20) or below support for lower premiums/risk.
3. **Manage**: Buy to close/roll if unwanted assignment. Sell on down days.
4. **Factors**: Volatility/expiration/earnings/stock cost affect premiums.

#### Risks and Rewards
- **Rewards**: Premium income ($62-$500 examples); discounted shares.
- **Risks**: Assignment at loss if drops further; capital tie-up.
- **Best Market**: Bullish, high IV for premiums.

### Covered Call

Covered calls involve selling calls against owned stock (100 shares/contract) to collect premiums, enhancing yield but capping upside. Basic income; integrate with Wheel/rollouts. "New" likely refers to updated strategies like FAST MONEY (high premium near price).

#### How to Use It
1. **Own Shares**: Buy 100+ of bullish/neutral stock.
2. **Sell Call**: OTM strike you don't expect hit; collect premium.
3. **Manage**: Roll if approaches strike; if expires OTM, repeat. If ITM, shares called away + premium.
4. **Variations**: FAST MONEY (sell near current for high premium); light trading on holds.
5. **Examples**: From Rollouts - sell $122 on $120 shares for $275; roll in on drop.

#### Risks and Rewards
- **Rewards**: Income boost (e.g., $275 on 3 weeks); cap gains if called.
- **Risks**: Capped upside, losses if stock drops (offset by premium).
- **Best Market**: Neutral-mild bullish, moderate IV.


Objective and Scope

Purpose: What the daily task must produce (an actionable options playbook with strikes and triggers).

Scope: Which symbols to analyze (point to stock_tickers.txt if you’ll keep tickers there).

Example:

Purpose: Generate a daily, actionable options playbook for my watchlist using the strategies below.

Scope: Analyze tickers listed in watchlist.txt. If missing, analyze top 5 liquid mega-caps.

Strategy Rules (authoritative, see above strategies)

Copy your finalized rules for:

Poor Man’s Covered Call (when to use, entry/exit, rolls)

Bear Call Spread

Calendar Spreads

Cash-Secured Puts

Covered Calls

LEAPS

The Wheel

Rollout/Roll In procedures

For each, include:

Market regime fit (neutral, mild bullish, high IV, etc.)

Setup checklist (price action, IV rank, delta targets)

Entry rules (signals/levels)

Exit rules (profit target, time stop)

Fallback/Roll logic (exact if-then steps)

Example structure (legs, expirations, target credit/debit)

Strike Selection Framework

Delta targets: e.g., short strikes aim 0.15–0.30 delta unless noted.

IV/IVR: prefer IVR > 40 for credit spreads; avoid entering right before earnings unless intended.

Support/Resistance: prefer strikes beyond key support/resistance; allow +/- one strike if IV shifts.

Time to expiration:

Income spreads: 14–45 DTE

Covered calls: 7–21 DTE

Calendars: short leg 7–14 DTE; long leg 60+ DTE or LEAPS if diagonal

LEAPS: 300–700 DTE

Earnings/dividends: avoid short legs crossing earnings unless compensated; note ex-dividend risks.

Ticker Playbook Template (exact output format you want)

For the model to follow, include the template it must fill per ticker:

Template:

Timeframe: intraday / 1–3 days / 2–3 weeks / LEAPS horizon

Strategy: pick 1–2 from the approved list with reason tied to market regime

Strike Guidance:

Primary setup: [strategy name]

Short strike(s): [level(s)], target delta, rationale (S/R, IVR)

Long/protective leg(s): [level(s)], expiry

Alternatives: +/- 1 strike if price/IV shifts

Trade Trigger:

If price hits [level] or condition [breadth/IV/news], then place:

Order: [all legs, expiries, target credit/debit, min POP]

Exit Plan:

Take profit at [X%] or by [date/time]; time-based exit rule

Fallback (Adverse Move):

If short strike threatened: roll up/out/in for net credit when possible

If deep ITM and expensive to close: convert per rule (e.g., covered call/wheel)

If thesis invalidated: close at max loss cap [define]

Key Dates:

Earnings/dividend dates and handling plan

Alerts:

Set price alerts at: [levels]

Market Regime Map (when to use each strategy)

Neutral to mildly bullish, moderate IV: prioritize Poor Man’s Covered Call, Calendars, Covered Calls.

Bearish/neutral with high IV: prioritize Bear Call Spreads, CS put only at desired entries.

Strong bullish trend: LEAPS entries on down days; covered calls lightly OTM.

Choppy range: Wheel and short-duration covered calls; roll frequently.

Include examples like:

If IVR > 50 and price below resistance: prefer Bear Call Spread with 20–30 delta short.

If IVR < 20 and mild uptrend: prefer Calendars/PMCC.

Roll/Adjustment Playbook (step-by-step)

General:

Roll near expiration on green days for better credits.

Aim for net credit rolls; if not possible with minimal impact, reduce size/risk.

Examples:

Covered Call near strike with 3 days left: roll out 2–3 weeks, up 1–2 strikes for credit on up day.

Bear Call threatened: buy 100 shares to convert to covered call, or roll the short leg up/out; consider selling upper leg for small profit if available.

PMCC: if short gets threatened, roll up/out; if deep ITM and expensive to close, consider converting to covered call by buying shares or closing LEAPS if profitable.

Risk Management Rules

Position sizing: max risk per trade [% portfolio] or $ amount.

Max loss per strategy:

Verticals: width minus credit, cap at [$]

PMCC: net LEAPS cost minus premiums; cap [%]

CSP: define acceptable assignment and plan if assigned

Portfolio limits: max number of concurrent short premium trades; correlation caps.

No-trade windows: avoid entering new positions X minutes before major news/FOMC.

Output Requirements for the Daily Report

Sections required:

Big Money Flow

Prognostication

Strategy Filter

Ticker Playbooks (fill the template)

Risk Summary

Alert Checklist and Prioritized Action List

Formatting:

Bullets, concise; each ticker under ~200 words.

Missing data handling:

If any file missing, proceed and list missing items at top.

Delivery: post full output to task result.

File References and Dependencies

Files the task will read:

Ericassummary.txt (this file; rulebook)

watchlist.txt (tickers; one per line; optional notes like “avoid earnings week”)

macro_notes.txt (catalysts; optional)

If a file isn’t found, instruct to proceed with what is available and note which are missing.

Examples and Snippets (optional but helpful)

Include a couple of fully worked examples for one ticker each:

One neutral-high IV case using Bear Call Spread

One mild-bull case using PMCC or Calendar

Show exact order tickets with legs, expirations, and target credits.

If you add this structure to Ericassummary.txt, your Perplexity Task can reliably:

Read your rules

Map today’s regime to the right strategy

Propose concrete strikes

Give entry/exit/fallback steps

Push a clean notification to you each morning