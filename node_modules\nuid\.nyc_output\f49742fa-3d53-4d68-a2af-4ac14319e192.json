{"/Users/<USER>/Dropbox/code/src/github.com/nats-io/node-nuid/lib/nuid.js": {"path": "/Users/<USER>/Dropbox/code/src/github.com/nats-io/node-nuid/lib/nuid.js", "statementMap": {"0": {"start": {"line": 22, "column": 15}, "end": {"line": 22, "column": 32}}, "1": {"start": {"line": 27, "column": 15}, "end": {"line": 27, "column": 53}}, "2": {"start": {"line": 28, "column": 13}, "end": {"line": 28, "column": 15}}, "3": {"start": {"line": 29, "column": 15}, "end": {"line": 29, "column": 17}}, "4": {"start": {"line": 30, "column": 15}, "end": {"line": 30, "column": 17}}, "5": {"start": {"line": 31, "column": 15}, "end": {"line": 31, "column": 31}}, "6": {"start": {"line": 32, "column": 15}, "end": {"line": 32, "column": 17}}, "7": {"start": {"line": 33, "column": 15}, "end": {"line": 33, "column": 18}}, "8": {"start": {"line": 34, "column": 17}, "end": {"line": 34, "column": 32}}, "9": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 52}}, "10": {"start": {"line": 45, "column": 2}, "end": {"line": 45, "column": 35}}, "11": {"start": {"line": 46, "column": 2}, "end": {"line": 46, "column": 13}}, "12": {"start": {"line": 56, "column": 0}, "end": {"line": 60, "column": 1}}, "13": {"start": {"line": 57, "column": 2}, "end": {"line": 57, "column": 15}}, "14": {"start": {"line": 58, "column": 2}, "end": {"line": 58, "column": 22}}, "15": {"start": {"line": 59, "column": 2}, "end": {"line": 59, "column": 16}}, "16": {"start": {"line": 68, "column": 0}, "end": {"line": 71, "column": 1}}, "17": {"start": {"line": 69, "column": 2}, "end": {"line": 69, "column": 47}}, "18": {"start": {"line": 70, "column": 2}, "end": {"line": 70, "column": 69}}, "19": {"start": {"line": 79, "column": 0}, "end": {"line": 85, "column": 1}}, "20": {"start": {"line": 80, "column": 15}, "end": {"line": 80, "column": 41}}, "21": {"start": {"line": 81, "column": 2}, "end": {"line": 84, "column": 3}}, "22": {"start": {"line": 81, "column": 15}, "end": {"line": 81, "column": 16}}, "23": {"start": {"line": 82, "column": 15}, "end": {"line": 82, "column": 29}}, "24": {"start": {"line": 83, "column": 4}, "end": {"line": 83, "column": 39}}, "25": {"start": {"line": 93, "column": 0}, "end": {"line": 99, "column": 1}}, "26": {"start": {"line": 94, "column": 10}, "end": {"line": 94, "column": 18}}, "27": {"start": {"line": 95, "column": 2}, "end": {"line": 98, "column": 3}}, "28": {"start": {"line": 95, "column": 15}, "end": {"line": 95, "column": 27}}, "29": {"start": {"line": 96, "column": 4}, "end": {"line": 96, "column": 45}}, "30": {"start": {"line": 97, "column": 4}, "end": {"line": 97, "column": 28}}, "31": {"start": {"line": 107, "column": 0}, "end": {"line": 115, "column": 1}}, "32": {"start": {"line": 108, "column": 2}, "end": {"line": 108, "column": 22}}, "33": {"start": {"line": 109, "column": 2}, "end": {"line": 112, "column": 3}}, "34": {"start": {"line": 110, "column": 4}, "end": {"line": 110, "column": 17}}, "35": {"start": {"line": 111, "column": 4}, "end": {"line": 111, "column": 24}}, "36": {"start": {"line": 113, "column": 2}, "end": {"line": 113, "column": 16}}, "37": {"start": {"line": 114, "column": 2}, "end": {"line": 114, "column": 37}}, "38": {"start": {"line": 118, "column": 10}, "end": {"line": 118, "column": 20}}, "39": {"start": {"line": 127, "column": 0}, "end": {"line": 129, "column": 1}}, "40": {"start": {"line": 128, "column": 2}, "end": {"line": 128, "column": 10}}, "41": {"start": {"line": 137, "column": 0}, "end": {"line": 139, "column": 1}}, "42": {"start": {"line": 138, "column": 2}, "end": {"line": 138, "column": 17}}, "43": {"start": {"line": 145, "column": 0}, "end": {"line": 147, "column": 1}}, "44": {"start": {"line": 146, "column": 2}, "end": {"line": 146, "column": 10}}}, "fnMap": {"0": {"name": "Nuid", "decl": {"start": {"line": 44, "column": 9}, "end": {"line": 44, "column": 13}}, "loc": {"start": {"line": 44, "column": 17}, "end": {"line": 47, "column": 1}}, "line": 44}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 56, "column": 22}, "end": {"line": 56, "column": 23}}, "loc": {"start": {"line": 56, "column": 34}, "end": {"line": 60, "column": 1}}, "line": 56}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 68, "column": 31}, "end": {"line": 68, "column": 32}}, "loc": {"start": {"line": 68, "column": 43}, "end": {"line": 71, "column": 1}}, "line": 68}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 79, "column": 24}, "end": {"line": 79, "column": 25}}, "loc": {"start": {"line": 79, "column": 36}, "end": {"line": 85, "column": 1}}, "line": 79}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 93, "column": 25}, "end": {"line": 93, "column": 26}}, "loc": {"start": {"line": 93, "column": 37}, "end": {"line": 99, "column": 1}}, "line": 93}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 107, "column": 22}, "end": {"line": 107, "column": 23}}, "loc": {"start": {"line": 107, "column": 34}, "end": {"line": 115, "column": 1}}, "line": 107}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 127, "column": 16}, "end": {"line": 127, "column": 17}}, "loc": {"start": {"line": 127, "column": 28}, "end": {"line": 129, "column": 1}}, "line": 127}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 137, "column": 15}, "end": {"line": 137, "column": 16}}, "loc": {"start": {"line": 137, "column": 27}, "end": {"line": 139, "column": 1}}, "line": 137}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 145, "column": 24}, "end": {"line": 145, "column": 25}}, "loc": {"start": {"line": 145, "column": 36}, "end": {"line": 147, "column": 1}}, "line": 145}}, "branchMap": {"0": {"loc": {"start": {"line": 109, "column": 2}, "end": {"line": 112, "column": 3}}, "type": "if", "locations": [{"start": {"line": 109, "column": 2}, "end": {"line": 112, "column": 3}}, {"start": {"line": 109, "column": 2}, "end": {"line": 112, "column": 3}}], "line": 109}}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 1, "6": 1, "7": 1, "8": 1, "9": 1, "10": 1, "11": 1, "12": 1, "13": 2, "14": 2, "15": 2, "16": 1, "17": 3, "18": 3, "19": 1, "20": 3, "21": 3, "22": 3, "23": 36, "24": 36, "25": 1, "26": 10004, "27": 10004, "28": 10004, "29": 100040, "30": 100040, "31": 1, "32": 10002, "33": 10002, "34": 1, "35": 1, "36": 10002, "37": 10002, "38": 1, "39": 1, "40": 1, "41": 1, "42": 10002, "43": 1, "44": 8}, "f": {"0": 1, "1": 2, "2": 3, "3": 3, "4": 10004, "5": 10002, "6": 1, "7": 10002, "8": 8}, "b": {"0": [1, 10001]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "6691d4951d80a1d56ae12ecf38cc67bb5020b775", "contentHash": "eef4bd3e425f19c2c62e5cbf5e79b3407c8bb9eab46f32796b5223e7574849d8"}}