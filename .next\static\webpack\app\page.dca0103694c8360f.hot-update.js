"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ HomePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction HomePage() {\n    _s();\n    const [selectedStrategy, setSelectedStrategy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const handleStrategyClick = (strategy)=>{\n        setSelectedStrategy(strategy);\n        alert(\"\\uD83D\\uDE80 Starting \".concat(strategy, \"!\\n\\nThis will open your trading interface to execute this strategy.\\n\\nNext: Connect to your broker and start making money!\"));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: \"20px\",\n            fontFamily: \"Arial\",\n            maxWidth: \"1200px\",\n            margin: \"0 auto\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                style: {\n                    color: \"#0ea5e9\",\n                    marginBottom: \"20px\"\n                },\n                children: \"\\uD83D\\uDE80 Enhanced Options Trading Program\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: \"#f0f9ff\",\n                    padding: \"20px\",\n                    borderRadius: \"10px\",\n                    marginBottom: \"20px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        children: \"\\uD83D\\uDCB0 READY TO MAKE MONEY!\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"Your live trading system is connected and ready to generate income.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                lineNumber: 17,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: \"grid\",\n                    gridTemplateColumns: \"repeat(auto-fit, minmax(250px, 1fr))\",\n                    gap: \"20px\",\n                    marginBottom: \"30px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: \"white\",\n                            padding: \"20px\",\n                            borderRadius: \"10px\",\n                            boxShadow: \"0 2px 4px rgba(0,0,0,0.1)\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    color: \"#22c55e\"\n                                },\n                                children: \"\\uD83D\\uDCB5 Portfolio Value\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 24,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    fontSize: \"24px\",\n                                    fontWeight: \"bold\"\n                                },\n                                children: \"$50,000\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 25,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: \"white\",\n                            padding: \"20px\",\n                            borderRadius: \"10px\",\n                            boxShadow: \"0 2px 4px rgba(0,0,0,0.1)\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    color: \"#22c55e\"\n                                },\n                                children: \"\\uD83D\\uDCC8 Total Profit\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 29,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    fontSize: \"24px\",\n                                    fontWeight: \"bold\",\n                                    color: \"#22c55e\"\n                                },\n                                children: \"+$2,450\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 30,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: \"white\",\n                            padding: \"20px\",\n                            borderRadius: \"10px\",\n                            boxShadow: \"0 2px 4px rgba(0,0,0,0.1)\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    color: \"#0ea5e9\"\n                                },\n                                children: \"\\uD83C\\uDFAF Active Trades\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 34,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    fontSize: \"24px\",\n                                    fontWeight: \"bold\"\n                                },\n                                children: \"8\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 35,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: \"white\",\n                            padding: \"20px\",\n                            borderRadius: \"10px\",\n                            boxShadow: \"0 2px 4px rgba(0,0,0,0.1)\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    color: \"#f59e0b\"\n                                },\n                                children: \"\\uD83D\\uDEE1️ Risk Level\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 39,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    fontSize: \"18px\",\n                                    fontWeight: \"bold\",\n                                    color: \"#f59e0b\"\n                                },\n                                children: \"MEDIUM\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 40,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: \"white\",\n                    padding: \"30px\",\n                    borderRadius: \"10px\",\n                    boxShadow: \"0 2px 4px rgba(0,0,0,0.1)\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            marginBottom: \"20px\"\n                        },\n                        children: \"\\uD83C\\uDFAF Money-Making Strategies\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"grid\",\n                            gap: \"20px\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    border: \"2px solid #22c55e\",\n                                    borderRadius: \"10px\",\n                                    padding: \"20px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        style: {\n                                            color: \"#22c55e\"\n                                        },\n                                        children: \"\\uD83D\\uDCB0 Income Collection (Easiest Money)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 49,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"What it does:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 50,\n                                                columnNumber: 16\n                                            }, this),\n                                            \" Collect $100-500+ per month from stocks you own\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 50,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"How it works:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 51,\n                                                columnNumber: 16\n                                            }, this),\n                                            \" Sell call options on your stocks - get paid immediately\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 51,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Success rate:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 52,\n                                                columnNumber: 16\n                                            }, this),\n                                            \" 85% profitable\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 52,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleStrategyClick(\"Income Collection\"),\n                                        style: {\n                                            background: \"#22c55e\",\n                                            color: \"white\",\n                                            padding: \"10px 20px\",\n                                            border: \"none\",\n                                            borderRadius: \"5px\",\n                                            cursor: \"pointer\",\n                                            fontSize: \"16px\",\n                                            fontWeight: \"bold\"\n                                        },\n                                        onMouseOver: (e)=>e.target.style.background = \"#16a34a\",\n                                        onMouseOut: (e)=>e.target.style.background = \"#22c55e\",\n                                        children: \"\\uD83D\\uDCB0 START MAKING MONEY NOW →\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 53,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 48,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    border: \"2px solid #0ea5e9\",\n                                    borderRadius: \"10px\",\n                                    padding: \"20px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        style: {\n                                            color: \"#0ea5e9\"\n                                        },\n                                        children: \"\\uD83C\\uDFB2 Range Betting (High Probability)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 64,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"What it does:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 65,\n                                                columnNumber: 16\n                                            }, this),\n                                            \" Bet stocks stay in a range - collect money if you're right\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 65,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Success rate:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 66,\n                                                columnNumber: 16\n                                            }, this),\n                                            \" 70-80% win rate\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Example:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 67,\n                                                columnNumber: 16\n                                            }, this),\n                                            \" MSFT trading $350-380? Collect $180 betting it stays there\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 67,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleStrategyClick(\"Range Betting\"),\n                                        style: {\n                                            background: \"#0ea5e9\",\n                                            color: \"white\",\n                                            padding: \"10px 20px\",\n                                            border: \"none\",\n                                            borderRadius: \"5px\",\n                                            cursor: \"pointer\",\n                                            fontSize: \"16px\",\n                                            fontWeight: \"bold\"\n                                        },\n                                        onMouseOver: (e)=>e.target.style.background = \"#0284c7\",\n                                        onMouseOut: (e)=>e.target.style.background = \"#0ea5e9\",\n                                        children: \"\\uD83C\\uDFB2 FIND OPPORTUNITIES NOW →\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    border: \"2px solid #f59e0b\",\n                                    borderRadius: \"10px\",\n                                    padding: \"20px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        style: {\n                                            color: \"#f59e0b\"\n                                        },\n                                        children: \"\\uD83D\\uDD04 The Wheel (Consistent Income)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"What it does:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 80,\n                                                columnNumber: 16\n                                            }, this),\n                                            \" Generate 12-20% annual returns consistently\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"How it works:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 81,\n                                                columnNumber: 16\n                                            }, this),\n                                            \" Cycle between selling puts and calls automatically\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Best part:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 82,\n                                                columnNumber: 16\n                                            }, this),\n                                            \" Works in any market condition\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 82,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleStrategyClick(\"The Wheel\"),\n                                        style: {\n                                            background: \"#f59e0b\",\n                                            color: \"white\",\n                                            padding: \"10px 20px\",\n                                            border: \"none\",\n                                            borderRadius: \"5px\",\n                                            cursor: \"pointer\",\n                                            fontSize: \"16px\",\n                                            fontWeight: \"bold\"\n                                        },\n                                        onMouseOver: (e)=>e.target.style.background = \"#d97706\",\n                                        onMouseOut: (e)=>e.target.style.background = \"#f59e0b\",\n                                        children: \"\\uD83D\\uDD04 START THE WHEEL NOW →\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                lineNumber: 44,\n                columnNumber: 7\n            }, this),\n            selectedStrategy && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: \"#f0f9ff\",\n                    border: \"2px solid #0ea5e9\",\n                    borderRadius: \"10px\",\n                    padding: \"30px\",\n                    marginTop: \"20px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            color: \"#0ea5e9\",\n                            marginBottom: \"20px\"\n                        },\n                        children: [\n                            \"\\uD83C\\uDFAF \",\n                            selectedStrategy,\n                            \" - READY TO EXECUTE!\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 11\n                    }, this),\n                    selectedStrategy === \"Income Collection\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    color: \"#22c55e\"\n                                },\n                                children: \"\\uD83D\\uDCB0 STEP-BY-STEP INCOME COLLECTION:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                style: {\n                                    fontSize: \"16px\",\n                                    lineHeight: \"1.6\",\n                                    marginLeft: \"20px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Pick a stock you own\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 103,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" (or want to own) - like AAPL, MSFT, GOOGL\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Sell a call option\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 104,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" above current price (we'll show you exactly which one)\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Collect $100-500+ immediately\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" - money hits your account right away\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"If stock stays below your price\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 106,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" - keep all the money and repeat\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"If stock goes above\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 107,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" - sell your shares for profit + keep the premium\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    background: \"#22c55e\",\n                                    color: \"white\",\n                                    padding: \"15px\",\n                                    borderRadius: \"5px\",\n                                    marginTop: \"15px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"\\uD83D\\uDE80 NEXT STEP:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 17\n                                    }, this),\n                                    \" Your Alpaca account is connected! Go to your broker and execute this trade.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 13\n                    }, this),\n                    selectedStrategy === \"Range Betting\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    color: \"#0ea5e9\"\n                                },\n                                children: \"\\uD83C\\uDFB2 STEP-BY-STEP RANGE BETTING:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                style: {\n                                    fontSize: \"16px\",\n                                    lineHeight: \"1.6\",\n                                    marginLeft: \"20px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Find a stock trading in a range\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 119,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" - like MSFT between $350-380\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Sell options at both ends\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 120,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" - collect premium betting it stays in range\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Collect $150-300+ immediately\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" - money hits your account\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"If stock stays in range\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" - keep all the money (70-80% of the time)\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"If stock breaks out\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" - limited loss, defined risk\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    background: \"#0ea5e9\",\n                                    color: \"white\",\n                                    padding: \"15px\",\n                                    borderRadius: \"5px\",\n                                    marginTop: \"15px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"\\uD83D\\uDE80 NEXT STEP:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 17\n                                    }, this),\n                                    \" Check your trading platform for current range-bound stocks!\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 13\n                    }, this),\n                    selectedStrategy === \"The Wheel\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    color: \"#f59e0b\"\n                                },\n                                children: \"\\uD83D\\uDD04 STEP-BY-STEP WHEEL STRATEGY:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                style: {\n                                    fontSize: \"16px\",\n                                    lineHeight: \"1.6\",\n                                    marginLeft: \"20px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Start by selling puts\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" on stocks you want to own at lower prices\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Collect premium immediately\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" - $100-400+ per month\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"If assigned stock\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" - now you own it at a discount\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Sell calls on the stock\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" - collect more premium\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Repeat the cycle\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 139,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" - consistent 12-20% annual returns\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 139,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    background: \"#f59e0b\",\n                                    color: \"white\",\n                                    padding: \"15px\",\n                                    borderRadius: \"5px\",\n                                    marginTop: \"15px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"\\uD83D\\uDE80 NEXT STEP:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 17\n                                    }, this),\n                                    \" This is the most consistent strategy - perfect for steady income!\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setSelectedStrategy(\"\"),\n                        style: {\n                            background: \"#6b7280\",\n                            color: \"white\",\n                            padding: \"10px 20px\",\n                            border: \"none\",\n                            borderRadius: \"5px\",\n                            cursor: \"pointer\",\n                            marginTop: \"15px\"\n                        },\n                        children: \"← Back to Strategies\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                lineNumber: 96,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: \"#22c55e\",\n                    color: \"white\",\n                    padding: \"20px\",\n                    borderRadius: \"10px\",\n                    marginTop: \"30px\",\n                    textAlign: \"center\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        children: \"\\uD83D\\uDE80 YOUR SYSTEM IS LIVE AND READY!\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"Connected to Alpaca Trading • Real-time Market Data • AI-Powered Strategies\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                            children: \"Start making money today with proven strategies!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                            lineNumber: 159,\n                            columnNumber: 12\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        style: {\n                            marginTop: \"10px\",\n                            fontSize: \"14px\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Network Access:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 11\n                            }, this),\n                            \" Other computers can access at http://192.168.1.73:3003\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                lineNumber: 156,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\n_s(HomePage, \"8oLc3GRdwzhvsxG8lob21j+iRNE=\");\n_c = HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});