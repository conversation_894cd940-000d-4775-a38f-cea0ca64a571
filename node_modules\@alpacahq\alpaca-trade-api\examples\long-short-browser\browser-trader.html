<!DOCTYPE html>
<head>
  <meta charset="UTF-8">
  <title>In-Browser Trader</title>

  <!-- Bootstrap files -->
  <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css" integrity="sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm" crossorigin="anonymous">
  <script src="https://code.jquery.com/jquery-3.2.1.slim.min.js" integrity="sha384-KJ3o2DKtIkvYIK3UENzmM7KCkRr/rE9/Qpg6aAZGJwFDMVNA/GpGFF93hXpG5KkN" crossorigin="anonymous"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.12.9/umd/popper.min.js" integrity="sha384-ApNbgh9B+Y1QKtv3Rn7W3mgPxhU9K/ScQsAP7hUibX39j7fakFPskvXusvfa0b4Q" crossorigin="anonymous"></script>
  <script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/js/bootstrap.min.js" integrity="sha384-JZR6Spejh4U02d8jOt6vLEHfe/JQGiRRSQQxSfFWpi1MquVdAyjUar5+76PVCmYl" crossorigin="anonymous"></script>
  
  <!-- Local files -->
  <link rel="stylesheet" type="text/css" href="browser-trader-style.css"/>
  <script src="../../lib/utils/Chart.bundle.js"></script>
  <script src="long-short-browser.js"></script>
  <script src="../../lib/cors-api.js"></script>
</head>
<body>
  <div id="title-container">
    <h1>Long-Short Equity Trading Algorithm</h1>
  </div>
  <div id="data-container">
    <div id="side-container">
      <div id="positions">
        <h5 style="margin: 5px; font-weight: bold;">Positions</h5>
        <div id="positions-title">
          <p class="position-fragment">Symbol</p>
          <p class="position-fragment">Qty</p>
          <p class="position-fragment">Side</p>
          <p class="position-fragment">+/-</p>
        </div>
        <div id="positions-log">

        </div>
      </div>
      <div id="orders">
        <h5 style="margin: 5px; font-weight: bold;">Orders</h5>
        <div id="order-title">
          <p class="order-fragment">Symbol</p>
          <p class="order-fragment">Qty</p>
          <p class="order-fragment">Side</p>
          <p class="order-fragment">Type</p>
        </div>
        <div id="orders-log">

        </div>
      </div>
    </div>
    <div id="chart-container">
      <canvas id="main_chart"></canvas>
    </div>
    <div id="event-log">

    </div>
  </div>
  <div id="bottom-container">
    <button type="button" class="btn btn-success control-button" onclick="runScript();">
      Run script
    </button>
    <div id="input-container">
      <h5 id="input-title">Enter credentials here</h5>
      <input id="api-key" type="text" placeholder="API_KEY"/>
      <input id="api-secret" type="text" placeholder="API_SECRET"/>
    </div>
    <button type="button" class="btn btn-danger control-button" onclick="killScript();">
      Kill script
    </button>
  </div>
</body>