'use client';

import { useState, useEffect } from 'react';
import { TrendingUp, AlertTriangle, DollarSign, Target, BarChart3, Shield, Wifi, WifiOff } from 'lucide-react';
import { TradingDashboard } from '../components/TradingDashboard';
import { StrategyRecommendations } from '../components/StrategyRecommendations';
import { RiskMonitor } from '../components/RiskMonitor';
import { MarketAlerts } from '../components/MarketAlerts';
import { PositionManager } from '../components/PositionManager';
import { useMarketData } from '../lib/hooks/useMarketData';

export default function HomePage() {
  const [activeTab, setActiveTab] = useState('dashboard');

  // Use live market data
  const {
    account,
    stocks,
    marketOverview,
    loading,
    error,
    lastUpdated,
    isMarketOpen,
    refreshAll,
    startAutoRefresh,
    stopAutoRefresh
  } = useMarketData(['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'META', 'NVDA', 'JPM', 'JNJ', 'V']);

  // Portfolio metrics from live data or defaults
  const portfolioValue = account?.account?.portfolioValue || 50000;
  const totalProfit = account?.account?.equity ? (account.account.equity - 47550) : 2450;
  const activePositions = account?.positions?.length || 8;
  const riskLevel = portfolioValue > 0 ? (Math.abs(totalProfit) / portfolioValue > 0.1 ? 'HIGH' : 'MEDIUM') : 'LOW';

  // Auto-refresh data every minute when market is open
  useEffect(() => {
    if (isMarketOpen) {
      startAutoRefresh(60000); // 1 minute
    } else {
      startAutoRefresh(300000); // 5 minutes when market closed
    }

    return () => stopAutoRefresh();
  }, [isMarketOpen, startAutoRefresh, stopAutoRefresh]);

  const tabs = [
    { id: 'dashboard', name: 'Dashboard', icon: BarChart3 },
    { id: 'strategies', name: 'Smart Strategies', icon: Target },
    { id: 'positions', name: 'My Trades', icon: DollarSign },
    { id: 'alerts', name: 'Opportunities', icon: AlertTriangle },
    { id: 'risk', name: 'Safety Check', icon: Shield },
  ];

  const getRiskColor = (level: string) => {
    switch (level) {
      case 'LOW': return 'text-success-600 bg-success-100';
      case 'MEDIUM': return 'text-warning-600 bg-warning-100';
      case 'HIGH': return 'text-danger-600 bg-danger-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  return (
    <div className="space-y-6">
      {/* Connection Status */}
      {error && (
        <div className="bg-danger-50 border border-danger-200 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <WifiOff className="h-5 w-5 text-danger-600" />
            <span className="text-danger-800 font-medium">Connection Error</span>
          </div>
          <p className="text-danger-700 text-sm mt-1">{error}</p>
          <button
            onClick={refreshAll}
            className="btn-danger mt-2 text-sm"
            disabled={loading}
          >
            {loading ? 'Retrying...' : 'Retry Connection'}
          </button>
        </div>
      )}

      {/* Market Status */}
      <div className="bg-primary-50 border border-primary-200 rounded-lg p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {isMarketOpen ? (
              <Wifi className="h-5 w-5 text-success-600" />
            ) : (
              <WifiOff className="h-5 w-5 text-gray-500" />
            )}
            <span className="font-medium text-primary-900">
              Market is {isMarketOpen ? 'OPEN' : 'CLOSED'}
            </span>
            {lastUpdated && (
              <span className="text-sm text-primary-700">
                • Last updated: {lastUpdated.toLocaleTimeString()}
              </span>
            )}
          </div>
          <button
            onClick={refreshAll}
            className="btn-secondary text-sm"
            disabled={loading}
          >
            {loading ? 'Updating...' : 'Refresh Data'}
          </button>
        </div>
      </div>

      {/* Header Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="card">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <DollarSign className="h-8 w-8 text-primary-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Portfolio Value</p>
              <p className="text-2xl font-semibold text-gray-900">
                ${portfolioValue.toLocaleString()}
              </p>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <TrendingUp className="h-8 w-8 text-success-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Total Profit</p>
              <p className="text-2xl font-semibold text-success-600">
                +${totalProfit.toLocaleString()}
              </p>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Target className="h-8 w-8 text-primary-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Active Trades</p>
              <p className="text-2xl font-semibold text-gray-900">
                {activePositions}
              </p>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Shield className="h-8 w-8 text-warning-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Risk Level</p>
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getRiskColor(riskLevel)}`}>
                {riskLevel}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`${
                  activeTab === tab.id
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                } whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2`}
              >
                <Icon className="h-4 w-4" />
                <span>{tab.name}</span>
              </button>
            );
          })}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="mt-6">
        {activeTab === 'dashboard' && <TradingDashboard />}
        {activeTab === 'strategies' && <StrategyRecommendations />}
        {activeTab === 'positions' && <PositionManager />}
        {activeTab === 'alerts' && <MarketAlerts />}
        {activeTab === 'risk' && <RiskMonitor />}
      </div>

      {/* Quick Help Section */}
      <div className="card bg-primary-50 border-primary-200">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <div className="flex items-center justify-center h-8 w-8 rounded-md bg-primary-500 text-white">
              <span className="text-sm font-medium">?</span>
            </div>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-primary-800">
              New to Options Trading?
            </h3>
            <div className="mt-2 text-sm text-primary-700">
              <p>
                This system uses simple language to help you make money from stocks you already like. 
                Think of it like collecting rent from your investments while you wait for them to grow.
              </p>
            </div>
            <div className="mt-3">
              <div className="-mx-2 -my-1.5 flex">
                <button className="bg-primary-100 px-2 py-1.5 rounded-md text-xs font-medium text-primary-800 hover:bg-primary-200">
                  Learn the Basics
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
