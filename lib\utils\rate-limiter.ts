// Rate Limiter to prevent API overuse
// Simple explanation: Prevents hitting API limits by spacing out requests

export class RateLimiter {
  private requests: { [key: string]: number[] } = {};
  private limits: { [key: string]: { requests: number; window: number } } = {
    'alpaca': { requests: 200, window: 60000 }, // 200 requests per minute
    'fmp': { requests: 250, window: 60000 }, // 250 requests per minute for free tier
  };

  /**
   * Check if we can make a request
   * Simple explanation: Like checking if you can make another phone call without going over your plan
   */
  canMakeRequest(service: 'alpaca' | 'fmp'): boolean {
    const now = Date.now();
    const limit = this.limits[service];
    
    if (!this.requests[service]) {
      this.requests[service] = [];
    }

    // Remove old requests outside the window
    this.requests[service] = this.requests[service].filter(
      timestamp => now - timestamp < limit.window
    );

    // Check if we're under the limit
    return this.requests[service].length < limit.requests;
  }

  /**
   * Record a request
   * Simple explanation: Keep track of when you made a request
   */
  recordRequest(service: 'alpaca' | 'fmp'): void {
    const now = Date.now();
    
    if (!this.requests[service]) {
      this.requests[service] = [];
    }

    this.requests[service].push(now);
  }

  /**
   * Wait until we can make a request
   * Simple explanation: Wait your turn if you've made too many requests
   */
  async waitForAvailability(service: 'alpaca' | 'fmp'): Promise<void> {
    while (!this.canMakeRequest(service)) {
      await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second
    }
  }

  /**
   * Get time until next available request
   * Simple explanation: How long until you can make another request
   */
  getTimeUntilAvailable(service: 'alpaca' | 'fmp'): number {
    if (this.canMakeRequest(service)) {
      return 0;
    }

    const now = Date.now();
    const limit = this.limits[service];
    const oldestRequest = Math.min(...this.requests[service]);
    
    return limit.window - (now - oldestRequest);
  }

  /**
   * Get current usage stats
   * Simple explanation: See how many requests you've made recently
   */
  getUsageStats(service: 'alpaca' | 'fmp'): {
    used: number;
    limit: number;
    resetTime: number;
  } {
    const now = Date.now();
    const limit = this.limits[service];
    
    if (!this.requests[service]) {
      this.requests[service] = [];
    }

    // Clean old requests
    this.requests[service] = this.requests[service].filter(
      timestamp => now - timestamp < limit.window
    );

    const oldestRequest = this.requests[service].length > 0 
      ? Math.min(...this.requests[service]) 
      : now;

    return {
      used: this.requests[service].length,
      limit: limit.requests,
      resetTime: oldestRequest + limit.window
    };
  }
}

// Global rate limiter instance
export const rateLimiter = new RateLimiter();
