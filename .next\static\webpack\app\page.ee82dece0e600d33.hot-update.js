"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ HomePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction HomePage() {\n    _s();\n    const [selectedStrategy, setSelectedStrategy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [liveData, setLiveData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const generateActionableTrades = (strategy, marketData)=>{\n        const trades = [];\n        const today = new Date();\n        const nextFriday = new Date(today);\n        nextFriday.setDate(today.getDate() + (5 - today.getDay() + 7) % 7);\n        const monthlyExp = new Date(today.getFullYear(), today.getMonth() + 1, 15);\n        Object.entries(marketData).forEach((param)=>{\n            let [symbol, data] = param;\n            const price = data.price;\n            if (strategy === \"Income Collection\") {\n                // Covered Call - 5% OTM\n                const strike = Math.ceil(price * 1.05 / 5) * 5;\n                const premium = Math.round(price * 0.015 * 100); // Estimate 1.5% premium\n                trades.push({\n                    symbol,\n                    currentPrice: price.toFixed(2),\n                    action: \"SELL CALL\",\n                    strike,\n                    expiration: nextFriday.toLocaleDateString(),\n                    premium: \"$\".concat(premium),\n                    requirement: \"Own 100 shares of \".concat(symbol),\n                    maxProfit: \"$\".concat(premium + (strike - price) * 100),\n                    breakeven: (price - premium / 100).toFixed(2)\n                });\n            }\n            if (strategy === \"Cash Secured Puts\") {\n                // Cash-Secured Put - 5% OTM\n                const strike = Math.floor(price * 0.95 / 5) * 5;\n                const premium = Math.round(price * 0.02 * 100); // Estimate 2% premium\n                trades.push({\n                    symbol,\n                    currentPrice: price.toFixed(2),\n                    action: \"SELL PUT\",\n                    strike,\n                    expiration: nextFriday.toLocaleDateString(),\n                    premium: \"$\".concat(premium),\n                    requirement: \"$\".concat(strike * 100, \" cash\"),\n                    maxProfit: \"$\".concat(premium),\n                    breakeven: (strike - premium / 100).toFixed(2)\n                });\n            }\n            if (strategy === \"LEAPS\") {\n                // LEAPS Call - ATM or slightly ITM\n                const strike = Math.floor(price / 5) * 5;\n                const leapsExp = new Date(today.getFullYear() + 1, 11, 15); // Dec next year\n                const premium = Math.round(price * 0.15 * 100); // Estimate 15% for LEAPS\n                trades.push({\n                    symbol,\n                    currentPrice: price.toFixed(2),\n                    action: \"BUY CALL\",\n                    strike,\n                    expiration: leapsExp.toLocaleDateString(),\n                    premium: \"$\".concat(premium),\n                    requirement: \"$\".concat(premium, \" capital\"),\n                    leverage: \"10:1\",\n                    breakeven: (strike + premium / 100).toFixed(2)\n                });\n            }\n        });\n        return trades.slice(0, 3); // Return top 3 trades\n    };\n    const handleStrategyClick = async (strategy)=>{\n        setSelectedStrategy(strategy);\n        setLoading(true);\n        try {\n            // Get live market data for actionable trades\n            const response = await fetch(\"/api/stocks?symbols=AAPL,MSFT,GOOGL,AMZN,TSLA,META,NVDA,SPY,QQQ,IWM\");\n            const result = await response.json();\n            if (result.success) {\n                setLiveData(result.data);\n            }\n        } catch (error) {\n            console.error(\"Error fetching live data:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: \"20px\",\n            fontFamily: \"Arial\",\n            maxWidth: \"1200px\",\n            margin: \"0 auto\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                style: {\n                    color: \"#0ea5e9\",\n                    marginBottom: \"20px\"\n                },\n                children: \"\\uD83D\\uDE80 Enhanced Options Trading Program\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                lineNumber: 98,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: \"#f0f9ff\",\n                    padding: \"20px\",\n                    borderRadius: \"10px\",\n                    marginBottom: \"20px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        children: \"\\uD83D\\uDCB0 READY TO MAKE MONEY!\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"Your live trading system is connected and ready to generate income.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                lineNumber: 100,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: \"grid\",\n                    gridTemplateColumns: \"repeat(auto-fit, minmax(250px, 1fr))\",\n                    gap: \"20px\",\n                    marginBottom: \"30px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: \"white\",\n                            padding: \"20px\",\n                            borderRadius: \"10px\",\n                            boxShadow: \"0 2px 4px rgba(0,0,0,0.1)\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    color: \"#22c55e\"\n                                },\n                                children: \"\\uD83D\\uDCB5 Portfolio Value\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    fontSize: \"24px\",\n                                    fontWeight: \"bold\"\n                                },\n                                children: \"$50,000\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: \"white\",\n                            padding: \"20px\",\n                            borderRadius: \"10px\",\n                            boxShadow: \"0 2px 4px rgba(0,0,0,0.1)\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    color: \"#22c55e\"\n                                },\n                                children: \"\\uD83D\\uDCC8 Total Profit\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    fontSize: \"24px\",\n                                    fontWeight: \"bold\",\n                                    color: \"#22c55e\"\n                                },\n                                children: \"+$2,450\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: \"white\",\n                            padding: \"20px\",\n                            borderRadius: \"10px\",\n                            boxShadow: \"0 2px 4px rgba(0,0,0,0.1)\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    color: \"#0ea5e9\"\n                                },\n                                children: \"\\uD83C\\uDFAF Active Trades\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    fontSize: \"24px\",\n                                    fontWeight: \"bold\"\n                                },\n                                children: \"8\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: \"white\",\n                            padding: \"20px\",\n                            borderRadius: \"10px\",\n                            boxShadow: \"0 2px 4px rgba(0,0,0,0.1)\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    color: \"#f59e0b\"\n                                },\n                                children: \"\\uD83D\\uDEE1️ Risk Level\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    fontSize: \"18px\",\n                                    fontWeight: \"bold\",\n                                    color: \"#f59e0b\"\n                                },\n                                children: \"MEDIUM\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                lineNumber: 105,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: \"white\",\n                    padding: \"30px\",\n                    borderRadius: \"10px\",\n                    boxShadow: \"0 2px 4px rgba(0,0,0,0.1)\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            marginBottom: \"20px\"\n                        },\n                        children: \"\\uD83C\\uDFAF Money-Making Strategies\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"grid\",\n                            gap: \"20px\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    border: \"2px solid #22c55e\",\n                                    borderRadius: \"10px\",\n                                    padding: \"20px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        style: {\n                                            color: \"#22c55e\"\n                                        },\n                                        children: \"\\uD83D\\uDCB0 Income Collection (Covered Calls)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"What it does:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 16\n                                            }, this),\n                                            \" Collect $100-500+ per month from stocks you own\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"How it works:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 134,\n                                                columnNumber: 16\n                                            }, this),\n                                            \" Sell call options on your stocks - get paid immediately\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Success rate:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 16\n                                            }, this),\n                                            \" 85% profitable\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleStrategyClick(\"Income Collection\"),\n                                        style: {\n                                            background: \"#22c55e\",\n                                            color: \"white\",\n                                            padding: \"10px 20px\",\n                                            border: \"none\",\n                                            borderRadius: \"5px\",\n                                            cursor: \"pointer\",\n                                            fontSize: \"16px\",\n                                            fontWeight: \"bold\"\n                                        },\n                                        onMouseOver: (e)=>e.target.style.background = \"#16a34a\",\n                                        onMouseOut: (e)=>e.target.style.background = \"#22c55e\",\n                                        children: \"\\uD83D\\uDCB0 START MAKING MONEY NOW →\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    border: \"2px solid #8b5cf6\",\n                                    borderRadius: \"10px\",\n                                    padding: \"20px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        style: {\n                                            color: \"#8b5cf6\"\n                                        },\n                                        children: \"\\uD83C\\uDFE6 Poor Man's Covered Call (PMCC)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"What it does:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 148,\n                                                columnNumber: 16\n                                            }, this),\n                                            \" Get covered call income with 90% less capital\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"How it works:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 149,\n                                                columnNumber: 16\n                                            }, this),\n                                            \" Buy LEAPS, sell short calls against it\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Example:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 16\n                                            }, this),\n                                            \" Control $18,000 of AAPL stock with just $1,800\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleStrategyClick(\"Poor Mans Covered Call\"),\n                                        style: {\n                                            background: \"#8b5cf6\",\n                                            color: \"white\",\n                                            padding: \"10px 20px\",\n                                            border: \"none\",\n                                            borderRadius: \"5px\",\n                                            cursor: \"pointer\",\n                                            fontSize: \"16px\",\n                                            fontWeight: \"bold\"\n                                        },\n                                        onMouseOver: (e)=>e.target.style.background = \"#7c3aed\",\n                                        onMouseOut: (e)=>e.target.style.background = \"#8b5cf6\",\n                                        children: \"\\uD83C\\uDFE6 START PMCC NOW →\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    border: \"2px solid #0ea5e9\",\n                                    borderRadius: \"10px\",\n                                    padding: \"20px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        style: {\n                                            color: \"#0ea5e9\"\n                                        },\n                                        children: \"\\uD83C\\uDFB2 Bear Call Spread (Range Betting)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"What it does:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 16\n                                            }, this),\n                                            \" Bet stocks stay below a level - collect money if right\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Success rate:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 164,\n                                                columnNumber: 16\n                                            }, this),\n                                            \" 70-80% win rate\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Example:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 165,\n                                                columnNumber: 16\n                                            }, this),\n                                            \" MSFT at $350? Bet it stays below $360, collect $240\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleStrategyClick(\"Bear Call Spread\"),\n                                        style: {\n                                            background: \"#0ea5e9\",\n                                            color: \"white\",\n                                            padding: \"10px 20px\",\n                                            border: \"none\",\n                                            borderRadius: \"5px\",\n                                            cursor: \"pointer\",\n                                            fontSize: \"16px\",\n                                            fontWeight: \"bold\"\n                                        },\n                                        onMouseOver: (e)=>e.target.style.background = \"#0284c7\",\n                                        onMouseOut: (e)=>e.target.style.background = \"#0ea5e9\",\n                                        children: \"\\uD83C\\uDFB2 FIND OPPORTUNITIES NOW →\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    border: \"2px solid #f59e0b\",\n                                    borderRadius: \"10px\",\n                                    padding: \"20px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        style: {\n                                            color: \"#f59e0b\"\n                                        },\n                                        children: \"\\uD83D\\uDD04 The Wheel (Consistent Income)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"What it does:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 16\n                                            }, this),\n                                            \" Generate 12-20% annual returns consistently\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"How it works:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 16\n                                            }, this),\n                                            \" Cycle between selling puts and calls automatically\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Best part:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 16\n                                            }, this),\n                                            \" Works in any market condition\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleStrategyClick(\"The Wheel\"),\n                                        style: {\n                                            background: \"#f59e0b\",\n                                            color: \"white\",\n                                            padding: \"10px 20px\",\n                                            border: \"none\",\n                                            borderRadius: \"5px\",\n                                            cursor: \"pointer\",\n                                            fontSize: \"16px\",\n                                            fontWeight: \"bold\"\n                                        },\n                                        onMouseOver: (e)=>e.target.style.background = \"#d97706\",\n                                        onMouseOut: (e)=>e.target.style.background = \"#f59e0b\",\n                                        children: \"\\uD83D\\uDD04 START THE WHEEL NOW →\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 181,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    border: \"2px solid #ef4444\",\n                                    borderRadius: \"10px\",\n                                    padding: \"20px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        style: {\n                                            color: \"#ef4444\"\n                                        },\n                                        children: \"\\uD83D\\uDCB8 Cash-Secured Puts (Buy Stocks Cheap)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"What it does:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 193,\n                                                columnNumber: 16\n                                            }, this),\n                                            \" Get paid to buy stocks at discount prices\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"How it works:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 16\n                                            }, this),\n                                            \" Sell puts, collect premium, maybe get cheap stock\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Example:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 16\n                                            }, this),\n                                            \" AAPL at $180? Get paid $300 to buy it at $175\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleStrategyClick(\"Cash Secured Puts\"),\n                                        style: {\n                                            background: \"#ef4444\",\n                                            color: \"white\",\n                                            padding: \"10px 20px\",\n                                            border: \"none\",\n                                            borderRadius: \"5px\",\n                                            cursor: \"pointer\",\n                                            fontSize: \"16px\",\n                                            fontWeight: \"bold\"\n                                        },\n                                        onMouseOver: (e)=>e.target.style.background = \"#dc2626\",\n                                        onMouseOut: (e)=>e.target.style.background = \"#ef4444\",\n                                        children: \"\\uD83D\\uDCB8 GET PAID TO BUY →\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    border: \"2px solid #10b981\",\n                                    borderRadius: \"10px\",\n                                    padding: \"20px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        style: {\n                                            color: \"#10b981\"\n                                        },\n                                        children: \"⏰ Calendar Spreads (Time Decay)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"What it does:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 16\n                                            }, this),\n                                            \" Profit from time passing on sideways stocks\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"How it works:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 209,\n                                                columnNumber: 16\n                                            }, this),\n                                            \" Sell short-term, buy long-term at same strike\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Best for:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 16\n                                            }, this),\n                                            \" Stocks stuck in a range for weeks\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleStrategyClick(\"Calendar Spreads\"),\n                                        style: {\n                                            background: \"#10b981\",\n                                            color: \"white\",\n                                            padding: \"10px 20px\",\n                                            border: \"none\",\n                                            borderRadius: \"5px\",\n                                            cursor: \"pointer\",\n                                            fontSize: \"16px\",\n                                            fontWeight: \"bold\"\n                                        },\n                                        onMouseOver: (e)=>e.target.style.background = \"#059669\",\n                                        onMouseOut: (e)=>e.target.style.background = \"#10b981\",\n                                        children: \"⏰ PROFIT FROM TIME →\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    border: \"2px solid #6366f1\",\n                                    borderRadius: \"10px\",\n                                    padding: \"20px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        style: {\n                                            color: \"#6366f1\"\n                                        },\n                                        children: \"\\uD83D\\uDE80 LEAPS (Long-Term Leverage)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"What it does:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 16\n                                            }, this),\n                                            \" Control $15,000 of stock with $1,500\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"How it works:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 224,\n                                                columnNumber: 16\n                                            }, this),\n                                            \" Buy long-term options for massive leverage\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Example:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 16\n                                            }, this),\n                                            \" 10x leverage on your favorite stocks\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleStrategyClick(\"LEAPS\"),\n                                        style: {\n                                            background: \"#6366f1\",\n                                            color: \"white\",\n                                            padding: \"10px 20px\",\n                                            border: \"none\",\n                                            borderRadius: \"5px\",\n                                            cursor: \"pointer\",\n                                            fontSize: \"16px\",\n                                            fontWeight: \"bold\"\n                                        },\n                                        onMouseOver: (e)=>e.target.style.background = \"#4f46e5\",\n                                        onMouseOut: (e)=>e.target.style.background = \"#6366f1\",\n                                        children: \"\\uD83D\\uDE80 GET LEVERAGE NOW →\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                lineNumber: 127,\n                columnNumber: 7\n            }, this),\n            selectedStrategy && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: \"#f0f9ff\",\n                    border: \"2px solid #0ea5e9\",\n                    borderRadius: \"10px\",\n                    padding: \"30px\",\n                    marginTop: \"20px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            color: \"#0ea5e9\",\n                            marginBottom: \"20px\"\n                        },\n                        children: [\n                            \"\\uD83C\\uDFAF \",\n                            selectedStrategy,\n                            \" - READY TO EXECUTE!\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                        lineNumber: 240,\n                        columnNumber: 11\n                    }, this),\n                    selectedStrategy === \"Income Collection\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    color: \"#22c55e\"\n                                },\n                                children: \"\\uD83D\\uDCB0 STEP-BY-STEP COVERED CALLS:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                style: {\n                                    fontSize: \"16px\",\n                                    lineHeight: \"1.6\",\n                                    marginLeft: \"20px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Own 100 shares\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" of a stock you like (AAPL, MSFT, GOOGL)\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Sell a call option\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 247,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" above current price - collect $100-500 immediately\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Set strike 5-10% above\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 248,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" current price for safety\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"If stock stays below strike\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 249,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" - keep all premium and repeat\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"If stock goes above strike\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 250,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" - sell shares for profit + keep premium\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    background: \"#22c55e\",\n                                    color: \"white\",\n                                    padding: \"15px\",\n                                    borderRadius: \"5px\",\n                                    marginTop: \"15px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"\\uD83D\\uDE80 EXAMPLE:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 17\n                                    }, this),\n                                    \" Own AAPL at $180, sell $190 call for $275. If AAPL stays below $190, keep $275. If above, sell at $190 + keep $275 = $1,275 profit!\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 252,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                        lineNumber: 243,\n                        columnNumber: 13\n                    }, this),\n                    selectedStrategy === \"Poor Mans Covered Call\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    color: \"#8b5cf6\"\n                                },\n                                children: \"\\uD83C\\uDFE6 STEP-BY-STEP POOR MAN'S COVERED CALL:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                style: {\n                                    fontSize: \"16px\",\n                                    lineHeight: \"1.6\",\n                                    marginLeft: \"20px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Buy a LEAPS call\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 262,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" (1+ year expiration) deep in-the-money\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Sell short-term calls\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" against it monthly for income\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 263,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Use 90% less capital\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 264,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" than buying 100 shares\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 264,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Collect monthly premiums\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 265,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" while LEAPS appreciates\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 265,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Roll or close\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" if short call gets threatened\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    background: \"#8b5cf6\",\n                                    color: \"white\",\n                                    padding: \"15px\",\n                                    borderRadius: \"5px\",\n                                    marginTop: \"15px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"\\uD83D\\uDE80 EXAMPLE:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 269,\n                                        columnNumber: 17\n                                    }, this),\n                                    \" Instead of buying $18,000 of AAPL stock, buy $1,800 LEAPS. Sell monthly calls for $200-400. Same income, 90% less capital!\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 268,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                        lineNumber: 259,\n                        columnNumber: 13\n                    }, this),\n                    selectedStrategy === \"Bear Call Spread\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    color: \"#0ea5e9\"\n                                },\n                                children: \"\\uD83C\\uDFB2 STEP-BY-STEP BEAR CALL SPREAD:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 276,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                style: {\n                                    fontSize: \"16px\",\n                                    lineHeight: \"1.6\",\n                                    marginLeft: \"20px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Pick a stock near resistance\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 278,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" that you think won't break higher\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Sell a call\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" at the resistance level (collect premium)\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 279,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Buy a higher call\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" for protection (spend less premium)\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 280,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Keep the difference\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 281,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" if stock stays below your sold strike\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Max profit in 2-4 weeks\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 282,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" if you're right about direction\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    background: \"#0ea5e9\",\n                                    color: \"white\",\n                                    padding: \"15px\",\n                                    borderRadius: \"5px\",\n                                    marginTop: \"15px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"\\uD83D\\uDE80 EXAMPLE:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 285,\n                                        columnNumber: 17\n                                    }, this),\n                                    \" MSFT at $350, sell $360 call for $400, buy $370 call for $150. Net credit $250. If MSFT stays below $360, keep all $250!\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 284,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                        lineNumber: 275,\n                        columnNumber: 13\n                    }, this),\n                    selectedStrategy === \"Range Betting\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    color: \"#0ea5e9\"\n                                },\n                                children: \"\\uD83C\\uDFB2 STEP-BY-STEP RANGE BETTING:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                style: {\n                                    fontSize: \"16px\",\n                                    lineHeight: \"1.6\",\n                                    marginLeft: \"20px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Find a stock trading in a range\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 294,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" - like MSFT between $350-380\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Sell options at both ends\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" - collect premium betting it stays in range\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 295,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Collect $150-300+ immediately\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" - money hits your account\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 296,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"If stock stays in range\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 297,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" - keep all the money (70-80% of the time)\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 297,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"If stock breaks out\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" - limited loss, defined risk\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 293,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    background: \"#0ea5e9\",\n                                    color: \"white\",\n                                    padding: \"15px\",\n                                    borderRadius: \"5px\",\n                                    marginTop: \"15px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"\\uD83D\\uDE80 NEXT STEP:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 301,\n                                        columnNumber: 17\n                                    }, this),\n                                    \" Check your trading platform for current range-bound stocks!\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 300,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                        lineNumber: 291,\n                        columnNumber: 13\n                    }, this),\n                    selectedStrategy === \"The Wheel\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    color: \"#f59e0b\"\n                                },\n                                children: \"\\uD83D\\uDD04 STEP-BY-STEP WHEEL STRATEGY:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 308,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                style: {\n                                    fontSize: \"16px\",\n                                    lineHeight: \"1.6\",\n                                    marginLeft: \"20px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Start by selling puts\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 310,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" on stocks you want to own at lower prices\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 310,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Collect premium immediately\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 311,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" - $100-400+ per month\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 311,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"If assigned stock\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 312,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" - now you own it at a discount\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 312,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Sell calls on the stock\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 313,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" - collect more premium\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 313,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Repeat the cycle\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 314,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" - consistent 12-20% annual returns\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 309,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    background: \"#f59e0b\",\n                                    color: \"white\",\n                                    padding: \"15px\",\n                                    borderRadius: \"5px\",\n                                    marginTop: \"15px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"\\uD83D\\uDE80 EXAMPLE:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 317,\n                                        columnNumber: 17\n                                    }, this),\n                                    \" Sell AAPL $175 put for $300. If assigned, own AAPL at $175. Sell $185 call for $250. If called away, profit $1,550 + premiums!\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                        lineNumber: 307,\n                        columnNumber: 13\n                    }, this),\n                    selectedStrategy === \"Cash Secured Puts\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    color: \"#ef4444\"\n                                },\n                                children: \"\\uD83D\\uDCB8 STEP-BY-STEP CASH-SECURED PUTS:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                style: {\n                                    fontSize: \"16px\",\n                                    lineHeight: \"1.6\",\n                                    marginLeft: \"20px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Pick a stock you want to own\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 326,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" at a lower price\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Sell a put option\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 327,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" below current price - collect premium\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Set aside cash\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 328,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" to buy 100 shares if assigned\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 328,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"If stock stays above strike\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 329,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" - keep premium, repeat\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 329,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"If stock drops below\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" - buy stock at discount + keep premium\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 330,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 325,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    background: \"#ef4444\",\n                                    color: \"white\",\n                                    padding: \"15px\",\n                                    borderRadius: \"5px\",\n                                    marginTop: \"15px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"\\uD83D\\uDE80 EXAMPLE:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 333,\n                                        columnNumber: 17\n                                    }, this),\n                                    \" AAPL at $180, sell $175 put for $300. If AAPL stays above $175, keep $300. If below, buy AAPL at $175 (5% discount) + keep $300!\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 332,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                        lineNumber: 323,\n                        columnNumber: 13\n                    }, this),\n                    selectedStrategy === \"Calendar Spreads\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    color: \"#10b981\"\n                                },\n                                children: \"⏰ STEP-BY-STEP CALENDAR SPREADS:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 340,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                style: {\n                                    fontSize: \"16px\",\n                                    lineHeight: \"1.6\",\n                                    marginLeft: \"20px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Find a sideways stock\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 342,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" trading in a tight range\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 342,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Sell a short-term option\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 343,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" at current price (collect premium)\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 343,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Buy a long-term option\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 344,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" at same strike (protection)\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 344,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Profit from time decay\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 345,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" as short option expires faster\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 345,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Repeat monthly\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 346,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" for consistent income\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 346,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 341,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    background: \"#10b981\",\n                                    color: \"white\",\n                                    padding: \"15px\",\n                                    borderRadius: \"5px\",\n                                    marginTop: \"15px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"\\uD83D\\uDE80 EXAMPLE:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 349,\n                                        columnNumber: 17\n                                    }, this),\n                                    \" MSFT stuck at $350. Sell 2-week $350 call for $200, buy 3-month $350 call for $150. Net $50 profit if MSFT stays near $350!\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 348,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                        lineNumber: 339,\n                        columnNumber: 13\n                    }, this),\n                    selectedStrategy === \"LEAPS\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    color: \"#6366f1\"\n                                },\n                                children: \"\\uD83D\\uDE80 STEP-BY-STEP LEAPS STRATEGY:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 356,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                style: {\n                                    fontSize: \"16px\",\n                                    lineHeight: \"1.6\",\n                                    marginLeft: \"20px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Pick your favorite stock\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 358,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" for long-term growth\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 358,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Buy LEAPS calls\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 359,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" (1-2 years out) for massive leverage\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 359,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Use 10% of capital\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 360,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" vs buying shares outright\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 360,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Get 10x exposure\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 361,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" to stock movements\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 361,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Sell anytime in profit\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 362,\n                                                columnNumber: 21\n                                            }, this),\n                                            \" or hold for big gains\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 362,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 357,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    background: \"#6366f1\",\n                                    color: \"white\",\n                                    padding: \"15px\",\n                                    borderRadius: \"5px\",\n                                    marginTop: \"15px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"\\uD83D\\uDE80 EXAMPLE:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 365,\n                                        columnNumber: 17\n                                    }, this),\n                                    \" Instead of buying $15,000 of AAPL stock, buy $1,500 LEAPS. If AAPL goes up 20%, your LEAPS might go up 200%! Same upside, 90% less capital.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 364,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                        lineNumber: 355,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setSelectedStrategy(\"\"),\n                        style: {\n                            background: \"#6b7280\",\n                            color: \"white\",\n                            padding: \"10px 20px\",\n                            border: \"none\",\n                            borderRadius: \"5px\",\n                            cursor: \"pointer\",\n                            marginTop: \"15px\"\n                        },\n                        children: \"← Back to Strategies\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                        lineNumber: 370,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                lineNumber: 239,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: \"#1e40af\",\n                    color: \"white\",\n                    padding: \"20px\",\n                    borderRadius: \"10px\",\n                    marginTop: \"30px\",\n                    textAlign: \"center\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        children: \"\\uD83C\\uDFE6 PROFESSIONAL TRADING WITH INTERACTIVE BROKERS!\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                        lineNumber: 380,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"✅ Best Options Data • ✅ $0.65 Per Contract • ✅ Professional Platform\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                        lineNumber: 381,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                            children: \"Your system now supports IBKR for professional-grade options trading!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                            lineNumber: 382,\n                            columnNumber: 12\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                        lineNumber: 382,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: \"rgba(255,255,255,0.1)\",\n                            padding: \"15px\",\n                            borderRadius: \"5px\",\n                            marginTop: \"15px\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"\\uD83D\\uDE80 NEXT STEP:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 384,\n                                        columnNumber: 14\n                                    }, this),\n                                    \" Set up your IBKR API connection\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 384,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    fontSize: \"14px\",\n                                    marginTop: \"5px\"\n                                },\n                                children: \"1. Enable API in your IBKR account • 2. Download TWS/Gateway • 3. Start trading!\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 385,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                        lineNumber: 383,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                lineNumber: 379,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: \"#22c55e\",\n                    color: \"white\",\n                    padding: \"20px\",\n                    borderRadius: \"10px\",\n                    marginTop: \"20px\",\n                    textAlign: \"center\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        children: \"\\uD83D\\uDE80 YOUR SYSTEM IS LIVE AND READY!\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                        lineNumber: 392,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"Connected to Alpaca • FMP Data • IBKR Ready • AI-Powered Strategies\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                        lineNumber: 393,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                            children: \"Start making money today with proven strategies!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                            lineNumber: 394,\n                            columnNumber: 12\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                        lineNumber: 394,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        style: {\n                            marginTop: \"10px\",\n                            fontSize: \"14px\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Network Access:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 396,\n                                columnNumber: 11\n                            }, this),\n                            \" Other computers can access at http://192.168.1.73:3003\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                        lineNumber: 395,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                lineNumber: 391,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n        lineNumber: 97,\n        columnNumber: 5\n    }, this);\n}\n_s(HomePage, \"ovJrq+w6DchUH+Yrk40Ze7eM3HM=\");\n_c = HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});