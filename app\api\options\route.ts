// API Route for Options Data
// Provides options chains and analysis

import { NextRequest, NextResponse } from 'next/server';
import { MarketDataService } from '../../../lib/api/market-data-service';

const marketData = new MarketDataService();

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const symbolsParam = searchParams.get('symbols');
    
    if (!symbolsParam) {
      return NextResponse.json({ error: 'Symbols parameter required' }, { status: 400 });
    }

    const symbols = symbolsParam.split(',').map(s => s.trim().toUpperCase());
    
    if (symbols.length === 0) {
      return NextResponse.json({ error: 'No valid symbols provided' }, { status: 400 });
    }

    // Get options chains
    const optionsData = await marketData.getOptionsChains(symbols);
    
    return NextResponse.json({
      success: true,
      data: optionsData,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error in options API:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch options data',
        message: error instanceof Error ? error.message : 'Unknown error'
      }, 
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { symbols } = body;
    
    if (!symbols || !Array.isArray(symbols)) {
      return NextResponse.json({ error: 'Invalid symbols array' }, { status: 400 });
    }

    const optionsData = await marketData.getOptionsChains(symbols);
    
    return NextResponse.json({
      success: true,
      data: optionsData,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error in options POST API:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch options data',
        message: error instanceof Error ? error.message : 'Unknown error'
      }, 
      { status: 500 }
    );
  }
}
