// Sample data for demonstration purposes
// In a real application, this would come from market data APIs

import { Stock, OptionContract, Position, MarketAnalysis } from './types';

export const sampleStocks: Stock[] = [
  {
    symbol: 'AAPL',
    name: 'Apple Inc.',
    price: 182.50,
    change: 2.15,
    changePercent: 1.19,
    volume: 45678900,
    marketCap: 2850000000000,
    sector: 'Technology'
  },
  {
    symbol: 'MSFT',
    name: 'Microsoft Corporation',
    price: 378.25,
    change: -1.85,
    changePercent: -0.49,
    volume: 23456789,
    marketCap: 2800000000000,
    sector: 'Technology'
  },
  {
    symbol: 'GOOGL',
    name: 'Alphabet Inc.',
    price: 142.80,
    change: 0.95,
    changePercent: 0.67,
    volume: 18765432,
    marketCap: 1750000000000,
    sector: 'Technology'
  },
  {
    symbol: 'TSLA',
    name: 'Tesla Inc.',
    price: 248.75,
    change: 8.45,
    changePercent: 3.52,
    volume: 67890123,
    marketCap: 790000000000,
    sector: 'Automotive'
  },
  {
    symbol: 'AMZN',
    name: 'Amazon.com Inc.',
    price: 145.60,
    change: -2.30,
    changePercent: -1.56,
    volume: 34567890,
    marketCap: 1520000000000,
    sector: 'Consumer Discretionary'
  }
];

export const sampleOptionsChain: { [symbol: string]: OptionContract[] } = {
  'AAPL': [
    {
      strike: 180,
      bid: 4.20,
      ask: 4.35,
      last: 4.28,
      volume: 1250,
      openInterest: 8900,
      impliedVolatility: 0.28,
      delta: 0.65,
      gamma: 0.025,
      theta: -0.08,
      vega: 0.15,
      rho: 0.12,
      intrinsicValue: 2.50,
      timeValue: 1.78,
      daysToExpiration: 30
    },
    {
      strike: 185,
      bid: 2.15,
      ask: 2.25,
      last: 2.20,
      volume: 890,
      openInterest: 5600,
      impliedVolatility: 0.26,
      delta: 0.35,
      gamma: 0.030,
      theta: -0.06,
      vega: 0.12,
      rho: 0.08,
      intrinsicValue: 0,
      timeValue: 2.20,
      daysToExpiration: 30
    }
  ],
  'MSFT': [
    {
      strike: 375,
      bid: 8.50,
      ask: 8.75,
      last: 8.60,
      volume: 750,
      openInterest: 4200,
      impliedVolatility: 0.24,
      delta: 0.58,
      gamma: 0.018,
      theta: -0.12,
      vega: 0.18,
      rho: 0.15,
      intrinsicValue: 3.25,
      timeValue: 5.35,
      daysToExpiration: 30
    }
  ]
};

export const samplePositions: Position[] = [
  {
    id: '1',
    symbol: 'AAPL',
    strategy: 'COVERED_CALL',
    legs: [
      {
        type: 'CALL',
        action: 'SELL',
        strike: 185,
        expiration: '2024-02-16',
        quantity: 1,
        premium: 2.20,
        currentValue: 1.85
      }
    ],
    openDate: new Date('2024-01-15'),
    status: 'ACTIVE',
    totalCost: -220, // Credit received
    currentValue: -185,
    profitLoss: 35,
    profitLossPercent: 15.9,
    maxProfit: 220,
    maxLoss: 0, // Covered call has no loss on the option side
    breakeven: [182.50 - 2.20],
    daysToExpiration: 18,
    alerts: []
  },
  {
    id: '2',
    symbol: 'MSFT',
    strategy: 'BEAR_CALL_SPREAD',
    legs: [
      {
        type: 'CALL',
        action: 'SELL',
        strike: 380,
        expiration: '2024-02-09',
        quantity: 1,
        premium: 3.50,
        currentValue: 4.20
      },
      {
        type: 'CALL',
        action: 'BUY',
        strike: 385,
        expiration: '2024-02-09',
        quantity: 1,
        premium: 2.10,
        currentValue: 2.80
      }
    ],
    openDate: new Date('2024-01-10'),
    status: 'ACTIVE',
    totalCost: -140, // Net credit
    currentValue: -140,
    profitLoss: 0,
    profitLossPercent: 0,
    maxProfit: 140,
    maxLoss: 360, // (385-380)*100 - 140
    breakeven: [381.40],
    daysToExpiration: 11,
    alerts: [
      {
        id: 'alert1',
        type: 'TIME',
        message: 'Position expires in 11 days',
        priority: 'MEDIUM',
        triggered: true,
        createdAt: new Date()
      }
    ]
  }
];

export const sampleMarketAnalysis: { [symbol: string]: MarketAnalysis } = {
  'AAPL': {
    regime: 'NEUTRAL_TO_BULLISH',
    volatilityRank: 65,
    trend: 'BULLISH',
    support: 175.00,
    resistance: 190.00,
    impliedVolatility: 28,
    historicalVolatility: 24,
    earnings: new Date('2024-02-01'),
    dividend: {
      exDate: new Date('2024-02-09'),
      amount: 0.24
    }
  },
  'MSFT': {
    regime: 'CHOPPY_RANGE',
    volatilityRank: 45,
    trend: 'NEUTRAL',
    support: 365.00,
    resistance: 385.00,
    impliedVolatility: 24,
    historicalVolatility: 22,
    earnings: new Date('2024-01-31')
  },
  'GOOGL': {
    regime: 'LOW_VOLATILITY',
    volatilityRank: 25,
    trend: 'BULLISH',
    support: 135.00,
    resistance: 150.00,
    impliedVolatility: 22,
    historicalVolatility: 26
  },
  'TSLA': {
    regime: 'HIGH_VOLATILITY',
    volatilityRank: 85,
    trend: 'BULLISH',
    support: 230.00,
    resistance: 260.00,
    impliedVolatility: 45,
    historicalVolatility: 38
  },
  'AMZN': {
    regime: 'BEARISH_HIGH_IV',
    volatilityRank: 75,
    trend: 'BEARISH',
    support: 140.00,
    resistance: 155.00,
    impliedVolatility: 32,
    historicalVolatility: 28
  }
};

// Helper function to get sample data
export function getSampleData() {
  return {
    stocks: sampleStocks,
    optionsChains: sampleOptionsChain,
    positions: samplePositions,
    marketAnalyses: sampleMarketAnalysis
  };
}
