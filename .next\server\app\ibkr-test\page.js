/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/ibkr-test/page";
exports.ids = ["app/ibkr-test/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fibkr-test%2Fpage&page=%2Fibkr-test%2Fpage&appPaths=%2Fibkr-test%2Fpage&pagePath=private-next-app-dir%2Fibkr-test%2Fpage.tsx&appDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cericashacks%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cericashacks&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fibkr-test%2Fpage&page=%2Fibkr-test%2Fpage&appPaths=%2Fibkr-test%2Fpage&pagePath=private-next-app-dir%2Fibkr-test%2Fpage.tsx&appDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cericashacks%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cericashacks&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'ibkr-test',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/ibkr-test/page.tsx */ \"(rsc)/./app/ibkr-test/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/ibkr-test/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/ibkr-test/page\",\n        pathname: \"/ibkr-test\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fibkr-test%2Fpage&page=%2Fibkr-test%2Fpage&appPaths=%2Fibkr-test%2Fpage&pagePath=private-next-app-dir%2Fibkr-test%2Fpage.tsx&appDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cericashacks%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cericashacks&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSJFit%5C%5CDesktop%5C%5Cericashacks%5C%5Capp%5C%5Cibkr-test%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSJFit%5C%5CDesktop%5C%5Cericashacks%5C%5Capp%5C%5Cibkr-test%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/ibkr-test/page.tsx */ \"(ssr)/./app/ibkr-test/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1NKRml0JTVDJTVDRGVza3RvcCU1QyU1Q2VyaWNhc2hhY2tzJTVDJTVDYXBwJTVDJTVDaWJrci10ZXN0JTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDRKQUFxRyIsInNvdXJjZXMiOlsid2VicGFjazovL2VuaGFuY2VkLW9wdGlvbnMtdHJhZGluZy1wcm9ncmFtLz8wZmMzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcU0pGaXRcXFxcRGVza3RvcFxcXFxlcmljYXNoYWNrc1xcXFxhcHBcXFxcaWJrci10ZXN0XFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSJFit%5C%5CDesktop%5C%5Cericashacks%5C%5Capp%5C%5Cibkr-test%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSJFit%5C%5CDesktop%5C%5Cericashacks%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSJFit%5C%5CDesktop%5C%5Cericashacks%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSJFit%5C%5CDesktop%5C%5Cericashacks%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSJFit%5C%5CDesktop%5C%5Cericashacks%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSJFit%5C%5CDesktop%5C%5Cericashacks%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSJFit%5C%5CDesktop%5C%5Cericashacks%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSJFit%5C%5CDesktop%5C%5Cericashacks%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSJFit%5C%5CDesktop%5C%5Cericashacks%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSJFit%5C%5CDesktop%5C%5Cericashacks%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSJFit%5C%5CDesktop%5C%5Cericashacks%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSJFit%5C%5CDesktop%5C%5Cericashacks%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSJFit%5C%5CDesktop%5C%5Cericashacks%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSJFit%5C%5CDesktop%5C%5Cericashacks%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSJFit%5C%5CDesktop%5C%5Cericashacks%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSJFit%5C%5CDesktop%5C%5Cericashacks%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSJFit%5C%5CDesktop%5C%5Cericashacks%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSJFit%5C%5CDesktop%5C%5Cericashacks%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSJFit%5C%5CDesktop%5C%5Cericashacks%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSJFit%5C%5CDesktop%5C%5Cericashacks%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSJFit%5C%5CDesktop%5C%5Cericashacks%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSJFit%5C%5CDesktop%5C%5Cericashacks%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSJFit%5C%5CDesktop%5C%5Cericashacks%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./app/ibkr-test/page.tsx":
/*!********************************!*\
  !*** ./app/ibkr-test/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ IBKRTestPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction IBKRTestPage() {\n    const [connectionStatus, setConnectionStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"Not Connected\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [accountInfo, setAccountInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const testConnection = async ()=>{\n        setLoading(true);\n        setError(\"\");\n        try {\n            console.log(\"\\uD83D\\uDD0C Testing IBKR connection...\");\n            const response = await fetch(\"/api/ibkr?action=connect\");\n            const result = await response.json();\n            if (result.success) {\n                setConnectionStatus(\"✅ Connected to IBKR!\");\n                console.log(\"✅ IBKR Connected successfully!\");\n                // Get account info\n                setTimeout(async ()=>{\n                    try {\n                        const accountResponse = await fetch(\"/api/ibkr?action=account\");\n                        const accountResult = await accountResponse.json();\n                        if (accountResult.success) {\n                            setAccountInfo(accountResult.data);\n                        }\n                    } catch (err) {\n                        console.log(\"Account info not available yet\");\n                    }\n                }, 2000);\n            } else {\n                setConnectionStatus(\"❌ Connection Failed\");\n                setError(result.message || \"Unknown error\");\n                console.error(\"❌ IBKR Connection failed:\", result.message);\n            }\n        } catch (err) {\n            setConnectionStatus(\"❌ Connection Error\");\n            setError(err instanceof Error ? err.message : \"Network error\");\n            console.error(\"❌ Connection error:\", err);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const checkStatus = async ()=>{\n        try {\n            const response = await fetch(\"/api/ibkr?action=status\");\n            const result = await response.json();\n            if (result.success) {\n                setConnectionStatus(result.data.connected ? \"✅ Connected\" : \"❌ Disconnected\");\n            }\n        } catch (err) {\n            setConnectionStatus(\"❌ Error checking status\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: \"20px\",\n            fontFamily: \"Arial\",\n            maxWidth: \"800px\",\n            margin: \"0 auto\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                style: {\n                    color: \"#1e40af\",\n                    marginBottom: \"20px\"\n                },\n                children: \"\\uD83C\\uDFE6 IBKR Connection Test\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: \"#f0f9ff\",\n                    padding: \"20px\",\n                    borderRadius: \"10px\",\n                    marginBottom: \"20px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        children: \"\\uD83D\\uDCCB Setup Steps (Web Interface)\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                        style: {\n                            lineHeight: \"1.6\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"In IBKR Client Portal:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" Go to Settings → API → Enable API Access\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Download IB Gateway:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" Lighter than TWS, easier to use\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Get your Account ID:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" Look for account number (starts with U or DU)\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Set Socket Port:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" 7497 for paper trading, 7496 for live\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Enable Paper Trading:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" Perfect for learning without risk!\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 77,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: \"#22c55e\",\n                            color: \"white\",\n                            padding: \"15px\",\n                            borderRadius: \"5px\",\n                            marginTop: \"15px\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"\\uD83D\\uDCA1 TIP:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 11\n                            }, this),\n                            \" Start with Paper Trading (DU account) - it's free and perfect for learning!\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                lineNumber: 70,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: \"#fffbeb\",\n                    border: \"1px solid #fed7aa\",\n                    padding: \"20px\",\n                    borderRadius: \"10px\",\n                    marginBottom: \"20px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        children: \"\\uD83D\\uDD0D Find Your Account ID\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"Your IBKR account ID should be visible in the Client Portal:\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        style: {\n                            lineHeight: \"1.6\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Live Account:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 15\n                                    }, this),\n                                    ' Starts with \"U\" (like U1234567)'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Paper Account:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 15\n                                    }, this),\n                                    ' Starts with \"DU\" (like DU1234567)'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Location:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" Usually at the top of your Client Portal page\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 91,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: \"white\",\n                            padding: \"10px\",\n                            borderRadius: \"5px\",\n                            marginTop: \"10px\",\n                            border: \"1px solid #d1d5db\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                style: {\n                                    display: \"block\",\n                                    marginBottom: \"5px\",\n                                    fontWeight: \"bold\"\n                                },\n                                children: \"Enter your Account ID:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                placeholder: \"e.g., DU1234567 or U1234567\",\n                                style: {\n                                    width: \"100%\",\n                                    padding: \"8px\",\n                                    border: \"1px solid #d1d5db\",\n                                    borderRadius: \"3px\"\n                                },\n                                onChange: (e)=>console.log(\"Account ID:\", e.target.value)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    fontSize: \"12px\",\n                                    color: \"#6b7280\",\n                                    marginTop: \"5px\"\n                                },\n                                children: \"We'll use this to connect to your IBKR account\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                lineNumber: 85,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: \"white\",\n                    padding: \"20px\",\n                    borderRadius: \"10px\",\n                    boxShadow: \"0 2px 4px rgba(0,0,0,0.1)\",\n                    marginBottom: \"20px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        children: \"Connection Status\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        style: {\n                            fontSize: \"18px\",\n                            fontWeight: \"bold\",\n                            color: connectionStatus.includes(\"✅\") ? \"#22c55e\" : \"#ef4444\"\n                        },\n                        children: connectionStatus\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 9\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: \"#fef2f2\",\n                            border: \"1px solid #fecaca\",\n                            padding: \"10px\",\n                            borderRadius: \"5px\",\n                            marginTop: \"10px\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            style: {\n                                color: \"#dc2626\",\n                                margin: 0\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"Error:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 56\n                                }, this),\n                                \" \",\n                                error\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            marginTop: \"15px\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: testConnection,\n                                disabled: loading,\n                                style: {\n                                    background: loading ? \"#9ca3af\" : \"#1e40af\",\n                                    color: \"white\",\n                                    padding: \"10px 20px\",\n                                    border: \"none\",\n                                    borderRadius: \"5px\",\n                                    cursor: loading ? \"not-allowed\" : \"pointer\",\n                                    marginRight: \"10px\",\n                                    fontSize: \"16px\"\n                                },\n                                children: loading ? \"\\uD83D\\uDD04 Connecting...\" : \"\\uD83D\\uDD0C Connect to IBKR\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 121,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: checkStatus,\n                                style: {\n                                    background: \"#6b7280\",\n                                    color: \"white\",\n                                    padding: \"10px 20px\",\n                                    border: \"none\",\n                                    borderRadius: \"5px\",\n                                    cursor: \"pointer\",\n                                    fontSize: \"16px\"\n                                },\n                                children: \"\\uD83D\\uDCCA Check Status\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                lineNumber: 108,\n                columnNumber: 7\n            }, this),\n            accountInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: \"#f0fdf4\",\n                    border: \"1px solid #bbf7d0\",\n                    padding: \"20px\",\n                    borderRadius: \"10px\",\n                    marginBottom: \"20px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        style: {\n                            color: \"#15803d\"\n                        },\n                        children: \"\\uD83D\\uDCB0 Account Information\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"grid\",\n                            gridTemplateColumns: \"repeat(auto-fit, minmax(200px, 1fr))\",\n                            gap: \"15px\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Net Liquidation:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 48\n                                    }, this),\n                                    \"$\",\n                                    accountInfo.netLiquidation?.toLocaleString() || \"N/A\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Available Funds:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 48\n                                    }, this),\n                                    \"$\",\n                                    accountInfo.availableFunds?.toLocaleString() || \"N/A\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Buying Power:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 45\n                                    }, this),\n                                    \"$\",\n                                    accountInfo.buyingPower?.toLocaleString() || \"N/A\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Total Cash:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 43\n                                    }, this),\n                                    \"$\",\n                                    accountInfo.totalCashValue?.toLocaleString() || \"N/A\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                lineNumber: 156,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: \"#fffbeb\",\n                    border: \"1px solid #fed7aa\",\n                    padding: \"20px\",\n                    borderRadius: \"10px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        style: {\n                            color: \"#d97706\"\n                        },\n                        children: \"\\uD83D\\uDD27 Troubleshooting\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            marginBottom: \"15px\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"If connection fails:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                style: {\n                                    marginTop: \"5px\",\n                                    lineHeight: \"1.6\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"Make sure TWS Gateway is running and logged in\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"Check that API is enabled: Configure → Settings → API → Settings\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"Verify port number: 7497 for paper trading, 7496 for live\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: 'Ensure \"Enable ActiveX and Socket Clients\" is checked'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"Try restarting TWS Gateway\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Common Error Messages:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                style: {\n                                    marginTop: \"5px\",\n                                    lineHeight: \"1.6\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Connection timeout:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 17\n                                            }, this),\n                                            \" TWS Gateway not running or wrong port\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Connection refused:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 17\n                                            }, this),\n                                            \" API not enabled in settings\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Authentication failed:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                                lineNumber: 198,\n                                                columnNumber: 17\n                                            }, this),\n                                            \" Wrong client ID or account\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                lineNumber: 179,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: \"#1e40af\",\n                    color: \"white\",\n                    padding: \"20px\",\n                    borderRadius: \"10px\",\n                    marginTop: \"20px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        children: \"\\uD83D\\uDE80 Quick Setup Links\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                        lineNumber: 204,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"grid\",\n                            gridTemplateColumns: \"repeat(auto-fit, minmax(250px, 1fr))\",\n                            gap: \"15px\",\n                            marginTop: \"15px\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    background: \"rgba(255,255,255,0.1)\",\n                                    padding: \"15px\",\n                                    borderRadius: \"5px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"\\uD83D\\uDCF1 Client Portal\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 46\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"https://www.interactivebrokers.com/sso/Login\",\n                                        target: \"_blank\",\n                                        style: {\n                                            color: \"#93c5fd\"\n                                        },\n                                        children: \"Enable API Access\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    background: \"rgba(255,255,255,0.1)\",\n                                    padding: \"15px\",\n                                    borderRadius: \"5px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"\\uD83D\\uDCBB Download IB Gateway\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 52\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"https://www.interactivebrokers.com/en/trading/ib-api.php\",\n                                        target: \"_blank\",\n                                        style: {\n                                            color: \"#93c5fd\"\n                                        },\n                                        children: \"Get IB Gateway\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    background: \"rgba(255,255,255,0.1)\",\n                                    padding: \"15px\",\n                                    borderRadius: \"5px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"\\uD83D\\uDCDA Paper Trading\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 46\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        style: {\n                                            color: \"#93c5fd\"\n                                        },\n                                        children: \"Enable in Client Portal Settings\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                        lineNumber: 205,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                lineNumber: 203,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: \"#22c55e\",\n                    color: \"white\",\n                    padding: \"15px\",\n                    borderRadius: \"10px\",\n                    marginTop: \"20px\",\n                    textAlign: \"center\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                            children: \"\\uD83D\\uDE80 Once connected, you'll have access to:\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 12\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"Professional options data • Real-time quotes • Advanced order types • Global markets\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                        lineNumber: 227,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                lineNumber: 225,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n        lineNumber: 67,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/ibkr-test/page.tsx\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"6adbdb4a3757\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9lbmhhbmNlZC1vcHRpb25zLXRyYWRpbmctcHJvZ3JhbS8uL2FwcC9nbG9iYWxzLmNzcz8wZWIyIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiNmFkYmRiNGEzNzU3XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/ibkr-test/page.tsx":
/*!********************************!*\
  !*** ./app/ibkr-test/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ericashacks\app\ibkr-test\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"Enhanced Options Trading Program\",\n    description: \"A comprehensive options trading system with enhanced strategies and user-friendly interface\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-gray-50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"bg-white shadow-sm border-b border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between h-16\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-xl font-semibold text-gray-900\",\n                                            children: \"Enhanced Options Trading\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\layout.tsx\",\n                                            lineNumber: 24,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\layout.tsx\",\n                                        lineNumber: 23,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-500\",\n                                            children: \"Smart Trading Without the Jargon\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\layout.tsx\",\n                                            lineNumber: 29,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\layout.tsx\",\n                                        lineNumber: 28,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\layout.tsx\",\n                                lineNumber: 22,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\layout.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\layout.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\layout.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\layout.tsx\",\n                lineNumber: 19,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\layout.tsx\",\n            lineNumber: 18,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\layout.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fibkr-test%2Fpage&page=%2Fibkr-test%2Fpage&appPaths=%2Fibkr-test%2Fpage&pagePath=private-next-app-dir%2Fibkr-test%2Fpage.tsx&appDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cericashacks%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cericashacks&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();