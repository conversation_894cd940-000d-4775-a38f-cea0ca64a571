/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/ibkr-test/page";
exports.ids = ["app/ibkr-test/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fibkr-test%2Fpage&page=%2Fibkr-test%2Fpage&appPaths=%2Fibkr-test%2Fpage&pagePath=private-next-app-dir%2Fibkr-test%2Fpage.tsx&appDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cericashacks%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cericashacks&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fibkr-test%2Fpage&page=%2Fibkr-test%2Fpage&appPaths=%2Fibkr-test%2Fpage&pagePath=private-next-app-dir%2Fibkr-test%2Fpage.tsx&appDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cericashacks%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cericashacks&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'ibkr-test',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/ibkr-test/page.tsx */ \"(rsc)/./app/ibkr-test/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/ibkr-test/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/ibkr-test/page\",\n        pathname: \"/ibkr-test\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fibkr-test%2Fpage&page=%2Fibkr-test%2Fpage&appPaths=%2Fibkr-test%2Fpage&pagePath=private-next-app-dir%2Fibkr-test%2Fpage.tsx&appDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cericashacks%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cericashacks&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSJFit%5C%5CDesktop%5C%5Cericashacks%5C%5Capp%5C%5Cibkr-test%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSJFit%5C%5CDesktop%5C%5Cericashacks%5C%5Capp%5C%5Cibkr-test%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/ibkr-test/page.tsx */ \"(ssr)/./app/ibkr-test/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1NKRml0JTVDJTVDRGVza3RvcCU1QyU1Q2VyaWNhc2hhY2tzJTVDJTVDYXBwJTVDJTVDaWJrci10ZXN0JTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDRKQUFxRyIsInNvdXJjZXMiOlsid2VicGFjazovL2VuaGFuY2VkLW9wdGlvbnMtdHJhZGluZy1wcm9ncmFtLz8wZmMzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcU0pGaXRcXFxcRGVza3RvcFxcXFxlcmljYXNoYWNrc1xcXFxhcHBcXFxcaWJrci10ZXN0XFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSJFit%5C%5CDesktop%5C%5Cericashacks%5C%5Capp%5C%5Cibkr-test%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSJFit%5C%5CDesktop%5C%5Cericashacks%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSJFit%5C%5CDesktop%5C%5Cericashacks%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSJFit%5C%5CDesktop%5C%5Cericashacks%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSJFit%5C%5CDesktop%5C%5Cericashacks%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSJFit%5C%5CDesktop%5C%5Cericashacks%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSJFit%5C%5CDesktop%5C%5Cericashacks%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSJFit%5C%5CDesktop%5C%5Cericashacks%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSJFit%5C%5CDesktop%5C%5Cericashacks%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSJFit%5C%5CDesktop%5C%5Cericashacks%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSJFit%5C%5CDesktop%5C%5Cericashacks%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSJFit%5C%5CDesktop%5C%5Cericashacks%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSJFit%5C%5CDesktop%5C%5Cericashacks%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1NKRml0JTVDJTVDRGVza3RvcCU1QyU1Q2VyaWNhc2hhY2tzJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDYXBwLXJvdXRlci5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNTSkZpdCU1QyU1Q0Rlc2t0b3AlNUMlNUNlcmljYXNoYWNrcyU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2NsaWVudC1wYWdlLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1NKRml0JTVDJTVDRGVza3RvcCU1QyU1Q2VyaWNhc2hhY2tzJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDZXJyb3ItYm91bmRhcnkuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDU0pGaXQlNUMlNUNEZXNrdG9wJTVDJTVDZXJpY2FzaGFja3MlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNsYXlvdXQtcm91dGVyLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1NKRml0JTVDJTVDRGVza3RvcCU1QyU1Q2VyaWNhc2hhY2tzJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDbm90LWZvdW5kLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1NKRml0JTVDJTVDRGVza3RvcCU1QyU1Q2VyaWNhc2hhY2tzJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDcmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa09BQXdJO0FBQ3hJO0FBQ0Esb09BQXlJO0FBQ3pJO0FBQ0EsME9BQTRJO0FBQzVJO0FBQ0Esd09BQTJJO0FBQzNJO0FBQ0Esa1BBQWdKO0FBQ2hKO0FBQ0Esc1FBQTBKIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZW5oYW5jZWQtb3B0aW9ucy10cmFkaW5nLXByb2dyYW0vP2NmZTIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxTSkZpdFxcXFxEZXNrdG9wXFxcXGVyaWNhc2hhY2tzXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcYXBwLXJvdXRlci5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcU0pGaXRcXFxcRGVza3RvcFxcXFxlcmljYXNoYWNrc1xcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGNsaWVudC1wYWdlLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxTSkZpdFxcXFxEZXNrdG9wXFxcXGVyaWNhc2hhY2tzXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcZXJyb3ItYm91bmRhcnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXFNKRml0XFxcXERlc2t0b3BcXFxcZXJpY2FzaGFja3NcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxsYXlvdXQtcm91dGVyLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxTSkZpdFxcXFxEZXNrdG9wXFxcXGVyaWNhc2hhY2tzXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcbm90LWZvdW5kLWJvdW5kYXJ5LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxTSkZpdFxcXFxEZXNrdG9wXFxcXGVyaWNhc2hhY2tzXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxccmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qc1wiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSJFit%5C%5CDesktop%5C%5Cericashacks%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSJFit%5C%5CDesktop%5C%5Cericashacks%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSJFit%5C%5CDesktop%5C%5Cericashacks%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSJFit%5C%5CDesktop%5C%5Cericashacks%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSJFit%5C%5CDesktop%5C%5Cericashacks%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSJFit%5C%5CDesktop%5C%5Cericashacks%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSJFit%5C%5CDesktop%5C%5Cericashacks%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSJFit%5C%5CDesktop%5C%5Cericashacks%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSJFit%5C%5CDesktop%5C%5Cericashacks%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSJFit%5C%5CDesktop%5C%5Cericashacks%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./app/ibkr-test/page.tsx":
/*!********************************!*\
  !*** ./app/ibkr-test/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ IBKRTestPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction IBKRTestPage() {\n    const [connectionStatus, setConnectionStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"Not Connected\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [accountInfo, setAccountInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const testConnection = async ()=>{\n        setLoading(true);\n        setError(\"\");\n        try {\n            console.log(\"\\uD83D\\uDD0C Testing IBKR connection...\");\n            const response = await fetch(\"/api/ibkr?action=connect\");\n            const result = await response.json();\n            if (result.success) {\n                setConnectionStatus(\"✅ Connected to IBKR!\");\n                console.log(\"✅ IBKR Connected successfully!\");\n                // Get account info\n                setTimeout(async ()=>{\n                    try {\n                        const accountResponse = await fetch(\"/api/ibkr?action=account\");\n                        const accountResult = await accountResponse.json();\n                        if (accountResult.success) {\n                            setAccountInfo(accountResult.data);\n                        }\n                    } catch (err) {\n                        console.log(\"Account info not available yet\");\n                    }\n                }, 2000);\n            } else {\n                setConnectionStatus(\"❌ Connection Failed\");\n                setError(result.message || \"Unknown error\");\n                console.error(\"❌ IBKR Connection failed:\", result.message);\n            }\n        } catch (err) {\n            setConnectionStatus(\"❌ Connection Error\");\n            setError(err instanceof Error ? err.message : \"Network error\");\n            console.error(\"❌ Connection error:\", err);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const checkStatus = async ()=>{\n        try {\n            const response = await fetch(\"/api/ibkr?action=status\");\n            const result = await response.json();\n            if (result.success) {\n                setConnectionStatus(result.data.connected ? \"✅ Connected\" : \"❌ Disconnected\");\n            }\n        } catch (err) {\n            setConnectionStatus(\"❌ Error checking status\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: \"20px\",\n            fontFamily: \"Arial\",\n            maxWidth: \"800px\",\n            margin: \"0 auto\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                style: {\n                    color: \"#1e40af\",\n                    marginBottom: \"20px\"\n                },\n                children: \"\\uD83C\\uDFE6 IBKR Connection Test\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: \"#f0f9ff\",\n                    padding: \"20px\",\n                    borderRadius: \"10px\",\n                    marginBottom: \"20px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        children: \"\\uD83D\\uDCCB Pre-Connection Checklist\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        style: {\n                            lineHeight: \"1.6\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"✅ TWS Gateway is running and logged in\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"✅ API is enabled in TWS Gateway settings\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"✅ Socket port is set to 7497 (paper) or 7496 (live)\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: '✅ \"Allow connections from localhost\" is checked'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                lineNumber: 70,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: \"white\",\n                    padding: \"20px\",\n                    borderRadius: \"10px\",\n                    boxShadow: \"0 2px 4px rgba(0,0,0,0.1)\",\n                    marginBottom: \"20px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        children: \"Connection Status\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        style: {\n                            fontSize: \"18px\",\n                            fontWeight: \"bold\",\n                            color: connectionStatus.includes(\"✅\") ? \"#22c55e\" : \"#ef4444\"\n                        },\n                        children: connectionStatus\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 9\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: \"#fef2f2\",\n                            border: \"1px solid #fecaca\",\n                            padding: \"10px\",\n                            borderRadius: \"5px\",\n                            marginTop: \"10px\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            style: {\n                                color: \"#dc2626\",\n                                margin: 0\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"Error:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 56\n                                }, this),\n                                \" \",\n                                error\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            marginTop: \"15px\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: testConnection,\n                                disabled: loading,\n                                style: {\n                                    background: loading ? \"#9ca3af\" : \"#1e40af\",\n                                    color: \"white\",\n                                    padding: \"10px 20px\",\n                                    border: \"none\",\n                                    borderRadius: \"5px\",\n                                    cursor: loading ? \"not-allowed\" : \"pointer\",\n                                    marginRight: \"10px\",\n                                    fontSize: \"16px\"\n                                },\n                                children: loading ? \"\\uD83D\\uDD04 Connecting...\" : \"\\uD83D\\uDD0C Connect to IBKR\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: checkStatus,\n                                style: {\n                                    background: \"#6b7280\",\n                                    color: \"white\",\n                                    padding: \"10px 20px\",\n                                    border: \"none\",\n                                    borderRadius: \"5px\",\n                                    cursor: \"pointer\",\n                                    fontSize: \"16px\"\n                                },\n                                children: \"\\uD83D\\uDCCA Check Status\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 110,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                lineNumber: 80,\n                columnNumber: 7\n            }, this),\n            accountInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: \"#f0fdf4\",\n                    border: \"1px solid #bbf7d0\",\n                    padding: \"20px\",\n                    borderRadius: \"10px\",\n                    marginBottom: \"20px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        style: {\n                            color: \"#15803d\"\n                        },\n                        children: \"\\uD83D\\uDCB0 Account Information\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"grid\",\n                            gridTemplateColumns: \"repeat(auto-fit, minmax(200px, 1fr))\",\n                            gap: \"15px\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Net Liquidation:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 48\n                                    }, this),\n                                    \"$\",\n                                    accountInfo.netLiquidation?.toLocaleString() || \"N/A\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Available Funds:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 48\n                                    }, this),\n                                    \"$\",\n                                    accountInfo.availableFunds?.toLocaleString() || \"N/A\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Buying Power:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 45\n                                    }, this),\n                                    \"$\",\n                                    accountInfo.buyingPower?.toLocaleString() || \"N/A\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Total Cash:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 43\n                                    }, this),\n                                    \"$\",\n                                    accountInfo.totalCashValue?.toLocaleString() || \"N/A\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                lineNumber: 128,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: \"#fffbeb\",\n                    border: \"1px solid #fed7aa\",\n                    padding: \"20px\",\n                    borderRadius: \"10px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        style: {\n                            color: \"#d97706\"\n                        },\n                        children: \"\\uD83D\\uDD27 Troubleshooting\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            marginBottom: \"15px\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"If connection fails:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                style: {\n                                    marginTop: \"5px\",\n                                    lineHeight: \"1.6\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"Make sure TWS Gateway is running and logged in\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"Check that API is enabled: Configure → Settings → API → Settings\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"Verify port number: 7497 for paper trading, 7496 for live\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: 'Ensure \"Enable ActiveX and Socket Clients\" is checked'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"Try restarting TWS Gateway\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Common Error Messages:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                style: {\n                                    marginTop: \"5px\",\n                                    lineHeight: \"1.6\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Connection timeout:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                                lineNumber: 168,\n                                                columnNumber: 17\n                                            }, this),\n                                            \" TWS Gateway not running or wrong port\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Connection refused:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                                lineNumber: 169,\n                                                columnNumber: 17\n                                            }, this),\n                                            \" API not enabled in settings\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Authentication failed:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                                lineNumber: 170,\n                                                columnNumber: 17\n                                            }, this),\n                                            \" Wrong client ID or account\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                lineNumber: 151,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: \"#22c55e\",\n                    color: \"white\",\n                    padding: \"15px\",\n                    borderRadius: \"10px\",\n                    marginTop: \"20px\",\n                    textAlign: \"center\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                            children: \"\\uD83D\\uDE80 Once connected, you'll have access to:\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 12\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"Professional options data • Real-time quotes • Advanced order types • Global markets\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n                lineNumber: 175,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\ibkr-test\\\\page.tsx\",\n        lineNumber: 67,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvaWJrci10ZXN0L3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUVpQztBQUVsQixTQUFTQztJQUN0QixNQUFNLENBQUNDLGtCQUFrQkMsb0JBQW9CLEdBQUdILCtDQUFRQSxDQUFDO0lBQ3pELE1BQU0sQ0FBQ0ksU0FBU0MsV0FBVyxHQUFHTCwrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUNNLGFBQWFDLGVBQWUsR0FBR1AsK0NBQVFBLENBQUM7SUFDL0MsTUFBTSxDQUFDUSxPQUFPQyxTQUFTLEdBQUdULCtDQUFRQSxDQUFDO0lBRW5DLE1BQU1VLGlCQUFpQjtRQUNyQkwsV0FBVztRQUNYSSxTQUFTO1FBRVQsSUFBSTtZQUNGRSxRQUFRQyxHQUFHLENBQUM7WUFFWixNQUFNQyxXQUFXLE1BQU1DLE1BQU07WUFDN0IsTUFBTUMsU0FBUyxNQUFNRixTQUFTRyxJQUFJO1lBRWxDLElBQUlELE9BQU9FLE9BQU8sRUFBRTtnQkFDbEJkLG9CQUFvQjtnQkFDcEJRLFFBQVFDLEdBQUcsQ0FBQztnQkFFWixtQkFBbUI7Z0JBQ25CTSxXQUFXO29CQUNULElBQUk7d0JBQ0YsTUFBTUMsa0JBQWtCLE1BQU1MLE1BQU07d0JBQ3BDLE1BQU1NLGdCQUFnQixNQUFNRCxnQkFBZ0JILElBQUk7d0JBRWhELElBQUlJLGNBQWNILE9BQU8sRUFBRTs0QkFDekJWLGVBQWVhLGNBQWNDLElBQUk7d0JBQ25DO29CQUNGLEVBQUUsT0FBT0MsS0FBSzt3QkFDWlgsUUFBUUMsR0FBRyxDQUFDO29CQUNkO2dCQUNGLEdBQUc7WUFFTCxPQUFPO2dCQUNMVCxvQkFBb0I7Z0JBQ3BCTSxTQUFTTSxPQUFPUSxPQUFPLElBQUk7Z0JBQzNCWixRQUFRSCxLQUFLLENBQUMsNkJBQTZCTyxPQUFPUSxPQUFPO1lBQzNEO1FBQ0YsRUFBRSxPQUFPRCxLQUFLO1lBQ1puQixvQkFBb0I7WUFDcEJNLFNBQVNhLGVBQWVFLFFBQVFGLElBQUlDLE9BQU8sR0FBRztZQUM5Q1osUUFBUUgsS0FBSyxDQUFDLHVCQUF1QmM7UUFDdkMsU0FBVTtZQUNSakIsV0FBVztRQUNiO0lBQ0Y7SUFFQSxNQUFNb0IsY0FBYztRQUNsQixJQUFJO1lBQ0YsTUFBTVosV0FBVyxNQUFNQyxNQUFNO1lBQzdCLE1BQU1DLFNBQVMsTUFBTUYsU0FBU0csSUFBSTtZQUVsQyxJQUFJRCxPQUFPRSxPQUFPLEVBQUU7Z0JBQ2xCZCxvQkFBb0JZLE9BQU9NLElBQUksQ0FBQ0ssU0FBUyxHQUFHLGdCQUFnQjtZQUM5RDtRQUNGLEVBQUUsT0FBT0osS0FBSztZQUNabkIsb0JBQW9CO1FBQ3RCO0lBQ0Y7SUFFQSxxQkFDRSw4REFBQ3dCO1FBQUlDLE9BQU87WUFBRUMsU0FBUztZQUFRQyxZQUFZO1lBQVNDLFVBQVU7WUFBU0MsUUFBUTtRQUFTOzswQkFDdEYsOERBQUNDO2dCQUFHTCxPQUFPO29CQUFFTSxPQUFPO29CQUFXQyxjQUFjO2dCQUFPOzBCQUFHOzs7Ozs7MEJBRXZELDhEQUFDUjtnQkFBSUMsT0FBTztvQkFBRVEsWUFBWTtvQkFBV1AsU0FBUztvQkFBUVEsY0FBYztvQkFBUUYsY0FBYztnQkFBTzs7a0NBQy9GLDhEQUFDRztrQ0FBRzs7Ozs7O2tDQUNKLDhEQUFDQzt3QkFBR1gsT0FBTzs0QkFBRVksWUFBWTt3QkFBTTs7MENBQzdCLDhEQUFDQzswQ0FBRzs7Ozs7OzBDQUNKLDhEQUFDQTswQ0FBRzs7Ozs7OzBDQUNKLDhEQUFDQTswQ0FBRzs7Ozs7OzBDQUNKLDhEQUFDQTswQ0FBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQUlSLDhEQUFDZDtnQkFBSUMsT0FBTztvQkFBRVEsWUFBWTtvQkFBU1AsU0FBUztvQkFBUVEsY0FBYztvQkFBUUssV0FBVztvQkFBNkJQLGNBQWM7Z0JBQU87O2tDQUNySSw4REFBQ1E7a0NBQUc7Ozs7OztrQ0FDSiw4REFBQ0M7d0JBQUVoQixPQUFPOzRCQUFFaUIsVUFBVTs0QkFBUUMsWUFBWTs0QkFBUVosT0FBT2hDLGlCQUFpQjZDLFFBQVEsQ0FBQyxPQUFPLFlBQVk7d0JBQVU7a0NBQzdHN0M7Ozs7OztvQkFHRk0sdUJBQ0MsOERBQUNtQjt3QkFBSUMsT0FBTzs0QkFBRVEsWUFBWTs0QkFBV1ksUUFBUTs0QkFBcUJuQixTQUFTOzRCQUFRUSxjQUFjOzRCQUFPWSxXQUFXO3dCQUFPO2tDQUN4SCw0RUFBQ0w7NEJBQUVoQixPQUFPO2dDQUFFTSxPQUFPO2dDQUFXRixRQUFROzRCQUFFOzs4Q0FBRyw4REFBQ2tCOzhDQUFPOzs7Ozs7Z0NBQWU7Z0NBQUUxQzs7Ozs7Ozs7Ozs7O2tDQUl4RSw4REFBQ21CO3dCQUFJQyxPQUFPOzRCQUFFcUIsV0FBVzt3QkFBTzs7MENBQzlCLDhEQUFDRTtnQ0FDQ0MsU0FBUzFDO2dDQUNUMkMsVUFBVWpEO2dDQUNWd0IsT0FBTztvQ0FDTFEsWUFBWWhDLFVBQVUsWUFBWTtvQ0FDbEM4QixPQUFPO29DQUNQTCxTQUFTO29DQUNUbUIsUUFBUTtvQ0FDUlgsY0FBYztvQ0FDZGlCLFFBQVFsRCxVQUFVLGdCQUFnQjtvQ0FDbENtRCxhQUFhO29DQUNiVixVQUFVO2dDQUNaOzBDQUVDekMsVUFBVSwrQkFBcUI7Ozs7OzswQ0FHbEMsOERBQUMrQztnQ0FDQ0MsU0FBUzNCO2dDQUNURyxPQUFPO29DQUNMUSxZQUFZO29DQUNaRixPQUFPO29DQUNQTCxTQUFTO29DQUNUbUIsUUFBUTtvQ0FDUlgsY0FBYztvQ0FDZGlCLFFBQVE7b0NBQ1JULFVBQVU7Z0NBQ1o7MENBQ0Q7Ozs7Ozs7Ozs7Ozs7Ozs7OztZQU1KdkMsNkJBQ0MsOERBQUNxQjtnQkFBSUMsT0FBTztvQkFBRVEsWUFBWTtvQkFBV1ksUUFBUTtvQkFBcUJuQixTQUFTO29CQUFRUSxjQUFjO29CQUFRRixjQUFjO2dCQUFPOztrQ0FDNUgsOERBQUNRO3dCQUFHZixPQUFPOzRCQUFFTSxPQUFPO3dCQUFVO2tDQUFHOzs7Ozs7a0NBQ2pDLDhEQUFDUDt3QkFBSUMsT0FBTzs0QkFBRTRCLFNBQVM7NEJBQVFDLHFCQUFxQjs0QkFBd0NDLEtBQUs7d0JBQU87OzBDQUN0Ryw4REFBQy9COztrREFDQyw4REFBQ3VCO2tEQUFPOzs7Ozs7a0RBQXlCLDhEQUFDUzs7Ozs7b0NBQUs7b0NBQ3JDckQsWUFBWXNELGNBQWMsRUFBRUMsb0JBQW9COzs7Ozs7OzBDQUVwRCw4REFBQ2xDOztrREFDQyw4REFBQ3VCO2tEQUFPOzs7Ozs7a0RBQXlCLDhEQUFDUzs7Ozs7b0NBQUs7b0NBQ3JDckQsWUFBWXdELGNBQWMsRUFBRUQsb0JBQW9COzs7Ozs7OzBDQUVwRCw4REFBQ2xDOztrREFDQyw4REFBQ3VCO2tEQUFPOzs7Ozs7a0RBQXNCLDhEQUFDUzs7Ozs7b0NBQUs7b0NBQ2xDckQsWUFBWXlELFdBQVcsRUFBRUYsb0JBQW9COzs7Ozs7OzBDQUVqRCw4REFBQ2xDOztrREFDQyw4REFBQ3VCO2tEQUFPOzs7Ozs7a0RBQW9CLDhEQUFDUzs7Ozs7b0NBQUs7b0NBQ2hDckQsWUFBWTBELGNBQWMsRUFBRUgsb0JBQW9COzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQU0xRCw4REFBQ2xDO2dCQUFJQyxPQUFPO29CQUFFUSxZQUFZO29CQUFXWSxRQUFRO29CQUFxQm5CLFNBQVM7b0JBQVFRLGNBQWM7Z0JBQU87O2tDQUN0Ryw4REFBQ007d0JBQUdmLE9BQU87NEJBQUVNLE9BQU87d0JBQVU7a0NBQUc7Ozs7OztrQ0FFakMsOERBQUNQO3dCQUFJQyxPQUFPOzRCQUFFTyxjQUFjO3dCQUFPOzswQ0FDakMsOERBQUNlOzBDQUFPOzs7Ozs7MENBQ1IsOERBQUNlO2dDQUFHckMsT0FBTztvQ0FBRXFCLFdBQVc7b0NBQU9ULFlBQVk7Z0NBQU07O2tEQUMvQyw4REFBQ0M7a0RBQUc7Ozs7OztrREFDSiw4REFBQ0E7a0RBQUc7Ozs7OztrREFDSiw4REFBQ0E7a0RBQUc7Ozs7OztrREFDSiw4REFBQ0E7a0RBQUc7Ozs7OztrREFDSiw4REFBQ0E7a0RBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FJUiw4REFBQ2Q7OzBDQUNDLDhEQUFDdUI7MENBQU87Ozs7OzswQ0FDUiw4REFBQ1g7Z0NBQUdYLE9BQU87b0NBQUVxQixXQUFXO29DQUFPVCxZQUFZO2dDQUFNOztrREFDL0MsOERBQUNDOzswREFBRyw4REFBQ1M7MERBQU87Ozs7Ozs0Q0FBNEI7Ozs7Ozs7a0RBQ3hDLDhEQUFDVDs7MERBQUcsOERBQUNTOzBEQUFPOzs7Ozs7NENBQTRCOzs7Ozs7O2tEQUN4Qyw4REFBQ1Q7OzBEQUFHLDhEQUFDUzswREFBTzs7Ozs7OzRDQUErQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFLakQsOERBQUN2QjtnQkFBSUMsT0FBTztvQkFBRVEsWUFBWTtvQkFBV0YsT0FBTztvQkFBU0wsU0FBUztvQkFBUVEsY0FBYztvQkFBUVksV0FBVztvQkFBUWlCLFdBQVc7Z0JBQVM7O2tDQUNqSSw4REFBQ3RCO2tDQUFFLDRFQUFDTTtzQ0FBTzs7Ozs7Ozs7Ozs7a0NBQ1gsOERBQUNOO2tDQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFJWCIsInNvdXJjZXMiOlsid2VicGFjazovL2VuaGFuY2VkLW9wdGlvbnMtdHJhZGluZy1wcm9ncmFtLy4vYXBwL2lia3ItdGVzdC9wYWdlLnRzeD85OWRlIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IHsgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCc7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIElCS1JUZXN0UGFnZSgpIHtcbiAgY29uc3QgW2Nvbm5lY3Rpb25TdGF0dXMsIHNldENvbm5lY3Rpb25TdGF0dXNdID0gdXNlU3RhdGUoJ05vdCBDb25uZWN0ZWQnKTtcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbYWNjb3VudEluZm8sIHNldEFjY291bnRJbmZvXSA9IHVzZVN0YXRlKG51bGwpO1xuICBjb25zdCBbZXJyb3IsIHNldEVycm9yXSA9IHVzZVN0YXRlKCcnKTtcblxuICBjb25zdCB0ZXN0Q29ubmVjdGlvbiA9IGFzeW5jICgpID0+IHtcbiAgICBzZXRMb2FkaW5nKHRydWUpO1xuICAgIHNldEVycm9yKCcnKTtcbiAgICBcbiAgICB0cnkge1xuICAgICAgY29uc29sZS5sb2coJ/CflIwgVGVzdGluZyBJQktSIGNvbm5lY3Rpb24uLi4nKTtcbiAgICAgIFxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS9pYmtyP2FjdGlvbj1jb25uZWN0Jyk7XG4gICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgICBcbiAgICAgIGlmIChyZXN1bHQuc3VjY2Vzcykge1xuICAgICAgICBzZXRDb25uZWN0aW9uU3RhdHVzKCfinIUgQ29ubmVjdGVkIHRvIElCS1IhJyk7XG4gICAgICAgIGNvbnNvbGUubG9nKCfinIUgSUJLUiBDb25uZWN0ZWQgc3VjY2Vzc2Z1bGx5IScpO1xuICAgICAgICBcbiAgICAgICAgLy8gR2V0IGFjY291bnQgaW5mb1xuICAgICAgICBzZXRUaW1lb3V0KGFzeW5jICgpID0+IHtcbiAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgY29uc3QgYWNjb3VudFJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGkvaWJrcj9hY3Rpb249YWNjb3VudCcpO1xuICAgICAgICAgICAgY29uc3QgYWNjb3VudFJlc3VsdCA9IGF3YWl0IGFjY291bnRSZXNwb25zZS5qc29uKCk7XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIGlmIChhY2NvdW50UmVzdWx0LnN1Y2Nlc3MpIHtcbiAgICAgICAgICAgICAgc2V0QWNjb3VudEluZm8oYWNjb3VudFJlc3VsdC5kYXRhKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9IGNhdGNoIChlcnIpIHtcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCdBY2NvdW50IGluZm8gbm90IGF2YWlsYWJsZSB5ZXQnKTtcbiAgICAgICAgICB9XG4gICAgICAgIH0sIDIwMDApO1xuICAgICAgICBcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHNldENvbm5lY3Rpb25TdGF0dXMoJ+KdjCBDb25uZWN0aW9uIEZhaWxlZCcpO1xuICAgICAgICBzZXRFcnJvcihyZXN1bHQubWVzc2FnZSB8fCAnVW5rbm93biBlcnJvcicpO1xuICAgICAgICBjb25zb2xlLmVycm9yKCfinYwgSUJLUiBDb25uZWN0aW9uIGZhaWxlZDonLCByZXN1bHQubWVzc2FnZSk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICBzZXRDb25uZWN0aW9uU3RhdHVzKCfinYwgQ29ubmVjdGlvbiBFcnJvcicpO1xuICAgICAgc2V0RXJyb3IoZXJyIGluc3RhbmNlb2YgRXJyb3IgPyBlcnIubWVzc2FnZSA6ICdOZXR3b3JrIGVycm9yJyk7XG4gICAgICBjb25zb2xlLmVycm9yKCfinYwgQ29ubmVjdGlvbiBlcnJvcjonLCBlcnIpO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgY2hlY2tTdGF0dXMgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGkvaWJrcj9hY3Rpb249c3RhdHVzJyk7XG4gICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgICBcbiAgICAgIGlmIChyZXN1bHQuc3VjY2Vzcykge1xuICAgICAgICBzZXRDb25uZWN0aW9uU3RhdHVzKHJlc3VsdC5kYXRhLmNvbm5lY3RlZCA/ICfinIUgQ29ubmVjdGVkJyA6ICfinYwgRGlzY29ubmVjdGVkJyk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICBzZXRDb25uZWN0aW9uU3RhdHVzKCfinYwgRXJyb3IgY2hlY2tpbmcgc3RhdHVzJyk7XG4gICAgfVxuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPGRpdiBzdHlsZT17eyBwYWRkaW5nOiAnMjBweCcsIGZvbnRGYW1pbHk6ICdBcmlhbCcsIG1heFdpZHRoOiAnODAwcHgnLCBtYXJnaW46ICcwIGF1dG8nIH19PlxuICAgICAgPGgxIHN0eWxlPXt7IGNvbG9yOiAnIzFlNDBhZicsIG1hcmdpbkJvdHRvbTogJzIwcHgnIH19PvCfj6YgSUJLUiBDb25uZWN0aW9uIFRlc3Q8L2gxPlxuICAgICAgXG4gICAgICA8ZGl2IHN0eWxlPXt7IGJhY2tncm91bmQ6ICcjZjBmOWZmJywgcGFkZGluZzogJzIwcHgnLCBib3JkZXJSYWRpdXM6ICcxMHB4JywgbWFyZ2luQm90dG9tOiAnMjBweCcgfX0+XG4gICAgICAgIDxoMj7wn5OLIFByZS1Db25uZWN0aW9uIENoZWNrbGlzdDwvaDI+XG4gICAgICAgIDx1bCBzdHlsZT17eyBsaW5lSGVpZ2h0OiAnMS42JyB9fT5cbiAgICAgICAgICA8bGk+4pyFIFRXUyBHYXRld2F5IGlzIHJ1bm5pbmcgYW5kIGxvZ2dlZCBpbjwvbGk+XG4gICAgICAgICAgPGxpPuKchSBBUEkgaXMgZW5hYmxlZCBpbiBUV1MgR2F0ZXdheSBzZXR0aW5nczwvbGk+XG4gICAgICAgICAgPGxpPuKchSBTb2NrZXQgcG9ydCBpcyBzZXQgdG8gNzQ5NyAocGFwZXIpIG9yIDc0OTYgKGxpdmUpPC9saT5cbiAgICAgICAgICA8bGk+4pyFIFwiQWxsb3cgY29ubmVjdGlvbnMgZnJvbSBsb2NhbGhvc3RcIiBpcyBjaGVja2VkPC9saT5cbiAgICAgICAgPC91bD5cbiAgICAgIDwvZGl2PlxuXG4gICAgICA8ZGl2IHN0eWxlPXt7IGJhY2tncm91bmQ6ICd3aGl0ZScsIHBhZGRpbmc6ICcyMHB4JywgYm9yZGVyUmFkaXVzOiAnMTBweCcsIGJveFNoYWRvdzogJzAgMnB4IDRweCByZ2JhKDAsMCwwLDAuMSknLCBtYXJnaW5Cb3R0b206ICcyMHB4JyB9fT5cbiAgICAgICAgPGgzPkNvbm5lY3Rpb24gU3RhdHVzPC9oMz5cbiAgICAgICAgPHAgc3R5bGU9e3sgZm9udFNpemU6ICcxOHB4JywgZm9udFdlaWdodDogJ2JvbGQnLCBjb2xvcjogY29ubmVjdGlvblN0YXR1cy5pbmNsdWRlcygn4pyFJykgPyAnIzIyYzU1ZScgOiAnI2VmNDQ0NCcgfX0+XG4gICAgICAgICAge2Nvbm5lY3Rpb25TdGF0dXN9XG4gICAgICAgIDwvcD5cbiAgICAgICAgXG4gICAgICAgIHtlcnJvciAmJiAoXG4gICAgICAgICAgPGRpdiBzdHlsZT17eyBiYWNrZ3JvdW5kOiAnI2ZlZjJmMicsIGJvcmRlcjogJzFweCBzb2xpZCAjZmVjYWNhJywgcGFkZGluZzogJzEwcHgnLCBib3JkZXJSYWRpdXM6ICc1cHgnLCBtYXJnaW5Ub3A6ICcxMHB4JyB9fT5cbiAgICAgICAgICAgIDxwIHN0eWxlPXt7IGNvbG9yOiAnI2RjMjYyNicsIG1hcmdpbjogMCB9fT48c3Ryb25nPkVycm9yOjwvc3Ryb25nPiB7ZXJyb3J9PC9wPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApfVxuICAgICAgICBcbiAgICAgICAgPGRpdiBzdHlsZT17eyBtYXJnaW5Ub3A6ICcxNXB4JyB9fT5cbiAgICAgICAgICA8YnV0dG9uIFxuICAgICAgICAgICAgb25DbGljaz17dGVzdENvbm5lY3Rpb259XG4gICAgICAgICAgICBkaXNhYmxlZD17bG9hZGluZ31cbiAgICAgICAgICAgIHN0eWxlPXt7IFxuICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiBsb2FkaW5nID8gJyM5Y2EzYWYnIDogJyMxZTQwYWYnLCBcbiAgICAgICAgICAgICAgY29sb3I6ICd3aGl0ZScsIFxuICAgICAgICAgICAgICBwYWRkaW5nOiAnMTBweCAyMHB4JywgXG4gICAgICAgICAgICAgIGJvcmRlcjogJ25vbmUnLCBcbiAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnNXB4JywgXG4gICAgICAgICAgICAgIGN1cnNvcjogbG9hZGluZyA/ICdub3QtYWxsb3dlZCcgOiAncG9pbnRlcicsXG4gICAgICAgICAgICAgIG1hcmdpblJpZ2h0OiAnMTBweCcsXG4gICAgICAgICAgICAgIGZvbnRTaXplOiAnMTZweCdcbiAgICAgICAgICAgIH19XG4gICAgICAgICAgPlxuICAgICAgICAgICAge2xvYWRpbmcgPyAn8J+UhCBDb25uZWN0aW5nLi4uJyA6ICfwn5SMIENvbm5lY3QgdG8gSUJLUid9XG4gICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgXG4gICAgICAgICAgPGJ1dHRvbiBcbiAgICAgICAgICAgIG9uQ2xpY2s9e2NoZWNrU3RhdHVzfVxuICAgICAgICAgICAgc3R5bGU9e3sgXG4gICAgICAgICAgICAgIGJhY2tncm91bmQ6ICcjNmI3MjgwJywgXG4gICAgICAgICAgICAgIGNvbG9yOiAnd2hpdGUnLCBcbiAgICAgICAgICAgICAgcGFkZGluZzogJzEwcHggMjBweCcsIFxuICAgICAgICAgICAgICBib3JkZXI6ICdub25lJywgXG4gICAgICAgICAgICAgIGJvcmRlclJhZGl1czogJzVweCcsIFxuICAgICAgICAgICAgICBjdXJzb3I6ICdwb2ludGVyJyxcbiAgICAgICAgICAgICAgZm9udFNpemU6ICcxNnB4J1xuICAgICAgICAgICAgfX1cbiAgICAgICAgICA+XG4gICAgICAgICAgICDwn5OKIENoZWNrIFN0YXR1c1xuICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7YWNjb3VudEluZm8gJiYgKFxuICAgICAgICA8ZGl2IHN0eWxlPXt7IGJhY2tncm91bmQ6ICcjZjBmZGY0JywgYm9yZGVyOiAnMXB4IHNvbGlkICNiYmY3ZDAnLCBwYWRkaW5nOiAnMjBweCcsIGJvcmRlclJhZGl1czogJzEwcHgnLCBtYXJnaW5Cb3R0b206ICcyMHB4JyB9fT5cbiAgICAgICAgICA8aDMgc3R5bGU9e3sgY29sb3I6ICcjMTU4MDNkJyB9fT7wn5KwIEFjY291bnQgSW5mb3JtYXRpb248L2gzPlxuICAgICAgICAgIDxkaXYgc3R5bGU9e3sgZGlzcGxheTogJ2dyaWQnLCBncmlkVGVtcGxhdGVDb2x1bW5zOiAncmVwZWF0KGF1dG8tZml0LCBtaW5tYXgoMjAwcHgsIDFmcikpJywgZ2FwOiAnMTVweCcgfX0+XG4gICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICA8c3Ryb25nPk5ldCBMaXF1aWRhdGlvbjo8L3N0cm9uZz48YnIgLz5cbiAgICAgICAgICAgICAgJHthY2NvdW50SW5mby5uZXRMaXF1aWRhdGlvbj8udG9Mb2NhbGVTdHJpbmcoKSB8fCAnTi9BJ31cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPHN0cm9uZz5BdmFpbGFibGUgRnVuZHM6PC9zdHJvbmc+PGJyIC8+XG4gICAgICAgICAgICAgICR7YWNjb3VudEluZm8uYXZhaWxhYmxlRnVuZHM/LnRvTG9jYWxlU3RyaW5nKCkgfHwgJ04vQSd9XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxzdHJvbmc+QnV5aW5nIFBvd2VyOjwvc3Ryb25nPjxiciAvPlxuICAgICAgICAgICAgICAke2FjY291bnRJbmZvLmJ1eWluZ1Bvd2VyPy50b0xvY2FsZVN0cmluZygpIHx8ICdOL0EnfVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICA8c3Ryb25nPlRvdGFsIENhc2g6PC9zdHJvbmc+PGJyIC8+XG4gICAgICAgICAgICAgICR7YWNjb3VudEluZm8udG90YWxDYXNoVmFsdWU/LnRvTG9jYWxlU3RyaW5nKCkgfHwgJ04vQSd9XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuXG4gICAgICA8ZGl2IHN0eWxlPXt7IGJhY2tncm91bmQ6ICcjZmZmYmViJywgYm9yZGVyOiAnMXB4IHNvbGlkICNmZWQ3YWEnLCBwYWRkaW5nOiAnMjBweCcsIGJvcmRlclJhZGl1czogJzEwcHgnIH19PlxuICAgICAgICA8aDMgc3R5bGU9e3sgY29sb3I6ICcjZDk3NzA2JyB9fT7wn5SnIFRyb3VibGVzaG9vdGluZzwvaDM+XG4gICAgICAgIFxuICAgICAgICA8ZGl2IHN0eWxlPXt7IG1hcmdpbkJvdHRvbTogJzE1cHgnIH19PlxuICAgICAgICAgIDxzdHJvbmc+SWYgY29ubmVjdGlvbiBmYWlsczo8L3N0cm9uZz5cbiAgICAgICAgICA8b2wgc3R5bGU9e3sgbWFyZ2luVG9wOiAnNXB4JywgbGluZUhlaWdodDogJzEuNicgfX0+XG4gICAgICAgICAgICA8bGk+TWFrZSBzdXJlIFRXUyBHYXRld2F5IGlzIHJ1bm5pbmcgYW5kIGxvZ2dlZCBpbjwvbGk+XG4gICAgICAgICAgICA8bGk+Q2hlY2sgdGhhdCBBUEkgaXMgZW5hYmxlZDogQ29uZmlndXJlIOKGkiBTZXR0aW5ncyDihpIgQVBJIOKGkiBTZXR0aW5nczwvbGk+XG4gICAgICAgICAgICA8bGk+VmVyaWZ5IHBvcnQgbnVtYmVyOiA3NDk3IGZvciBwYXBlciB0cmFkaW5nLCA3NDk2IGZvciBsaXZlPC9saT5cbiAgICAgICAgICAgIDxsaT5FbnN1cmUgXCJFbmFibGUgQWN0aXZlWCBhbmQgU29ja2V0IENsaWVudHNcIiBpcyBjaGVja2VkPC9saT5cbiAgICAgICAgICAgIDxsaT5UcnkgcmVzdGFydGluZyBUV1MgR2F0ZXdheTwvbGk+XG4gICAgICAgICAgPC9vbD5cbiAgICAgICAgPC9kaXY+XG4gICAgICAgIFxuICAgICAgICA8ZGl2PlxuICAgICAgICAgIDxzdHJvbmc+Q29tbW9uIEVycm9yIE1lc3NhZ2VzOjwvc3Ryb25nPlxuICAgICAgICAgIDx1bCBzdHlsZT17eyBtYXJnaW5Ub3A6ICc1cHgnLCBsaW5lSGVpZ2h0OiAnMS42JyB9fT5cbiAgICAgICAgICAgIDxsaT48c3Ryb25nPkNvbm5lY3Rpb24gdGltZW91dDo8L3N0cm9uZz4gVFdTIEdhdGV3YXkgbm90IHJ1bm5pbmcgb3Igd3JvbmcgcG9ydDwvbGk+XG4gICAgICAgICAgICA8bGk+PHN0cm9uZz5Db25uZWN0aW9uIHJlZnVzZWQ6PC9zdHJvbmc+IEFQSSBub3QgZW5hYmxlZCBpbiBzZXR0aW5nczwvbGk+XG4gICAgICAgICAgICA8bGk+PHN0cm9uZz5BdXRoZW50aWNhdGlvbiBmYWlsZWQ6PC9zdHJvbmc+IFdyb25nIGNsaWVudCBJRCBvciBhY2NvdW50PC9saT5cbiAgICAgICAgICA8L3VsPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICA8ZGl2IHN0eWxlPXt7IGJhY2tncm91bmQ6ICcjMjJjNTVlJywgY29sb3I6ICd3aGl0ZScsIHBhZGRpbmc6ICcxNXB4JywgYm9yZGVyUmFkaXVzOiAnMTBweCcsIG1hcmdpblRvcDogJzIwcHgnLCB0ZXh0QWxpZ246ICdjZW50ZXInIH19PlxuICAgICAgICA8cD48c3Ryb25nPvCfmoAgT25jZSBjb25uZWN0ZWQsIHlvdSdsbCBoYXZlIGFjY2VzcyB0bzo8L3N0cm9uZz48L3A+XG4gICAgICAgIDxwPlByb2Zlc3Npb25hbCBvcHRpb25zIGRhdGEg4oCiIFJlYWwtdGltZSBxdW90ZXMg4oCiIEFkdmFuY2VkIG9yZGVyIHR5cGVzIOKAoiBHbG9iYWwgbWFya2V0czwvcD5cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwiSUJLUlRlc3RQYWdlIiwiY29ubmVjdGlvblN0YXR1cyIsInNldENvbm5lY3Rpb25TdGF0dXMiLCJsb2FkaW5nIiwic2V0TG9hZGluZyIsImFjY291bnRJbmZvIiwic2V0QWNjb3VudEluZm8iLCJlcnJvciIsInNldEVycm9yIiwidGVzdENvbm5lY3Rpb24iLCJjb25zb2xlIiwibG9nIiwicmVzcG9uc2UiLCJmZXRjaCIsInJlc3VsdCIsImpzb24iLCJzdWNjZXNzIiwic2V0VGltZW91dCIsImFjY291bnRSZXNwb25zZSIsImFjY291bnRSZXN1bHQiLCJkYXRhIiwiZXJyIiwibWVzc2FnZSIsIkVycm9yIiwiY2hlY2tTdGF0dXMiLCJjb25uZWN0ZWQiLCJkaXYiLCJzdHlsZSIsInBhZGRpbmciLCJmb250RmFtaWx5IiwibWF4V2lkdGgiLCJtYXJnaW4iLCJoMSIsImNvbG9yIiwibWFyZ2luQm90dG9tIiwiYmFja2dyb3VuZCIsImJvcmRlclJhZGl1cyIsImgyIiwidWwiLCJsaW5lSGVpZ2h0IiwibGkiLCJib3hTaGFkb3ciLCJoMyIsInAiLCJmb250U2l6ZSIsImZvbnRXZWlnaHQiLCJpbmNsdWRlcyIsImJvcmRlciIsIm1hcmdpblRvcCIsInN0cm9uZyIsImJ1dHRvbiIsIm9uQ2xpY2siLCJkaXNhYmxlZCIsImN1cnNvciIsIm1hcmdpblJpZ2h0IiwiZGlzcGxheSIsImdyaWRUZW1wbGF0ZUNvbHVtbnMiLCJnYXAiLCJiciIsIm5ldExpcXVpZGF0aW9uIiwidG9Mb2NhbGVTdHJpbmciLCJhdmFpbGFibGVGdW5kcyIsImJ1eWluZ1Bvd2VyIiwidG90YWxDYXNoVmFsdWUiLCJvbCIsInRleHRBbGlnbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./app/ibkr-test/page.tsx\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"6adbdb4a3757\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9lbmhhbmNlZC1vcHRpb25zLXRyYWRpbmctcHJvZ3JhbS8uL2FwcC9nbG9iYWxzLmNzcz8wZWIyIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiNmFkYmRiNGEzNzU3XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/ibkr-test/page.tsx":
/*!********************************!*\
  !*** ./app/ibkr-test/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ericashacks\app\ibkr-test\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"Enhanced Options Trading Program\",\n    description: \"A comprehensive options trading system with enhanced strategies and user-friendly interface\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-gray-50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"bg-white shadow-sm border-b border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between h-16\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-xl font-semibold text-gray-900\",\n                                            children: \"Enhanced Options Trading\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\layout.tsx\",\n                                            lineNumber: 24,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\layout.tsx\",\n                                        lineNumber: 23,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-500\",\n                                            children: \"Smart Trading Without the Jargon\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\layout.tsx\",\n                                            lineNumber: 29,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\layout.tsx\",\n                                        lineNumber: 28,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\layout.tsx\",\n                                lineNumber: 22,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\layout.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\layout.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\layout.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\layout.tsx\",\n                lineNumber: 19,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\layout.tsx\",\n            lineNumber: 18,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\layout.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fibkr-test%2Fpage&page=%2Fibkr-test%2Fpage&appPaths=%2Fibkr-test%2Fpage&pagePath=private-next-app-dir%2Fibkr-test%2Fpage.tsx&appDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cericashacks%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cericashacks&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();