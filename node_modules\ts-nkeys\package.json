{"name": "ts-nkeys", "version": "1.0.16", "description": "A public-key signature system based on Ed25519 for the NATS ecosystem in typescript for ts-nats and node-nats", "main": "lib/nkeys.js", "types": "lib/nkeys.d.ts", "scripts": {"build": "npm run clean:dist && tsc && npm run copy:dist", "clean": "npm run clean:dist", "clean:dist": "rm -rf lib/* coverage/* .nyc_output/*", "copy:dist": "cp lib/src/* lib/", "cover:html": "nyc report --reporter=html && open coverage/index.html", "cover:coveralls": "nyc report --reporter=lcovonly && cat ./coverage/lcov.info | coveralls", "pack": "npm run build && npm pack", "debugtest": "tsc && node node_modules/.bin/ava --verbose -T 6500000 --match", "test": "tsc && nyc ava --verbose -T 15000"}, "repository": {"type": "git", "url": "https://github.com//nats-io/ts-nkeys"}, "keywords": ["nats", "Ed25519", "node", "typescript"], "author": "The NATS Authors", "license": "Apache-2.0", "devDependencies": {"@types/node": "^13.7.4", "ava": "^2.4.0", "coveralls": "^3.0.9", "dependency-check": "^4.1.0", "nyc": "^15.0.0", "ts-node": "^8.6.2", "typescript": "^3.8.2"}, "dependencies": {"tweetnacl": "^1.0.3"}, "ava": {"failFast": false, "require": ["./lib/test/helpers/ava_fix.js"], "files": ["./lib/test/**/*.js", "!./lib/test/helpers/**/*.js"], "sources": ["**/*.js"]}, "nyc": {"extension": [".ts", ".js"], "include": ["src/**/*.ts", "lib/src/**/*.js"], "exclude": ["lib/test/**", "examples/**", "benchmark/**"], "sourceMap": true, "all": false}}