"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AlpacaStocksClient = void 0;
const entityv2_1 = require("./entityv2");
const websocket_1 = require("./websocket");
class AlpacaStocksClient extends websocket_1.AlpacaWebsocket {
    constructor(options) {
        const url = "wss" + options.url.substr(options.url.indexOf(":")) + "/v2/" + options.feed;
        options.url = url;
        options.subscriptions = {
            trades: [],
            quotes: [],
            bars: [],
            updatedBars: [],
            dailyBars: [],
            statuses: [],
            lulds: [],
            cancelErrors: [],
            corrections: [],
        };
        super(options);
    }
    subscribeForTrades(trades) {
        this.session.subscriptions.trades.push(...trades);
        this.subscribe({ trades });
    }
    subscribeForQuotes(quotes) {
        this.session.subscriptions.quotes.push(...quotes);
        this.subscribe({ quotes });
    }
    subscribeForBars(bars) {
        this.session.subscriptions.bars.push(...bars);
        this.subscribe({ bars });
    }
    subscribeForUpdatedBars(updatedBars) {
        this.session.subscriptions.updatedBars.push(...updatedBars);
        this.subscribe({ updatedBars });
    }
    subscribeForDailyBars(dailyBars) {
        this.session.subscriptions.dailyBars.push(...dailyBars);
        this.subscribe({ dailyBars });
    }
    subscribeForStatuses(statuses) {
        this.session.subscriptions.statuses.push(...statuses);
        this.subscribe({ statuses });
    }
    subscribeForLulds(lulds) {
        this.session.subscriptions.lulds.push(...lulds);
        this.subscribe({ lulds });
    }
    subscribe(symbols) {
        var _a, _b, _c, _d, _e, _f, _g;
        const subMsg = {
            action: "subscribe",
            trades: (_a = symbols.trades) !== null && _a !== void 0 ? _a : [],
            quotes: (_b = symbols.quotes) !== null && _b !== void 0 ? _b : [],
            bars: (_c = symbols.bars) !== null && _c !== void 0 ? _c : [],
            updatedBars: (_d = symbols.updatedBars) !== null && _d !== void 0 ? _d : [],
            dailyBars: (_e = symbols.dailyBars) !== null && _e !== void 0 ? _e : [],
            statuses: (_f = symbols.statuses) !== null && _f !== void 0 ? _f : [],
            lulds: (_g = symbols.lulds) !== null && _g !== void 0 ? _g : [],
        };
        this.conn.send(this.msgpack.encode(subMsg));
    }
    subscribeAll() {
        this.subscribe(this.session.subscriptions);
    }
    unsubscribeFromTrades(trades) {
        this.session.subscriptions.trades = this.session.subscriptions.trades.filter((trade) => !trades.includes(trade));
        this.unsubscribe({ trades });
    }
    unsubscribeFromQuotes(quotes) {
        this.session.subscriptions.quotes = this.session.subscriptions.quotes.filter((quote) => !quotes.includes(quote));
        this.unsubscribe({ quotes });
    }
    unsubscribeFromBars(bars) {
        this.session.subscriptions.bars = this.session.subscriptions.bars.filter((bar) => !bars.includes(bar));
        this.unsubscribe({ bars });
    }
    unsubscribeFromUpdatedBars(updatedBars) {
        this.session.subscriptions.updatedBars =
            this.session.subscriptions.updatedBars.filter((updatedBar) => !updatedBars.includes(updatedBar));
        this.unsubscribe({ updatedBars });
    }
    unsubscribeFromDailyBars(dailyBars) {
        this.session.subscriptions.dailyBars = this.session.subscriptions.dailyBars.filter((dailyBar) => !dailyBars.includes(dailyBar));
        this.unsubscribe({ dailyBars });
    }
    unsubscribeFromStatuses(statuses) {
        this.session.subscriptions.statuses = this.session.subscriptions.statuses.filter((status) => !statuses.includes(status));
        this.unsubscribe({ statuses });
    }
    unsubscribeFromLulds(lulds) {
        this.session.subscriptions.lulds = this.session.subscriptions.lulds.filter((luld) => !lulds.includes(luld));
        this.unsubscribe({ lulds });
    }
    unsubscribe(symbols) {
        var _a, _b, _c, _d, _e, _f, _g;
        const unsubMsg = {
            action: "unsubscribe",
            trades: (_a = symbols.trades) !== null && _a !== void 0 ? _a : [],
            quotes: (_b = symbols.quotes) !== null && _b !== void 0 ? _b : [],
            bars: (_c = symbols.bars) !== null && _c !== void 0 ? _c : [],
            updatedBars: (_d = symbols.updatedBars) !== null && _d !== void 0 ? _d : [],
            dailyBars: (_e = symbols.dailyBars) !== null && _e !== void 0 ? _e : [],
            statuses: (_f = symbols.statuses) !== null && _f !== void 0 ? _f : [],
            lulds: (_g = symbols.lulds) !== null && _g !== void 0 ? _g : [],
        };
        this.conn.send(this.msgpack.encode(unsubMsg));
    }
    updateSubscriptions(msg) {
        this.log(`listening to streams:
        trades: ${msg.trades},
        quotes: ${msg.quotes},
        bars: ${msg.bars},
        updatedBars: ${msg.updatedBars},
        dailyBars: ${msg.dailyBars},
        statuses: ${msg.statuses},
        lulds: ${msg.lulds},
        cancelErrors: ${msg.cancelErrors},
        corrections: ${msg.corrections}`);
        this.session.subscriptions = {
            trades: msg.trades,
            quotes: msg.quotes,
            bars: msg.bars,
            updatedBars: msg.updatedBars,
            dailyBars: msg.dailyBars,
            statuses: msg.statuses,
            lulds: msg.lulds,
            cancelErrors: msg.cancelErrors,
            corrections: msg.corrections,
        };
    }
    onStockTrade(fn) {
        this.on(websocket_1.EVENT.TRADES, (trade) => fn(trade));
    }
    onStockQuote(fn) {
        this.on(websocket_1.EVENT.QUOTES, (quote) => fn(quote));
    }
    onStockBar(fn) {
        this.on(websocket_1.EVENT.BARS, (bar) => fn(bar));
    }
    onStockUpdatedBar(fn) {
        this.on(websocket_1.EVENT.UPDATED_BARS, (updatedBar) => fn(updatedBar));
    }
    onStockDailyBar(fn) {
        this.on(websocket_1.EVENT.DAILY_BARS, (dailyBar) => fn(dailyBar));
    }
    onStatuses(fn) {
        this.on(websocket_1.EVENT.TRADING_STATUSES, (status) => fn(status));
    }
    onLulds(fn) {
        this.on(websocket_1.EVENT.LULDS, (luld) => fn(luld));
    }
    onCancelErrors(fn) {
        this.on(websocket_1.EVENT.CANCEL_ERRORS, (cancelError) => fn(cancelError));
    }
    onCorrections(fn) {
        this.on(websocket_1.EVENT.CORRECTIONS, (correction) => fn(correction));
    }
    dataHandler(data) {
        data.forEach((element) => {
            if ("T" in element) {
                switch (element.T) {
                    case "t":
                        this.emit(websocket_1.EVENT.TRADES, (0, entityv2_1.AlpacaTradeV2)(element));
                        break;
                    case "q":
                        this.emit(websocket_1.EVENT.QUOTES, (0, entityv2_1.AlpacaQuoteV2)(element));
                        break;
                    case "b":
                        this.emit(websocket_1.EVENT.BARS, (0, entityv2_1.AlpacaBarV2)(element));
                        break;
                    case "u":
                        this.emit(websocket_1.EVENT.UPDATED_BARS, (0, entityv2_1.AlpacaBarV2)(element));
                        break;
                    case "d":
                        this.emit(websocket_1.EVENT.DAILY_BARS, (0, entityv2_1.AlpacaBarV2)(element));
                        break;
                    case "s":
                        this.emit(websocket_1.EVENT.TRADING_STATUSES, (0, entityv2_1.AlpacaStatusV2)(element));
                        break;
                    case "l":
                        this.emit(websocket_1.EVENT.LULDS, (0, entityv2_1.AlpacaLuldV2)(element));
                        break;
                    case "x":
                        this.emit(websocket_1.EVENT.CANCEL_ERRORS, (0, entityv2_1.AlpacaCancelErrorV2)(element));
                        break;
                    case "c":
                        this.emit(websocket_1.EVENT.CORRECTIONS, (0, entityv2_1.AlpacaCorrectionV2)(element));
                        break;
                    default:
                        this.emit(websocket_1.EVENT.CLIENT_ERROR, websocket_1.ERROR.UNEXPECTED_MESSAGE);
                }
            }
        });
    }
}
exports.AlpacaStocksClient = AlpacaStocksClient;
