import { AxiosResponse } from "axios";
import { CryptoQuote, CryptoTrade, CryptoBar, CryptoSnapshot, CryptoOrderbook, AlpacaSnapshot, AlpacaQuote, AlpacaTrade, AlpacaBar, AlpacaNews, AlpacaOptionBar, AlpacaOptionTrade, AlpacaOptionQuote, AlpacaOptionSnapshot, CorporateActions } from "./entityv2";
export declare enum Adjustment {
    RAW = "raw",
    DIVIDEND = "dividend",
    SPLIT = "split",
    ALL = "all"
}
export declare enum TYPE {
    TRADES = "trades",
    QUOTES = "quotes",
    BARS = "bars",
    SNAPSHOTS = "snapshots"
}
export declare function dataV2HttpRequest(url: string, queryParams: any, config: any): Promise<AxiosResponse<any>>;
export declare function getDataV2(endpoint: TYPE, path: string, options: any, config: any): AsyncGenerator<any, void, unknown>;
export declare function getMultiDataV2(symbols: Array<string>, url: string, endpoint: string, options: any, config: any): AsyncGenerator<{
    symbol: string;
    data: any;
}, void, unknown>;
export interface GetTradesParams {
    start?: string;
    end?: string;
    pageLimit?: number;
    limit?: number;
    feed?: string;
    asof?: string;
    page_token?: string;
    sort?: Sort;
}
export declare function getTrades(symbol: string, options: GetTradesParams, config: any): AsyncGenerator<AlpacaTrade, void, unknown>;
export declare function getMultiTrades(symbols: Array<string>, options: GetTradesParams, config: any): Promise<Map<string, AlpacaTrade[]>>;
export declare function getMultiTradesAsync(symbols: Array<string>, options: GetTradesParams, config: any): AsyncGenerator<AlpacaTrade, void, unknown>;
export interface GetQuotesParams {
    start?: string;
    end?: string;
    pageLimit?: number;
    limit?: number;
    feed?: string;
    asof?: string;
    page_token?: string;
    sort?: Sort;
}
export declare function getQuotes(symbol: string, options: GetQuotesParams, config: any): AsyncGenerator<AlpacaQuote, void, unknown>;
export declare function getMultiQuotes(symbols: Array<string>, options: GetQuotesParams, config: any): Promise<Map<string, AlpacaQuote[]>>;
export declare function getMultiQuotesAsync(symbols: Array<string>, options: GetQuotesParams, config: any): AsyncGenerator<AlpacaQuote, void, unknown>;
export interface GetBarsParams {
    timeframe: string;
    adjustment?: Adjustment;
    start?: string;
    end?: string;
    pageLimit?: number;
    limit?: number;
    feed?: string;
    asof?: string;
    page_token?: string;
    sort?: Sort;
}
export declare function getBars(symbol: string, options: GetBarsParams, config: any): AsyncGenerator<AlpacaBar, void, unknown>;
export declare function getMultiBars(symbols: Array<string>, options: GetBarsParams, config: any): Promise<Map<string, AlpacaBar[]>>;
export declare function getMultiBarsAsync(symbols: Array<string>, options: GetBarsParams, config: any): AsyncGenerator<AlpacaBar, void, unknown>;
export declare function getLatestTrade(symbol: string, config: any): Promise<AlpacaTrade>;
export declare function getLatestTrades(symbols: Array<string>, config: any): Promise<Map<string, AlpacaTrade>>;
export declare function getLatestQuote(symbol: string, config: any): Promise<AlpacaQuote>;
export declare function getLatestQuotes(symbols: Array<string>, config: any): Promise<Map<string, AlpacaQuote>>;
export declare function getLatestBar(symbol: string, config: any): Promise<AlpacaBar>;
export declare function getLatestBars(symbols: Array<string>, config: any): Promise<Map<string, AlpacaBar>>;
export declare function getSnapshot(symbol: string, config: any): Promise<AlpacaSnapshot>;
export declare function getSnapshots(symbols: Array<string>, config: any): Promise<AlpacaSnapshot[]>;
export interface GetCryptoTradesParams {
    start?: string;
    end?: string;
    limit?: number;
    pageLimit?: number;
    sort?: Sort;
}
export declare function getCryptoTrades(symbols: string[], options: GetCryptoTradesParams, config: any): Promise<Map<string, CryptoTrade[]>>;
export interface GetCryptoQuotesParams {
    start?: string;
    end?: string;
    limit?: number;
    pageLimit?: number;
    sort?: Sort;
}
export declare function getCryptoQuotes(symbols: string[], options: GetCryptoQuotesParams, config: any): Promise<Map<string, CryptoQuote[]>>;
export interface GetCryptoBarsParams {
    start?: string;
    end?: string;
    timeframe: string;
    limit?: number;
    pageLimit?: number;
    sort?: Sort;
}
export declare function getCryptoBars(symbols: string[], options: GetCryptoBarsParams, config: any): Promise<Map<string, CryptoBar[]>>;
export declare function getLatestCryptoBars(symbols: Array<string>, config: any): Promise<Map<string, CryptoBar>>;
export declare function getLatestCryptoTrades(symbols: Array<string>, config: any): Promise<Map<string, CryptoTrade>>;
export declare function getLatestCryptoQuotes(symbols: Array<string>, config: any): Promise<Map<string, CryptoQuote>>;
export declare function getCryptoSnapshots(symbols: Array<string>, config: any): Promise<Map<string, CryptoSnapshot>>;
export declare function getLatestCryptoOrderbooks(symbols: Array<string>, config: any): Promise<Map<string, CryptoOrderbook>>;
export declare enum Sort {
    ASC = "asc",
    DESC = "desc"
}
export interface GetNewsParams {
    symbols: Array<string>;
    start?: string;
    end?: string;
    sort?: Sort;
    includeContent?: boolean;
    excludeContentless?: boolean;
    totalLimit?: number;
    pageLimit?: number;
}
export declare function getNews(options: GetNewsParams, config: any): Promise<AlpacaNews[]>;
export interface GetOptionBarsParams {
    timeframe: string;
    start?: string;
    end?: string;
    pageLimit?: number;
    limit?: number;
    feed?: string;
    page_token?: string;
    sort?: Sort;
}
export declare function getMultiOptionBars(symbols: Array<string>, options: GetOptionBarsParams, config: any): Promise<Map<string, AlpacaOptionBar[]>>;
export declare function getMultiOptionBarsAsync(symbols: Array<string>, options: GetOptionBarsParams, config: any): AsyncGenerator<AlpacaOptionBar, void, unknown>;
export interface GetOptionTradesParams {
    start?: string;
    end?: string;
    pageLimit?: number;
    limit?: number;
    feed?: string;
    page_token?: string;
    sort?: Sort;
}
export declare function getMultiOptionTrades(symbols: Array<string>, options: GetOptionTradesParams, config: any): Promise<Map<string, AlpacaOptionTrade[]>>;
export declare function getMultiOptionTradesAsync(symbols: Array<string>, options: GetOptionTradesParams, config: any): AsyncGenerator<AlpacaOptionTrade, void, unknown>;
export declare function getLatestOptionTrades(symbols: Array<string>, config: any): Promise<Map<string, AlpacaOptionTrade>>;
export declare function getLatestOptionQuotes(symbols: Array<string>, config: any): Promise<Map<string, AlpacaOptionQuote>>;
export declare function getOptionSnapshots(symbols: Array<string>, config: any): Promise<AlpacaOptionSnapshot[]>;
export interface GetOptionChainParams {
    feed?: string;
    type?: string;
    pageLimit?: number;
    totalLimit?: number;
    strike_price_gte?: number;
    strike_price_lte?: number;
    expiration_date?: string;
    expiration_date_gte?: string;
    expiration_date_lte?: string;
    root_symbol?: string;
    page_token?: string;
}
export declare function getOptionChain(underlyingSymbol: string, options: GetOptionChainParams, config: any): Promise<AlpacaOptionSnapshot[]>;
export interface GetCorporateActionParams {
    types?: Array<string>;
    start?: string;
    end?: string;
    pageLimit?: number;
    totalLimit?: number;
    page_token?: string;
    sort?: Sort;
}
export declare function getCorporateActions(symbols: Array<string>, options: GetCorporateActionParams, config: any): Promise<CorporateActions | undefined>;
