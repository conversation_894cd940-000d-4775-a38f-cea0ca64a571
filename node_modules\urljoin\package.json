{"name": "url<PERSON>", "version": "0.1.5", "description": "Join the urls like joining the paths", "main": "index.js", "scripts": {"test": "grunt test", "coveralls": "istanbul cover -- grunt test && grunt coveralls"}, "repository": {"type": "git", "url": "https://github.com/yanni4night/urljoin.git"}, "keywords": ["url", "join"], "author": "<EMAIL>", "license": "MIT", "bugs": {"url": "https://github.com/yanni4night/urljoin/issues"}, "homepage": "https://github.com/yanni4night/urljoin", "devDependencies": {"grunt": "~0.4.5", "grunt-contrib-jshint": "~0.10.0", "grunt-contrib-nodeunit": "~0.4.1", "grunt-contrib-watch": "~0.6.1", "grunt-coveralls": "~1.0.0", "load-grunt-tasks": "~1.0.0", "time-grunt": "~1.0.0"}, "dependencies": {"extend": "~2.0.0"}}