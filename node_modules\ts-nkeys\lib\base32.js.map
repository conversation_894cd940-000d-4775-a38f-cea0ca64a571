{"version": 3, "file": "base32.js", "sourceRoot": "", "sources": ["../../src/base32.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;GAaG;;AAGH,kDAAkD;AAClD,mEAAmE;AACnE,2BAA2B;AAE3B;IAAA;IA8CA,CAAC;IA3CU,aAAM,GAAb,UAAc,GAAW;QACrB,IAAI,IAAI,GAAG,CAAC,CAAC;QACb,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,IAAI,CAAC,GAAG,IAAI,UAAU,CAAC,GAAG,CAAC,CAAC;QAC5B,IAAI,GAAG,GAAG,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC,UAAU,GAAC,CAAC,CAAC,CAAC;QAC/C,IAAI,CAAC,GAAG,CAAC,CAAC;QACV,KAAI,IAAI,CAAC,GAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,CAAC,EAAE,EAAE;YAChC,KAAK,GAAG,CAAC,KAAK,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5B,IAAI,IAAI,CAAC,CAAC;YACV,OAAM,IAAI,IAAI,CAAC,EAAE;gBACb,IAAI,KAAK,GAAG,CAAC,KAAK,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;gBACxC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;gBACvD,IAAI,IAAI,CAAC,CAAC;aACb;SACJ;QACD,IAAI,IAAI,GAAG,CAAC,EAAE;YACV,IAAI,KAAK,GAAG,CAAC,KAAK,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC;YACvC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;SAC1D;QACD,OAAO,GAAG,CAAC,KAAK,CAAC,CAAC,EAAC,CAAC,CAAC,CAAA;IACzB,CAAC;IAEM,aAAM,GAAb,UAAc,GAAW;QACrB,IAAI,IAAI,GAAG,CAAC,CAAC;QACb,IAAI,IAAI,GAAG,CAAC,CAAC;QACb,IAAI,CAAC,GAAG,CAAC,CAAC;QACV,IAAI,CAAC,GAAG,IAAI,UAAU,CAAC,GAAG,CAAC,CAAC;QAC5B,IAAI,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QACjD,KAAI,IAAI,CAAC,GAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,CAAC,EAAE,EAAE;YAChC,IAAI,CAAC,GAAG,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClC,IAAI,EAAE,GAAG,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YACpC,IAAG,EAAE,KAAK,CAAC,CAAC,EAAE;gBACV,MAAM,IAAI,KAAK,CAAC,4BAA4B,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;aACvD;YACD,IAAI,GAAG,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC;YACxB,IAAI,IAAI,CAAC,CAAC;YACV,IAAG,IAAI,IAAI,CAAC,EAAE;gBACV,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;gBACvC,IAAI,IAAI,CAAC,CAAC;aACb;SACJ;QACD,OAAO,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC3B,CAAC;IA5CM,eAAQ,GAAG,kCAAkC,CAAC;IA6CzD,aAAC;CAAA,AA9CD,IA8CC;AA9CY,wBAAM"}