# 🚀 Interactive Brokers (IBKR) API Setup Guide

## Why IBKR for Options Trading?

✅ **Best Options Data** - Real-time, accurate options chains  
✅ **Low Commissions** - $0.65 per options contract  
✅ **Professional Platform** - Used by hedge funds and pros  
✅ **Global Markets** - Trade options worldwide  
✅ **Advanced Tools** - Best-in-class options analytics  

## Step 1: Enable API Access in Your IBKR Account

### 1.1 Log into Client Portal
- Go to [https://www.interactivebrokers.com](https://www.interactivebrokers.com)
- Log into your account
- Go to **Account Management**

### 1.2 Enable API Access
- Navigate to **Settings** → **API** → **Settings**
- Check **"Enable ActiveX and Socket Clients"**
- Set **Socket Port** to `7497` (paper trading) or `7496` (live trading)
- Check **"Read-Only API"** if you only want market data
- **Save** your settings

### 1.3 Download TWS or IB Gateway
Choose one:

**Option A: TWS (Trader Workstation)** - Full trading platform
- Download from IBKR website
- More features but heavier

**Option B: IB Gateway** - Lightweight API server (RECOMMENDED)
- Download IB Gateway
- Lighter, perfect for API connections
- Runs in background

## Step 2: Configure Your System

### 2.1 Update Your Environment Variables
Edit your `.env.local` file:

```bash
# Interactive Brokers API Configuration
IBKR_HOST=127.0.0.1
IBKR_PORT=7497          # 7497 for paper, 7496 for live
IBKR_CLIENT_ID=1        # Unique client ID
IBKR_ACCOUNT_ID=DU123456 # Your IBKR account ID
```

### 2.2 Get Your Account ID
- In TWS/IB Gateway, your account ID is displayed at the top
- Paper accounts start with "DU"
- Live accounts start with "U"

## Step 3: Start TWS/IB Gateway

### 3.1 Launch the Platform
- Start TWS or IB Gateway
- Log in with your IBKR credentials
- Make sure API is enabled in settings

### 3.2 Configure API Settings
In TWS/Gateway:
- Go to **File** → **Global Configuration** → **API** → **Settings**
- Check **"Enable ActiveX and Socket Clients"**
- Set **Socket port** to `7497`
- Check **"Allow connections from localhost only"** for security
- **Apply** and **OK**

## Step 4: Test Your Connection

### 4.1 Verify TWS/Gateway is Running
- TWS/Gateway should show "Connected" status
- API should show as "Enabled"

### 4.2 Test API Connection
Your Enhanced Options Trading Program will automatically connect when you:
1. Start the application
2. Click on any strategy
3. System will connect to IBKR automatically

## Step 5: Trading Permissions

### 5.1 Enable Options Trading
In Client Portal:
- Go to **Account Management** → **Trading Permissions**
- Enable **Options Trading**
- Set appropriate risk levels

### 5.2 Set Trading Permissions
Enable:
- ✅ **Stocks**
- ✅ **Options**
- ✅ **Covered Calls**
- ✅ **Cash-Secured Puts**
- ✅ **Spreads** (if you want advanced strategies)

## Step 6: Paper Trading vs Live Trading

### Paper Trading (RECOMMENDED to start)
- **Port:** 7497
- **Account:** Starts with "DU"
- **Risk:** No real money
- **Perfect for:** Learning and testing

### Live Trading
- **Port:** 7496  
- **Account:** Starts with "U"
- **Risk:** Real money
- **Use when:** You're confident with the system

## Common Issues & Solutions

### Issue: "Connection Refused"
**Solution:**
1. Make sure TWS/Gateway is running
2. Check port number (7497 for paper, 7496 for live)
3. Verify API is enabled in TWS settings

### Issue: "Authentication Failed"
**Solution:**
1. Check your account ID is correct
2. Make sure you're logged into TWS/Gateway
3. Verify API permissions are enabled

### Issue: "No Market Data"
**Solution:**
1. Check your market data subscriptions
2. Verify you have real-time data permissions
3. Make sure you're connected during market hours

## Security Best Practices

### 🔒 Keep Your System Secure
- ✅ Only allow localhost connections
- ✅ Use strong passwords
- ✅ Keep TWS/Gateway updated
- ✅ Monitor API connections
- ✅ Start with paper trading

### 🚫 Never Share
- ❌ Your IBKR login credentials
- ❌ Your account ID publicly
- ❌ API access with others

## Next Steps

Once IBKR is connected:

1. **Test with Paper Trading** - Start safe with fake money
2. **Run Your First Strategy** - Try Income Collection first
3. **Monitor Performance** - Watch your trades in real-time
4. **Scale Up Gradually** - Increase position sizes as you gain confidence
5. **Go Live** - Switch to live trading when ready

## Support

### IBKR Support
- **Phone:** **************
- **Chat:** Available in Client Portal
- **API Documentation:** [IBKR API Docs](https://www.interactivebrokers.com/campus/ibkr-api-page/twsapi-doc/)

### Your System Support
- Check the console for connection messages
- All IBKR integration is automatic
- System will show connection status

---

**🚀 Ready to make money with professional-grade options trading through IBKR!**
