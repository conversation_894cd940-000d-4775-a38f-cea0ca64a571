// API Route for Stock Data
// Provides real-time stock prices and information

import { NextRequest, NextResponse } from 'next/server';
import { MarketDataService } from '../../../lib/api/market-data-service';
import fs from 'fs';
import path from 'path';

const marketData = new MarketDataService();

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const symbolsParam = searchParams.get('symbols');
    
    let symbols: string[] = [];
    
    if (symbolsParam) {
      // Use provided symbols
      symbols = symbolsParam.split(',').map(s => s.trim().toUpperCase());
    } else {
      // Read from watchlist.txt
      try {
        const watchlistPath = path.join(process.cwd(), 'watchlist.txt');
        const watchlistContent = fs.readFileSync(watchlistPath, 'utf-8');
        symbols = watchlistContent
          .split('\n')
          .map(line => line.trim())
          .filter(line => line && !line.startsWith('#'))
          .slice(0, 20); // Limit to 20 symbols to avoid API limits
      } catch (error) {
        // Fallback to default symbols
        symbols = ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'META', 'NVDA', 'JPM', 'JNJ', 'V'];
      }
    }

    if (symbols.length === 0) {
      return NextResponse.json({ error: 'No symbols provided' }, { status: 400 });
    }

    // Get stock data
    const stockData = await marketData.getStockData(symbols);
    
    return NextResponse.json({
      success: true,
      data: stockData,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error in stocks API:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch stock data',
        message: error instanceof Error ? error.message : 'Unknown error'
      }, 
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { symbols } = body;
    
    if (!symbols || !Array.isArray(symbols)) {
      return NextResponse.json({ error: 'Invalid symbols array' }, { status: 400 });
    }

    const stockData = await marketData.getStockData(symbols);
    
    return NextResponse.json({
      success: true,
      data: stockData,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error in stocks POST API:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch stock data',
        message: error instanceof Error ? error.message : 'Unknown error'
      }, 
      { status: 500 }
    );
  }
}
