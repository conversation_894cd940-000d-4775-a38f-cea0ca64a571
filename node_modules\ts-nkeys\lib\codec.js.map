{"version": 3, "file": "codec.js", "sourceRoot": "", "sources": ["../../src/codec.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;GAaG;;AAEH,iCAA8B;AAC9B,mCAAsC;AACtC,iCAAqE;AACrE,6BAA+B;AAC/B,mCAAgC;AAQhC;IAAA;IAsHA,CAAC;IAnHU,YAAM,GAAb,UAAc,MAAc,EAAE,GAAW;QACrC,IAAG,CAAE,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;YACvB,MAAM,IAAI,kBAAU,CAAC,sBAAc,CAAC,kBAAkB,CAAC,CAAC;SAC3D;QAED,IAAI,CAAC,gBAAQ,CAAC,aAAa,CAAC,MAAM,CAAC,EAAE;YACjC,MAAM,IAAI,kBAAU,CAAC,sBAAc,CAAC,iBAAiB,CAAC,CAAC;SAC1D;QAED,OAAO,KAAK,CAAC,OAAO,CAAC,KAAK,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC;IAC7C,CAAC;IAEM,gBAAU,GAAjB,UAAkB,IAAY,EAAE,GAAW;QACvC,IAAG,CAAE,gBAAQ,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE;YACrC,MAAM,IAAI,kBAAU,CAAC,sBAAc,CAAC,iBAAiB,CAAC,CAAC;SAC1D;QACD,IAAG,CAAE,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;YACvB,MAAM,IAAI,kBAAU,CAAC,sBAAc,CAAC,QAAQ,CAAC,CAAC;SACjD;QAED,IAAG,GAAG,CAAC,UAAU,IAAI,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE;YAC1C,MAAM,IAAI,kBAAU,CAAC,sBAAc,CAAC,cAAc,CAAC,CAAC;SACvD;QACD,OAAO,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;IAC1C,CAAC;IAEM,YAAM,GAAb,UAAc,QAAgB,EAAE,GAAW;QACvC,IAAG,CAAE,gBAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,EAAE;YACnC,MAAM,IAAI,kBAAU,CAAC,sBAAc,CAAC,iBAAiB,CAAC,CAAC;SAC1D;QACD,IAAI,GAAG,GAAG,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAC7B,IAAI,GAAG,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;YACrB,MAAM,IAAI,kBAAU,CAAC,sBAAc,CAAC,iBAAiB,CAAC,CAAC;SAC1D;QACD,OAAO,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IACxB,CAAC;IAEM,gBAAU,GAAjB,UAAkB,GAAW;QACzB,IAAI,GAAG,GAAG,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAC7B,IAAI,MAAM,GAAG,KAAK,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;QACtC,IAAI,MAAM,CAAC,CAAC,CAAC,IAAI,cAAM,CAAC,IAAI,EAAE;YAC1B,MAAM,IAAI,kBAAU,CAAC,sBAAc,CAAC,WAAW,CAAC,CAAC;SACpD;QACD,IAAI,CAAC,gBAAQ,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE;YAC1C,MAAM,IAAI,kBAAU,CAAC,sBAAc,CAAC,iBAAiB,CAAC,CAAC;SAC1D;QACD,OAAO,CAAC,EAAC,GAAG,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,EAAC,CAAC,CAAA;IACnD,CAAC;IAED,0CAA0C;IACnC,aAAO,GAAd,UAAe,IAAa,EAAE,IAAY,EAAE,OAAe;QACvD,yBAAyB;QACzB,IAAI,aAAa,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjC,IAAI,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;QACpC,IAAI,QAAQ,GAAG,CAAC,CAAC;QACjB,IAAI,GAAG,GAAG,aAAa,GAAG,UAAU,GAAG,QAAQ,CAAC;QAChD,IAAI,WAAW,GAAG,aAAa,GAAG,UAAU,CAAC;QAE7C,IAAI,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC5B,gDAAgD;QAChD,IAAI,IAAI,EAAE;YACN,IAAI,aAAa,GAAG,KAAK,CAAC,aAAa,CAAC,cAAM,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YAC3D,aAAa,CAAC,IAAI,CAAC,GAAG,EAAC,CAAC,EAAC,CAAC,CAAC,CAAC;SAC/B;aAAM;YACH,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;SACjB;QACD,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE,aAAa,EAAE,CAAC,CAAC,CAAC;QAEpC,oCAAoC;QACpC,IAAI,QAAQ,GAAG,aAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAC,WAAW,CAAC,CAAC,CAAC;QACxD,GAAG,CAAC,aAAa,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;QAEzC,OAAO,eAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;IAC9B,CAAC;IAED,4CAA4C;IACrC,aAAO,GAAd,UAAe,GAAW;QACtB,IAAI,GAAG,CAAC,UAAU,GAAG,CAAC,EAAE;YACpB,MAAM,IAAI,kBAAU,CAAC,sBAAc,CAAC,eAAe,CAAC,CAAC;SACxD;QACD,IAAI,GAAW,CAAC;QAChB,IAAI;YACA,GAAG,GAAG,eAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;SAC5B;QAAC,OAAM,EAAE,EAAE;YACR,MAAM,IAAI,kBAAU,CAAC,sBAAc,CAAC,eAAe,EAAE,EAAE,CAAC,CAAC;SAC5D;QAED,IAAI,WAAW,GAAG,GAAG,CAAC,UAAU,GAAG,CAAC,CAAC;QACrC,IAAI,QAAQ,GAAG,GAAG,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;QAE7C,IAAI,OAAO,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC;QACxC,IAAI,CAAC,aAAK,CAAC,QAAQ,CAAC,OAAO,EAAE,QAAQ,CAAC,EAAE;YACpC,MAAM,IAAI,kBAAU,CAAC,sBAAc,CAAC,eAAe,CAAC,CAAC;SACxD;QACD,OAAO,OAAO,CAAC;IACnB,CAAC;IAEM,mBAAa,GAApB,UAAqB,IAAY,EAAE,IAAY;QAC3C,+EAA+E;QAC/E,8EAA8E;QAC9E,IAAI,EAAE,GAAG,IAAI,GAAG,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC;QAC5B,IAAI,EAAE,GAAG,CAAC,IAAI,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,gBAAgB;QAC3C,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;IACjC,CAAC;IAEM,mBAAa,GAApB,UAAqB,GAAW;QAC5B,8DAA8D;QAC9D,uCAAuC;QACvC,IAAI,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,iBAAiB;QACxC,IAAI,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAC,CAAC,CAAC,IAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,eAAe;QAC/D,IAAI,CAAC,GAAG,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;QACV,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;QACV,OAAO,CAAC,CAAC;IACb,CAAC;IApHM,mBAAa,GAAuB,IAAI,CAAC,aAAa,EAAE,CAAC;IAqHpE,YAAC;CAAA,AAtHD,IAsHC;AAtHY,sBAAK"}