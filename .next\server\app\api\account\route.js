"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/account/route";
exports.ids = ["app/api/account/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Faccount%2Froute&page=%2Fapi%2Faccount%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Faccount%2Froute.ts&appDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cericashacks%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cericashacks&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Faccount%2Froute&page=%2Fapi%2Faccount%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Faccount%2Froute.ts&appDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cericashacks%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cericashacks&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_SJFit_Desktop_ericashacks_app_api_account_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/account/route.ts */ \"(rsc)/./app/api/account/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/account/route\",\n        pathname: \"/api/account\",\n        filename: \"route\",\n        bundlePath: \"app/api/account/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\api\\\\account\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_SJFit_Desktop_ericashacks_app_api_account_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/account/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Faccount%2Froute&page=%2Fapi%2Faccount%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Faccount%2Froute.ts&appDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cericashacks%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cericashacks&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/account/route.ts":
/*!**********************************!*\
  !*** ./app/api/account/route.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_api_market_data_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../lib/api/market-data-service */ \"(rsc)/./lib/api/market-data-service.ts\");\n// API Route for Account Information\n// Provides account balance, positions, and trading data\n\n\nconst marketData = new _lib_api_market_data_service__WEBPACK_IMPORTED_MODULE_1__.MarketDataService();\nasync function GET(request) {\n    try {\n        // Get account information and positions\n        const accountInfo = await marketData.getAccountInfo();\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: accountInfo,\n            timestamp: new Date().toISOString()\n        });\n    } catch (error) {\n        console.error(\"Error in account API:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"Failed to fetch account data\",\n            message: error instanceof Error ? error.message : \"Unknown error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/account/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/api/alpaca-client.ts":
/*!**********************************!*\
  !*** ./lib/api/alpaca-client.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AlpacaClient: () => (/* binding */ AlpacaClient)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(rsc)/./node_modules/axios/lib/axios.js\");\n// Alpaca API Client for Live Market Data and Trading\n// Provides real-time stock prices, options data, and portfolio management\n\nclass AlpacaClient {\n    constructor(){\n        this.apiKey = process.env.ALPACA_API_KEY || \"\";\n        this.secretKey = process.env.ALPACA_SECRET_KEY || \"\";\n        this.baseUrl = process.env.ALPACA_BASE_URL || \"https://paper-api.alpaca.markets\";\n        this.dataUrl = process.env.ALPACA_DATA_URL || \"https://data.alpaca.markets\";\n    }\n    getHeaders() {\n        return {\n            \"APCA-API-KEY-ID\": this.apiKey,\n            \"APCA-API-SECRET-KEY\": this.secretKey,\n            \"Content-Type\": \"application/json\"\n        };\n    }\n    /**\n   * Get current stock prices for multiple symbols\n   * Simple explanation: Get the latest prices for your watchlist stocks\n   */ async getStockPrices(symbols) {\n        try {\n            const symbolsParam = symbols.join(\",\");\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${this.dataUrl}/v2/stocks/quotes/latest`, {\n                headers: this.getHeaders(),\n                params: {\n                    symbols: symbolsParam,\n                    feed: \"iex\"\n                }\n            });\n            const quotes = response.data.quotes;\n            const stocks = {};\n            for (const symbol of symbols){\n                const quote = quotes[symbol];\n                if (quote) {\n                    // Get additional stock info\n                    const barsResponse = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${this.dataUrl}/v2/stocks/bars`, {\n                        headers: this.getHeaders(),\n                        params: {\n                            symbols: symbol,\n                            timeframe: \"1Day\",\n                            limit: 2,\n                            feed: \"iex\"\n                        }\n                    });\n                    const bars = barsResponse.data.bars[symbol];\n                    const currentBar = bars?.[bars.length - 1];\n                    const previousBar = bars?.[bars.length - 2];\n                    const currentPrice = quote.ap || quote.bp || currentBar?.c || 0;\n                    const previousClose = previousBar?.c || currentPrice;\n                    const change = currentPrice - previousClose;\n                    const changePercent = previousClose > 0 ? change / previousClose * 100 : 0;\n                    stocks[symbol] = {\n                        symbol,\n                        name: await this.getCompanyName(symbol),\n                        price: currentPrice,\n                        change,\n                        changePercent,\n                        volume: currentBar?.v || 0,\n                        marketCap: 0,\n                        sector: \"Unknown\" // Will be populated by FMP\n                    };\n                }\n            }\n            return stocks;\n        } catch (error) {\n            console.error(\"Error fetching stock prices from Alpaca:\", error);\n            throw new Error(\"Failed to fetch stock prices\");\n        }\n    }\n    /**\n   * Get account information and portfolio\n   * Simple explanation: Check your account balance and current positions\n   */ async getAccount() {\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${this.baseUrl}/v2/account`, {\n                headers: this.getHeaders()\n            });\n            const account = response.data;\n            return {\n                equity: parseFloat(account.equity),\n                cash: parseFloat(account.cash),\n                buyingPower: parseFloat(account.buying_power),\n                dayTradeCount: parseInt(account.daytrade_count),\n                portfolioValue: parseFloat(account.portfolio_value)\n            };\n        } catch (error) {\n            console.error(\"Error fetching account info from Alpaca:\", error);\n            throw new Error(\"Failed to fetch account information\");\n        }\n    }\n    /**\n   * Get current positions from Alpaca\n   * Simple explanation: See all your current stock and option positions\n   */ async getPositions() {\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${this.baseUrl}/v2/positions`, {\n                headers: this.getHeaders()\n            });\n            return response.data.map((position)=>({\n                    symbol: position.symbol,\n                    quantity: parseInt(position.qty),\n                    side: position.side,\n                    marketValue: parseFloat(position.market_value),\n                    costBasis: parseFloat(position.cost_basis),\n                    unrealizedPL: parseFloat(position.unrealized_pl),\n                    unrealizedPLPercent: parseFloat(position.unrealized_plpc) * 100,\n                    currentPrice: parseFloat(position.current_price),\n                    averageEntryPrice: parseFloat(position.avg_entry_price)\n                }));\n        } catch (error) {\n            console.error(\"Error fetching positions from Alpaca:\", error);\n            throw new Error(\"Failed to fetch positions\");\n        }\n    }\n    /**\n   * Get historical price data for analysis\n   * Simple explanation: Get past prices to analyze trends and patterns\n   */ async getHistoricalData(symbol, timeframe = \"1Day\", limit = 100) {\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${this.dataUrl}/v2/stocks/bars`, {\n                headers: this.getHeaders(),\n                params: {\n                    symbols: symbol,\n                    timeframe,\n                    limit,\n                    feed: \"iex\"\n                }\n            });\n            const bars = response.data.bars[symbol] || [];\n            return bars.map((bar)=>({\n                    timestamp: new Date(bar.t),\n                    open: bar.o,\n                    high: bar.h,\n                    low: bar.l,\n                    close: bar.c,\n                    volume: bar.v\n                }));\n        } catch (error) {\n            console.error(\"Error fetching historical data from Alpaca:\", error);\n            throw new Error(\"Failed to fetch historical data\");\n        }\n    }\n    /**\n   * Place a stock order\n   * Simple explanation: Buy or sell stocks through your account\n   */ async placeStockOrder(symbol, quantity, side, type = \"market\", limitPrice) {\n        try {\n            const orderData = {\n                symbol,\n                qty: quantity,\n                side,\n                type,\n                time_in_force: \"day\"\n            };\n            if (type === \"limit\" && limitPrice) {\n                orderData.limit_price = limitPrice;\n            }\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(`${this.baseUrl}/v2/orders`, orderData, {\n                headers: this.getHeaders()\n            });\n            return response.data;\n        } catch (error) {\n            console.error(\"Error placing stock order:\", error);\n            throw new Error(\"Failed to place stock order\");\n        }\n    }\n    /**\n   * Get order history\n   * Simple explanation: See all your past trades and their status\n   */ async getOrders(status = \"all\", limit = 50) {\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${this.baseUrl}/v2/orders`, {\n                headers: this.getHeaders(),\n                params: {\n                    status,\n                    limit,\n                    direction: \"desc\"\n                }\n            });\n            return response.data.map((order)=>({\n                    id: order.id,\n                    symbol: order.symbol,\n                    quantity: parseInt(order.qty),\n                    side: order.side,\n                    type: order.type,\n                    status: order.status,\n                    filledQuantity: parseInt(order.filled_qty || 0),\n                    filledPrice: parseFloat(order.filled_avg_price || 0),\n                    limitPrice: order.limit_price ? parseFloat(order.limit_price) : null,\n                    submittedAt: new Date(order.submitted_at),\n                    filledAt: order.filled_at ? new Date(order.filled_at) : null\n                }));\n        } catch (error) {\n            console.error(\"Error fetching orders from Alpaca:\", error);\n            throw new Error(\"Failed to fetch orders\");\n        }\n    }\n    /**\n   * Get company name for a symbol\n   * Simple explanation: Convert stock symbol to company name (AAPL -> Apple Inc.)\n   */ async getCompanyName(symbol) {\n        // This would typically come from a separate API or database\n        // For now, return the symbol as a fallback\n        const commonNames = {\n            \"AAPL\": \"Apple Inc.\",\n            \"MSFT\": \"Microsoft Corporation\",\n            \"GOOGL\": \"Alphabet Inc.\",\n            \"AMZN\": \"Amazon.com Inc.\",\n            \"TSLA\": \"Tesla Inc.\",\n            \"META\": \"Meta Platforms Inc.\",\n            \"NVDA\": \"NVIDIA Corporation\",\n            \"JPM\": \"JPMorgan Chase & Co.\",\n            \"JNJ\": \"Johnson & Johnson\",\n            \"V\": \"Visa Inc.\"\n        };\n        return commonNames[symbol] || `${symbol} Inc.`;\n    }\n    /**\n   * Check if markets are open\n   * Simple explanation: See if you can trade right now\n   */ async isMarketOpen() {\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${this.baseUrl}/v2/clock`, {\n                headers: this.getHeaders()\n            });\n            return response.data.is_open;\n        } catch (error) {\n            console.error(\"Error checking market status:\", error);\n            return false;\n        }\n    }\n    /**\n   * Get market calendar\n   * Simple explanation: See when markets are open/closed\n   */ async getMarketCalendar(start, end) {\n        try {\n            const params = {};\n            if (start) params.start = start;\n            if (end) params.end = end;\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${this.baseUrl}/v2/calendar`, {\n                headers: this.getHeaders(),\n                params\n            });\n            return response.data;\n        } catch (error) {\n            console.error(\"Error fetching market calendar:\", error);\n            throw new Error(\"Failed to fetch market calendar\");\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/api/alpaca-client.ts\n");

/***/ }),

/***/ "(rsc)/./lib/api/fmp-client.ts":
/*!*******************************!*\
  !*** ./lib/api/fmp-client.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FMPClient: () => (/* binding */ FMPClient)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(rsc)/./node_modules/axios/lib/axios.js\");\n// Financial Modeling Prep API Client\n// Provides comprehensive financial data, options chains, and company information\n\nclass FMPClient {\n    constructor(){\n        this.apiKey = process.env.FMP_API_KEY || \"\";\n        this.baseUrl = process.env.FMP_BASE_URL || \"https://financialmodelingprep.com/api\";\n    }\n    buildUrl(endpoint, params = {}) {\n        const url = new URL(`${this.baseUrl}${endpoint}`);\n        url.searchParams.append(\"apikey\", this.apiKey);\n        Object.entries(params).forEach(([key, value])=>{\n            if (value !== undefined && value !== null) {\n                url.searchParams.append(key, value.toString());\n            }\n        });\n        return url.toString();\n    }\n    /**\n   * Get comprehensive stock information\n   * Simple explanation: Get detailed info about companies including financials\n   */ async getStockInfo(symbols) {\n        try {\n            const symbolsParam = symbols.join(\",\");\n            // Get real-time quotes\n            const quotesUrl = this.buildUrl(\"/v3/quote\", {\n                symbols: symbolsParam\n            });\n            const quotesResponse = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(quotesUrl);\n            // Get company profiles for additional info\n            const profilesUrl = this.buildUrl(\"/v3/profile\", {\n                symbols: symbolsParam\n            });\n            const profilesResponse = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(profilesUrl);\n            const quotes = quotesResponse.data;\n            const profiles = profilesResponse.data;\n            const stocks = {};\n            quotes.forEach((quote)=>{\n                const profile = profiles.find((p)=>p.symbol === quote.symbol);\n                stocks[quote.symbol] = {\n                    symbol: quote.symbol,\n                    name: profile?.companyName || quote.name || `${quote.symbol} Inc.`,\n                    price: quote.price || 0,\n                    change: quote.change || 0,\n                    changePercent: quote.changesPercentage || 0,\n                    volume: quote.volume || 0,\n                    marketCap: profile?.mktCap || 0,\n                    sector: profile?.sector || \"Unknown\"\n                };\n            });\n            return stocks;\n        } catch (error) {\n            console.error(\"Error fetching stock info from FMP:\", error);\n            throw new Error(\"Failed to fetch stock information\");\n        }\n    }\n    /**\n   * Get options chain for a stock\n   * Simple explanation: Get all available option contracts for a stock\n   */ async getOptionsChain(symbol) {\n        try {\n            const url = this.buildUrl(`/v3/options-chain/${symbol}`);\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(url);\n            const optionsData = response.data;\n            const chainsByExpiration = {};\n            optionsData.forEach((option)=>{\n                const expiration = option.expiration;\n                if (!chainsByExpiration[expiration]) {\n                    chainsByExpiration[expiration] = {\n                        calls: [],\n                        puts: []\n                    };\n                }\n                const contract = {\n                    strike: option.strike,\n                    bid: option.bid || 0,\n                    ask: option.ask || 0,\n                    last: option.lastPrice || 0,\n                    volume: option.volume || 0,\n                    openInterest: option.openInterest || 0,\n                    impliedVolatility: option.impliedVolatility || 0,\n                    delta: option.delta || 0,\n                    gamma: option.gamma || 0,\n                    theta: option.theta || 0,\n                    vega: option.vega || 0,\n                    rho: option.rho || 0,\n                    intrinsicValue: this.calculateIntrinsicValue(option.type, option.strike, option.underlyingPrice),\n                    timeValue: (option.lastPrice || 0) - this.calculateIntrinsicValue(option.type, option.strike, option.underlyingPrice),\n                    daysToExpiration: this.calculateDaysToExpiration(option.expiration)\n                };\n                if (option.type === \"call\") {\n                    chainsByExpiration[expiration].calls.push(contract);\n                } else {\n                    chainsByExpiration[expiration].puts.push(contract);\n                }\n            });\n            return Object.entries(chainsByExpiration).map(([expiration, chains])=>({\n                    symbol,\n                    expiration,\n                    calls: chains.calls.sort((a, b)=>a.strike - b.strike),\n                    puts: chains.puts.sort((a, b)=>a.strike - b.strike)\n                }));\n        } catch (error) {\n            console.error(\"Error fetching options chain from FMP:\", error);\n            throw new Error(\"Failed to fetch options chain\");\n        }\n    }\n    /**\n   * Get historical stock prices for analysis\n   * Simple explanation: Get past stock prices to analyze trends\n   */ async getHistoricalPrices(symbol, period = \"1year\") {\n        try {\n            let endpoint = \"/v3/historical-price-full\";\n            let params = {};\n            if (period === \"1day\" || period === \"5day\") {\n                endpoint = \"/v3/historical-chart/1hour\";\n                params.from = this.getDateString(period === \"1day\" ? 1 : 5);\n                params.to = this.getDateString(0);\n            } else {\n                params.from = this.getDateString(this.getPeriodDays(period));\n                params.to = this.getDateString(0);\n            }\n            const url = this.buildUrl(`${endpoint}/${symbol}`, params);\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(url);\n            const historical = response.data.historical || response.data;\n            return historical.map((item)=>({\n                    date: new Date(item.date),\n                    open: item.open,\n                    high: item.high,\n                    low: item.low,\n                    close: item.close,\n                    volume: item.volume\n                })).reverse(); // Most recent first\n        } catch (error) {\n            console.error(\"Error fetching historical prices from FMP:\", error);\n            throw new Error(\"Failed to fetch historical prices\");\n        }\n    }\n    /**\n   * Get company financial ratios\n   * Simple explanation: Get key financial metrics to evaluate company health\n   */ async getFinancialRatios(symbol) {\n        try {\n            const url = this.buildUrl(`/v3/ratios/${symbol}`, {\n                limit: 1\n            });\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(url);\n            return response.data[0] || {};\n        } catch (error) {\n            console.error(\"Error fetching financial ratios from FMP:\", error);\n            throw new Error(\"Failed to fetch financial ratios\");\n        }\n    }\n    /**\n   * Get earnings calendar\n   * Simple explanation: See when companies will report their earnings\n   */ async getEarningsCalendar(from, to) {\n        try {\n            const params = {};\n            if (from) params.from = from;\n            if (to) params.to = to;\n            const url = this.buildUrl(\"/v3/earning_calendar\", params);\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(url);\n            return response.data.map((earning)=>({\n                    symbol: earning.symbol,\n                    date: earning.date,\n                    time: earning.time,\n                    epsEstimate: earning.epsEstimate,\n                    epsActual: earning.epsActual,\n                    revenueEstimate: earning.revenueEstimate,\n                    revenueActual: earning.revenueActual\n                }));\n        } catch (error) {\n            console.error(\"Error fetching earnings calendar from FMP:\", error);\n            throw new Error(\"Failed to fetch earnings calendar\");\n        }\n    }\n    /**\n   * Get dividend calendar\n   * Simple explanation: See when companies will pay dividends\n   */ async getDividendCalendar(from, to) {\n        try {\n            const params = {};\n            if (from) params.from = from;\n            if (to) params.to = to;\n            const url = this.buildUrl(\"/v3/stock_dividend_calendar\", params);\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(url);\n            return response.data.map((dividend)=>({\n                    symbol: dividend.symbol,\n                    date: dividend.date,\n                    recordDate: dividend.recordDate,\n                    paymentDate: dividend.paymentDate,\n                    declarationDate: dividend.declarationDate,\n                    dividend: dividend.dividend\n                }));\n        } catch (error) {\n            console.error(\"Error fetching dividend calendar from FMP:\", error);\n            throw new Error(\"Failed to fetch dividend calendar\");\n        }\n    }\n    /**\n   * Get market sector performance\n   * Simple explanation: See how different market sectors are performing\n   */ async getSectorPerformance() {\n        try {\n            const url = this.buildUrl(\"/v3/sector-performance\");\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(url);\n            return response.data.map((sector)=>({\n                    sector: sector.sector,\n                    changesPercentage: sector.changesPercentage\n                }));\n        } catch (error) {\n            console.error(\"Error fetching sector performance from FMP:\", error);\n            throw new Error(\"Failed to fetch sector performance\");\n        }\n    }\n    /**\n   * Get market indices (S&P 500, NASDAQ, etc.)\n   * Simple explanation: See how the overall market is performing\n   */ async getMarketIndices() {\n        try {\n            const indices = [\n                \"^GSPC\",\n                \"^IXIC\",\n                \"^DJI\",\n                \"^RUT\"\n            ]; // S&P 500, NASDAQ, Dow Jones, Russell 2000\n            const symbolsParam = indices.join(\",\");\n            const url = this.buildUrl(\"/v3/quote\", {\n                symbols: symbolsParam\n            });\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(url);\n            return response.data.map((index)=>({\n                    symbol: index.symbol,\n                    name: this.getIndexName(index.symbol),\n                    price: index.price,\n                    change: index.change,\n                    changesPercentage: index.changesPercentage\n                }));\n        } catch (error) {\n            console.error(\"Error fetching market indices from FMP:\", error);\n            throw new Error(\"Failed to fetch market indices\");\n        }\n    }\n    // Helper methods\n    calculateIntrinsicValue(type, strike, underlyingPrice) {\n        if (type === \"call\") {\n            return Math.max(0, underlyingPrice - strike);\n        } else {\n            return Math.max(0, strike - underlyingPrice);\n        }\n    }\n    calculateDaysToExpiration(expirationDate) {\n        const expiration = new Date(expirationDate);\n        const today = new Date();\n        const diffTime = expiration.getTime() - today.getTime();\n        return Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    }\n    getDateString(daysAgo) {\n        const date = new Date();\n        date.setDate(date.getDate() - daysAgo);\n        return date.toISOString().split(\"T\")[0];\n    }\n    getPeriodDays(period) {\n        switch(period){\n            case \"1day\":\n                return 1;\n            case \"5day\":\n                return 5;\n            case \"1month\":\n                return 30;\n            case \"3month\":\n                return 90;\n            case \"6month\":\n                return 180;\n            case \"1year\":\n                return 365;\n            case \"5year\":\n                return 1825;\n            default:\n                return 365;\n        }\n    }\n    getIndexName(symbol) {\n        const names = {\n            \"^GSPC\": \"S&P 500\",\n            \"^IXIC\": \"NASDAQ Composite\",\n            \"^DJI\": \"Dow Jones Industrial Average\",\n            \"^RUT\": \"Russell 2000\"\n        };\n        return names[symbol] || symbol;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/api/fmp-client.ts\n");

/***/ }),

/***/ "(rsc)/./lib/api/market-data-service.ts":
/*!****************************************!*\
  !*** ./lib/api/market-data-service.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MarketDataService: () => (/* binding */ MarketDataService)\n/* harmony export */ });\n/* harmony import */ var _alpaca_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./alpaca-client */ \"(rsc)/./lib/api/alpaca-client.ts\");\n/* harmony import */ var _fmp_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./fmp-client */ \"(rsc)/./lib/api/fmp-client.ts\");\n/* harmony import */ var _market_analysis__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../market-analysis */ \"(rsc)/./lib/market-analysis.ts\");\n/* harmony import */ var _utils_rate_limiter__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/rate-limiter */ \"(rsc)/./lib/utils/rate-limiter.ts\");\n// Market Data Service - Combines Alpaca and FMP data\n// Provides unified interface for all market data needs\n\n\n\n\nclass MarketDataService {\n    constructor(){\n        this.alpaca = new _alpaca_client__WEBPACK_IMPORTED_MODULE_0__.AlpacaClient();\n        this.fmp = new _fmp_client__WEBPACK_IMPORTED_MODULE_1__.FMPClient();\n    }\n    /**\n   * Get comprehensive stock data combining both APIs\n   * Simple explanation: Get complete stock information from multiple sources\n   */ async getStockData(symbols) {\n        try {\n            // Limit symbols to prevent rate limiting\n            const limitedSymbols = symbols.slice(0, 5);\n            // Wait for rate limits and get data from both sources\n            await Promise.all([\n                _utils_rate_limiter__WEBPACK_IMPORTED_MODULE_3__.rateLimiter.waitForAvailability(\"alpaca\"),\n                _utils_rate_limiter__WEBPACK_IMPORTED_MODULE_3__.rateLimiter.waitForAvailability(\"fmp\")\n            ]);\n            const [alpacaData, fmpData] = await Promise.allSettled([\n                this.getAlpacaDataSafely(limitedSymbols),\n                this.getFMPDataSafely(limitedSymbols)\n            ]);\n            const stocks = {};\n            // Combine data, preferring real-time prices from Alpaca and company info from FMP\n            limitedSymbols.forEach((symbol)=>{\n                let stock = {\n                    symbol,\n                    name: `${symbol} Inc.`,\n                    price: 0,\n                    change: 0,\n                    changePercent: 0,\n                    volume: 0,\n                    marketCap: 0,\n                    sector: \"Unknown\"\n                };\n                // Use FMP data as base (has company info)\n                if (fmpData.status === \"fulfilled\" && fmpData.value[symbol]) {\n                    stock = {\n                        ...stock,\n                        ...fmpData.value[symbol]\n                    };\n                }\n                // Override with Alpaca data if available (more real-time)\n                if (alpacaData.status === \"fulfilled\" && alpacaData.value[symbol]) {\n                    const alpacaStock = alpacaData.value[symbol];\n                    stock.price = alpacaStock.price;\n                    stock.change = alpacaStock.change;\n                    stock.changePercent = alpacaStock.changePercent;\n                    stock.volume = alpacaStock.volume;\n                }\n                stocks[symbol] = stock;\n            });\n            return stocks;\n        } catch (error) {\n            console.error(\"Error getting stock data:\", error);\n            throw new Error(\"Failed to fetch stock data\");\n        }\n    }\n    /**\n   * Get options chains for multiple symbols\n   * Simple explanation: Get all available option contracts for your stocks\n   */ async getOptionsChains(symbols) {\n        try {\n            const chains = {};\n            // Get options data from FMP (Alpaca doesn't provide options data in free tier)\n            const promises = symbols.map(async (symbol)=>{\n                try {\n                    const chain = await this.fmp.getOptionsChain(symbol);\n                    chains[symbol] = chain;\n                } catch (error) {\n                    console.error(`Error fetching options for ${symbol}:`, error);\n                    chains[symbol] = [];\n                }\n            });\n            await Promise.all(promises);\n            return chains;\n        } catch (error) {\n            console.error(\"Error getting options chains:\", error);\n            throw new Error(\"Failed to fetch options chains\");\n        }\n    }\n    /**\n   * Get comprehensive market analysis for symbols\n   * Simple explanation: Analyze market conditions to find trading opportunities\n   */ async getMarketAnalysis(symbols) {\n        try {\n            const analyses = {};\n            // Get historical data for analysis\n            const promises = symbols.map(async (symbol)=>{\n                try {\n                    // Get historical data from FMP (more comprehensive)\n                    const historicalData = await this.fmp.getHistoricalPrices(symbol, \"6month\");\n                    // Convert to format expected by MarketAnalyzer\n                    const analysisData = historicalData.map((item)=>({\n                            price: item.close,\n                            volume: item.volume,\n                            date: item.date\n                        }));\n                    // Get current stock data\n                    const stockData = await this.getStockData([\n                        symbol\n                    ]);\n                    const stock = stockData[symbol];\n                    if (stock && analysisData.length > 0) {\n                        // Calculate implied and historical volatility\n                        const optionsData = {\n                            impliedVolatility: 25,\n                            historicalVolatility: this.calculateHistoricalVolatility(analysisData)\n                        };\n                        analyses[symbol] = _market_analysis__WEBPACK_IMPORTED_MODULE_2__.MarketAnalyzer.analyzeMarket(stock, analysisData, optionsData);\n                    }\n                } catch (error) {\n                    console.error(`Error analyzing ${symbol}:`, error);\n                    // Provide default analysis\n                    analyses[symbol] = {\n                        regime: \"NEUTRAL_TO_BULLISH\",\n                        volatilityRank: 50,\n                        trend: \"NEUTRAL\",\n                        support: 0,\n                        resistance: 0,\n                        impliedVolatility: 25,\n                        historicalVolatility: 20\n                    };\n                }\n            });\n            await Promise.all(promises);\n            return analyses;\n        } catch (error) {\n            console.error(\"Error getting market analysis:\", error);\n            throw new Error(\"Failed to perform market analysis\");\n        }\n    }\n    /**\n   * Get account information and positions\n   * Simple explanation: Check your account balance and current trades\n   */ async getAccountInfo() {\n        try {\n            const [account, positions] = await Promise.all([\n                this.alpaca.getAccount(),\n                this.alpaca.getPositions()\n            ]);\n            return {\n                account,\n                positions\n            };\n        } catch (error) {\n            console.error(\"Error getting account info:\", error);\n            throw new Error(\"Failed to fetch account information\");\n        }\n    }\n    /**\n   * Get earnings calendar for the next few weeks\n   * Simple explanation: See when companies will report earnings\n   */ async getUpcomingEarnings(days = 14) {\n        try {\n            const today = new Date();\n            const futureDate = new Date();\n            futureDate.setDate(today.getDate() + days);\n            const earnings = await this.fmp.getEarningsCalendar(today.toISOString().split(\"T\")[0], futureDate.toISOString().split(\"T\")[0]);\n            return earnings;\n        } catch (error) {\n            console.error(\"Error getting earnings calendar:\", error);\n            return [];\n        }\n    }\n    /**\n   * Get dividend calendar for the next few weeks\n   * Simple explanation: See when companies will pay dividends\n   */ async getUpcomingDividends(days = 30) {\n        try {\n            const today = new Date();\n            const futureDate = new Date();\n            futureDate.setDate(today.getDate() + days);\n            const dividends = await this.fmp.getDividendCalendar(today.toISOString().split(\"T\")[0], futureDate.toISOString().split(\"T\")[0]);\n            return dividends;\n        } catch (error) {\n            console.error(\"Error getting dividend calendar:\", error);\n            return [];\n        }\n    }\n    /**\n   * Get market overview (indices, sector performance)\n   * Simple explanation: See how the overall market is doing\n   */ async getMarketOverview() {\n        try {\n            const [indices, sectors, isMarketOpen] = await Promise.all([\n                this.fmp.getMarketIndices(),\n                this.fmp.getSectorPerformance(),\n                this.alpaca.isMarketOpen()\n            ]);\n            return {\n                indices,\n                sectors,\n                isMarketOpen\n            };\n        } catch (error) {\n            console.error(\"Error getting market overview:\", error);\n            return {\n                indices: [],\n                sectors: [],\n                isMarketOpen: false\n            };\n        }\n    }\n    /**\n   * Place a stock order through Alpaca\n   * Simple explanation: Buy or sell stocks\n   */ async placeOrder(symbol, quantity, side, type = \"market\", limitPrice) {\n        try {\n            return await this.alpaca.placeStockOrder(symbol, quantity, side, type, limitPrice);\n        } catch (error) {\n            console.error(\"Error placing order:\", error);\n            throw new Error(\"Failed to place order\");\n        }\n    }\n    /**\n   * Get order history\n   * Simple explanation: See your past trades\n   */ async getOrderHistory(limit = 50) {\n        try {\n            return await this.alpaca.getOrders(\"all\", limit);\n        } catch (error) {\n            console.error(\"Error getting order history:\", error);\n            return [];\n        }\n    }\n    // Helper methods for safe API calls\n    async getAlpacaDataSafely(symbols) {\n        try {\n            await _utils_rate_limiter__WEBPACK_IMPORTED_MODULE_3__.rateLimiter.waitForAvailability(\"alpaca\");\n            _utils_rate_limiter__WEBPACK_IMPORTED_MODULE_3__.rateLimiter.recordRequest(\"alpaca\");\n            return await this.alpaca.getStockPrices(symbols);\n        } catch (error) {\n            console.error(\"Alpaca API error:\", error);\n            return {};\n        }\n    }\n    async getFMPDataSafely(symbols) {\n        try {\n            await _utils_rate_limiter__WEBPACK_IMPORTED_MODULE_3__.rateLimiter.waitForAvailability(\"fmp\");\n            _utils_rate_limiter__WEBPACK_IMPORTED_MODULE_3__.rateLimiter.recordRequest(\"fmp\");\n            return await this.fmp.getStockInfo(symbols);\n        } catch (error) {\n            console.error(\"FMP API error:\", error);\n            return {};\n        }\n    }\n    calculateHistoricalVolatility(data) {\n        if (data.length < 20) return 20;\n        // Calculate daily returns\n        const returns = [];\n        for(let i = 1; i < data.length; i++){\n            const dailyReturn = (data[i].price - data[i - 1].price) / data[i - 1].price;\n            returns.push(dailyReturn);\n        }\n        // Calculate standard deviation\n        const mean = returns.reduce((sum, ret)=>sum + ret, 0) / returns.length;\n        const squaredDiffs = returns.map((ret)=>Math.pow(ret - mean, 2));\n        const variance = squaredDiffs.reduce((sum, diff)=>sum + diff, 0) / returns.length;\n        const stdDev = Math.sqrt(variance);\n        // Annualize (252 trading days per year)\n        return stdDev * Math.sqrt(252) * 100;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/api/market-data-service.ts\n");

/***/ }),

/***/ "(rsc)/./lib/market-analysis.ts":
/*!********************************!*\
  !*** ./lib/market-analysis.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MarketAnalyzer: () => (/* binding */ MarketAnalyzer)\n/* harmony export */ });\n// Market Analysis and Alert System\n// Smart market detection without complex jargon\nclass MarketAnalyzer {\n    /**\n   * Analyze current market conditions for a stock\n   * Simple explanation: Like a weather forecast for your stock\n   */ static analyzeMarket(stock, historicalData, optionsData) {\n        const trend = this.determineTrend(historicalData);\n        const volatilityRank = this.calculateVolatilityRank(historicalData);\n        const regime = this.determineMarketRegime(trend, volatilityRank, stock);\n        const supportResistance = this.findSupportResistance(historicalData);\n        return {\n            regime,\n            volatilityRank,\n            trend,\n            support: supportResistance.support,\n            resistance: supportResistance.resistance,\n            impliedVolatility: optionsData?.impliedVolatility || 25,\n            historicalVolatility: optionsData?.historicalVolatility || 20,\n            earnings: this.getNextEarningsDate(stock.symbol),\n            dividend: this.getNextDividendInfo(stock.symbol)\n        };\n    }\n    /**\n   * Determine the best trading strategies for current market conditions\n   * Simple explanation: Like a GPS that tells you the best route based on traffic\n   */ static recommendStrategies(analysis, stock) {\n        const strategies = [];\n        switch(analysis.regime){\n            case \"NEUTRAL_TO_BULLISH\":\n                strategies.push(\"POOR_MANS_COVERED_CALL\", \"CALENDAR_SPREAD\", \"COVERED_CALL\");\n                if (analysis.volatilityRank < 30) {\n                    strategies.push(\"LEAPS\");\n                }\n                break;\n            case \"BEARISH_HIGH_IV\":\n                strategies.push(\"BEAR_CALL_SPREAD\", \"CASH_SECURED_PUT\");\n                if (analysis.volatilityRank > 70) {\n                    strategies.push(\"IRON_CONDOR\");\n                }\n                break;\n            case \"STRONG_BULLISH\":\n                strategies.push(\"LEAPS\", \"CASH_SECURED_PUT\");\n                if (stock.price > analysis.support * 1.05) {\n                    strategies.push(\"COVERED_CALL\");\n                }\n                break;\n            case \"CHOPPY_RANGE\":\n                strategies.push(\"THE_WHEEL\", \"IRON_CONDOR\", \"BUTTERFLY_SPREAD\");\n                break;\n            case \"HIGH_VOLATILITY\":\n                strategies.push(\"IRON_CONDOR\", \"BEAR_CALL_SPREAD\", \"BUTTERFLY_SPREAD\");\n                break;\n            case \"LOW_VOLATILITY\":\n                strategies.push(\"CALENDAR_SPREAD\", \"DIAGONAL_SPREAD\", \"LEAPS\");\n                break;\n        }\n        return strategies;\n    }\n    /**\n   * Generate trading alerts based on market conditions\n   * Simple explanation: Like notifications that tell you when good opportunities appear\n   */ static generateTradingAlerts(stocks, analyses) {\n        const alerts = [];\n        stocks.forEach((stock)=>{\n            const analysis = analyses[stock.symbol];\n            if (!analysis) return;\n            // High volatility opportunities\n            if (analysis.volatilityRank > 80) {\n                alerts.push({\n                    symbol: stock.symbol,\n                    type: \"OPPORTUNITY\",\n                    message: `${stock.symbol} has very high volatility - great for selling premium`,\n                    priority: \"HIGH\",\n                    strategies: [\n                        \"IRON_CONDOR\",\n                        \"BEAR_CALL_SPREAD\",\n                        \"CASH_SECURED_PUT\"\n                    ]\n                });\n            }\n            // Low volatility opportunities\n            if (analysis.volatilityRank < 20 && analysis.trend === \"BULLISH\") {\n                alerts.push({\n                    symbol: stock.symbol,\n                    type: \"OPPORTUNITY\",\n                    message: `${stock.symbol} has low volatility in uptrend - good for buying options`,\n                    priority: \"MEDIUM\",\n                    strategies: [\n                        \"LEAPS\",\n                        \"CALENDAR_SPREAD\"\n                    ]\n                });\n            }\n            // Support/Resistance alerts\n            const distanceFromSupport = (stock.price - analysis.support) / stock.price;\n            const distanceFromResistance = (analysis.resistance - stock.price) / stock.price;\n            if (distanceFromSupport < 0.02) {\n                alerts.push({\n                    symbol: stock.symbol,\n                    type: \"OPPORTUNITY\",\n                    message: `${stock.symbol} is near support at $${analysis.support.toFixed(2)} - potential bounce`,\n                    priority: \"HIGH\",\n                    strategies: [\n                        \"CASH_SECURED_PUT\",\n                        \"LEAPS\"\n                    ]\n                });\n            }\n            if (distanceFromResistance < 0.02) {\n                alerts.push({\n                    symbol: stock.symbol,\n                    type: \"WARNING\",\n                    message: `${stock.symbol} is near resistance at $${analysis.resistance.toFixed(2)} - may struggle to rise`,\n                    priority: \"MEDIUM\",\n                    strategies: [\n                        \"BEAR_CALL_SPREAD\",\n                        \"COVERED_CALL\"\n                    ]\n                });\n            }\n            // Earnings warnings\n            if (analysis.earnings && this.daysBetween(new Date(), analysis.earnings) <= 7) {\n                alerts.push({\n                    symbol: stock.symbol,\n                    type: \"WARNING\",\n                    message: `${stock.symbol} reports earnings in ${this.daysBetween(new Date(), analysis.earnings)} days - expect volatility`,\n                    priority: \"HIGH\",\n                    strategies: []\n                });\n            }\n            // Dividend alerts\n            if (analysis.dividend && this.daysBetween(new Date(), analysis.dividend.exDate) <= 5) {\n                alerts.push({\n                    symbol: stock.symbol,\n                    type: \"INFO\",\n                    message: `${stock.symbol} goes ex-dividend in ${this.daysBetween(new Date(), analysis.dividend.exDate)} days ($${analysis.dividend.amount})`,\n                    priority: \"MEDIUM\",\n                    strategies: [\n                        \"COVERED_CALL\"\n                    ]\n                });\n            }\n        });\n        return alerts.sort((a, b)=>{\n            const priorityOrder = {\n                \"HIGH\": 3,\n                \"MEDIUM\": 2,\n                \"LOW\": 1\n            };\n            return priorityOrder[b.priority] - priorityOrder[a.priority];\n        });\n    }\n    /**\n   * Calculate the optimal entry timing for a strategy\n   * Simple explanation: Like timing when to enter a busy restaurant for the best service\n   */ static calculateOptimalEntry(stock, analysis, strategy) {\n        const currentPrice = stock.price;\n        const support = analysis.support;\n        const resistance = analysis.resistance;\n        // Strategy-specific timing logic\n        switch(strategy){\n            case \"CASH_SECURED_PUT\":\n                if (currentPrice > support * 1.1) {\n                    return {\n                        timing: \"WAIT_FOR_DIP\",\n                        reason: \"Wait for stock to get closer to support for better entry\",\n                        targetPrice: support * 1.05\n                    };\n                }\n                return {\n                    timing: \"IMMEDIATE\",\n                    reason: \"Good price level for selling puts\"\n                };\n            case \"BEAR_CALL_SPREAD\":\n                if (currentPrice < resistance * 0.9) {\n                    return {\n                        timing: \"WAIT_FOR_RISE\",\n                        reason: \"Wait for stock to get closer to resistance\",\n                        targetPrice: resistance * 0.95\n                    };\n                }\n                return {\n                    timing: \"IMMEDIATE\",\n                    reason: \"Good level to sell calls against resistance\"\n                };\n            case \"LEAPS\":\n                if (analysis.volatilityRank > 50) {\n                    return {\n                        timing: \"WAIT_FOR_DIP\",\n                        reason: \"High volatility - wait for better prices\",\n                        targetPrice: currentPrice * 0.95\n                    };\n                }\n                return {\n                    timing: \"IMMEDIATE\",\n                    reason: \"Good volatility level for buying long-term options\"\n                };\n            default:\n                return {\n                    timing: \"IMMEDIATE\",\n                    reason: \"Market conditions are suitable\"\n                };\n        }\n    }\n    // Private helper methods\n    static determineTrend(data) {\n        if (data.length < 20) return \"NEUTRAL\";\n        const recent = data.slice(-10);\n        const older = data.slice(-20, -10);\n        const recentAvg = recent.reduce((sum, d)=>sum + d.price, 0) / recent.length;\n        const olderAvg = older.reduce((sum, d)=>sum + d.price, 0) / older.length;\n        const change = (recentAvg - olderAvg) / olderAvg;\n        if (change > 0.05) return \"BULLISH\";\n        if (change < -0.05) return \"BEARISH\";\n        return \"NEUTRAL\";\n    }\n    static calculateVolatilityRank(data) {\n        if (data.length < 30) return 50;\n        // Calculate daily returns\n        const returns = [];\n        for(let i = 1; i < data.length; i++){\n            returns.push((data[i].price - data[i - 1].price) / data[i - 1].price);\n        }\n        // Calculate current volatility (last 20 days)\n        const recentReturns = returns.slice(-20);\n        const recentVol = this.calculateStandardDeviation(recentReturns) * Math.sqrt(252);\n        // Calculate historical volatilities\n        const historicalVols = [];\n        for(let i = 20; i < returns.length; i++){\n            const periodReturns = returns.slice(i - 20, i);\n            historicalVols.push(this.calculateStandardDeviation(periodReturns) * Math.sqrt(252));\n        }\n        // Calculate rank\n        const lowerCount = historicalVols.filter((vol)=>vol < recentVol).length;\n        return lowerCount / historicalVols.length * 100;\n    }\n    static calculateStandardDeviation(values) {\n        const mean = values.reduce((sum, val)=>sum + val, 0) / values.length;\n        const squaredDiffs = values.map((val)=>Math.pow(val - mean, 2));\n        const avgSquaredDiff = squaredDiffs.reduce((sum, val)=>sum + val, 0) / values.length;\n        return Math.sqrt(avgSquaredDiff);\n    }\n    static determineMarketRegime(trend, volatilityRank, stock) {\n        if (trend === \"BULLISH\" && volatilityRank < 30) return \"STRONG_BULLISH\";\n        if (trend === \"BEARISH\" && volatilityRank > 70) return \"BEARISH_HIGH_IV\";\n        if (volatilityRank > 80) return \"HIGH_VOLATILITY\";\n        if (volatilityRank < 20) return \"LOW_VOLATILITY\";\n        if (trend === \"NEUTRAL\" && volatilityRank > 40) return \"CHOPPY_RANGE\";\n        return \"NEUTRAL_TO_BULLISH\";\n    }\n    static findSupportResistance(data) {\n        if (data.length < 50) {\n            const prices = data.map((d)=>d.price);\n            return {\n                support: Math.min(...prices) * 0.98,\n                resistance: Math.max(...prices) * 1.02\n            };\n        }\n        const prices = data.slice(-50).map((d)=>d.price);\n        const sorted = [\n            ...prices\n        ].sort((a, b)=>a - b);\n        return {\n            support: sorted[Math.floor(sorted.length * 0.1)],\n            resistance: sorted[Math.floor(sorted.length * 0.9)]\n        };\n    }\n    static getNextEarningsDate(symbol) {\n        // Simplified - in real implementation, fetch from earnings calendar API\n        const earningsCalendar = {\n            \"AAPL\": \"2024-01-25\",\n            \"MSFT\": \"2024-01-24\",\n            \"GOOGL\": \"2024-01-30\",\n            \"AMZN\": \"2024-02-01\"\n        };\n        const dateStr = earningsCalendar[symbol];\n        return dateStr ? new Date(dateStr) : undefined;\n    }\n    static getNextDividendInfo(symbol) {\n        // Simplified - in real implementation, fetch from dividend calendar API\n        const dividendCalendar = {\n            \"AAPL\": {\n                exDate: \"2024-02-09\",\n                amount: 0.24\n            },\n            \"MSFT\": {\n                exDate: \"2024-02-15\",\n                amount: 0.75\n            },\n            \"JPM\": {\n                exDate: \"2024-01-05\",\n                amount: 1.05\n            }\n        };\n        const info = dividendCalendar[symbol];\n        return info ? {\n            exDate: new Date(info.exDate),\n            amount: info.amount\n        } : undefined;\n    }\n    static daysBetween(date1, date2) {\n        const diffTime = Math.abs(date2.getTime() - date1.getTime());\n        return Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvbWFya2V0LWFuYWx5c2lzLnRzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxtQ0FBbUM7QUFDbkMsZ0RBQWdEO0FBSXpDLE1BQU1BO0lBRVg7OztHQUdDLEdBQ0QsT0FBT0MsY0FDTEMsS0FBWSxFQUNaQyxjQUErRCxFQUMvREMsV0FBeUUsRUFDekQ7UUFFaEIsTUFBTUMsUUFBUSxJQUFJLENBQUNDLGNBQWMsQ0FBQ0g7UUFDbEMsTUFBTUksaUJBQWlCLElBQUksQ0FBQ0MsdUJBQXVCLENBQUNMO1FBQ3BELE1BQU1NLFNBQVMsSUFBSSxDQUFDQyxxQkFBcUIsQ0FBQ0wsT0FBT0UsZ0JBQWdCTDtRQUNqRSxNQUFNUyxvQkFBb0IsSUFBSSxDQUFDQyxxQkFBcUIsQ0FBQ1Q7UUFFckQsT0FBTztZQUNMTTtZQUNBRjtZQUNBRjtZQUNBUSxTQUFTRixrQkFBa0JFLE9BQU87WUFDbENDLFlBQVlILGtCQUFrQkcsVUFBVTtZQUN4Q0MsbUJBQW1CWCxhQUFhVyxxQkFBcUI7WUFDckRDLHNCQUFzQlosYUFBYVksd0JBQXdCO1lBQzNEQyxVQUFVLElBQUksQ0FBQ0MsbUJBQW1CLENBQUNoQixNQUFNaUIsTUFBTTtZQUMvQ0MsVUFBVSxJQUFJLENBQUNDLG1CQUFtQixDQUFDbkIsTUFBTWlCLE1BQU07UUFDakQ7SUFDRjtJQUVBOzs7R0FHQyxHQUNELE9BQU9HLG9CQUFvQkMsUUFBd0IsRUFBRXJCLEtBQVksRUFBcUI7UUFDcEYsTUFBTXNCLGFBQWdDLEVBQUU7UUFFeEMsT0FBUUQsU0FBU2QsTUFBTTtZQUNyQixLQUFLO2dCQUNIZSxXQUFXQyxJQUFJLENBQUMsMEJBQTBCLG1CQUFtQjtnQkFDN0QsSUFBSUYsU0FBU2hCLGNBQWMsR0FBRyxJQUFJO29CQUNoQ2lCLFdBQVdDLElBQUksQ0FBQztnQkFDbEI7Z0JBQ0E7WUFFRixLQUFLO2dCQUNIRCxXQUFXQyxJQUFJLENBQUMsb0JBQW9CO2dCQUNwQyxJQUFJRixTQUFTaEIsY0FBYyxHQUFHLElBQUk7b0JBQ2hDaUIsV0FBV0MsSUFBSSxDQUFDO2dCQUNsQjtnQkFDQTtZQUVGLEtBQUs7Z0JBQ0hELFdBQVdDLElBQUksQ0FBQyxTQUFTO2dCQUN6QixJQUFJdkIsTUFBTXdCLEtBQUssR0FBR0gsU0FBU1YsT0FBTyxHQUFHLE1BQU07b0JBQ3pDVyxXQUFXQyxJQUFJLENBQUM7Z0JBQ2xCO2dCQUNBO1lBRUYsS0FBSztnQkFDSEQsV0FBV0MsSUFBSSxDQUFDLGFBQWEsZUFBZTtnQkFDNUM7WUFFRixLQUFLO2dCQUNIRCxXQUFXQyxJQUFJLENBQUMsZUFBZSxvQkFBb0I7Z0JBQ25EO1lBRUYsS0FBSztnQkFDSEQsV0FBV0MsSUFBSSxDQUFDLG1CQUFtQixtQkFBbUI7Z0JBQ3REO1FBQ0o7UUFFQSxPQUFPRDtJQUNUO0lBRUE7OztHQUdDLEdBQ0QsT0FBT0csc0JBQ0xDLE1BQWUsRUFDZkMsUUFBOEMsRUFPN0M7UUFFRCxNQUFNQyxTQU1ELEVBQUU7UUFFUEYsT0FBT0csT0FBTyxDQUFDN0IsQ0FBQUE7WUFDYixNQUFNcUIsV0FBV00sUUFBUSxDQUFDM0IsTUFBTWlCLE1BQU0sQ0FBQztZQUN2QyxJQUFJLENBQUNJLFVBQVU7WUFFZixnQ0FBZ0M7WUFDaEMsSUFBSUEsU0FBU2hCLGNBQWMsR0FBRyxJQUFJO2dCQUNoQ3VCLE9BQU9MLElBQUksQ0FBQztvQkFDVk4sUUFBUWpCLE1BQU1pQixNQUFNO29CQUNwQmEsTUFBTTtvQkFDTkMsU0FBUyxDQUFDLEVBQUUvQixNQUFNaUIsTUFBTSxDQUFDLHFEQUFxRCxDQUFDO29CQUMvRWUsVUFBVTtvQkFDVlYsWUFBWTt3QkFBQzt3QkFBZTt3QkFBb0I7cUJBQW1CO2dCQUNyRTtZQUNGO1lBRUEsK0JBQStCO1lBQy9CLElBQUlELFNBQVNoQixjQUFjLEdBQUcsTUFBTWdCLFNBQVNsQixLQUFLLEtBQUssV0FBVztnQkFDaEV5QixPQUFPTCxJQUFJLENBQUM7b0JBQ1ZOLFFBQVFqQixNQUFNaUIsTUFBTTtvQkFDcEJhLE1BQU07b0JBQ05DLFNBQVMsQ0FBQyxFQUFFL0IsTUFBTWlCLE1BQU0sQ0FBQyx3REFBd0QsQ0FBQztvQkFDbEZlLFVBQVU7b0JBQ1ZWLFlBQVk7d0JBQUM7d0JBQVM7cUJBQWtCO2dCQUMxQztZQUNGO1lBRUEsNEJBQTRCO1lBQzVCLE1BQU1XLHNCQUFzQixDQUFDakMsTUFBTXdCLEtBQUssR0FBR0gsU0FBU1YsT0FBTyxJQUFJWCxNQUFNd0IsS0FBSztZQUMxRSxNQUFNVSx5QkFBeUIsQ0FBQ2IsU0FBU1QsVUFBVSxHQUFHWixNQUFNd0IsS0FBSyxJQUFJeEIsTUFBTXdCLEtBQUs7WUFFaEYsSUFBSVMsc0JBQXNCLE1BQU07Z0JBQzlCTCxPQUFPTCxJQUFJLENBQUM7b0JBQ1ZOLFFBQVFqQixNQUFNaUIsTUFBTTtvQkFDcEJhLE1BQU07b0JBQ05DLFNBQVMsQ0FBQyxFQUFFL0IsTUFBTWlCLE1BQU0sQ0FBQyxxQkFBcUIsRUFBRUksU0FBU1YsT0FBTyxDQUFDd0IsT0FBTyxDQUFDLEdBQUcsbUJBQW1CLENBQUM7b0JBQ2hHSCxVQUFVO29CQUNWVixZQUFZO3dCQUFDO3dCQUFvQjtxQkFBUTtnQkFDM0M7WUFDRjtZQUVBLElBQUlZLHlCQUF5QixNQUFNO2dCQUNqQ04sT0FBT0wsSUFBSSxDQUFDO29CQUNWTixRQUFRakIsTUFBTWlCLE1BQU07b0JBQ3BCYSxNQUFNO29CQUNOQyxTQUFTLENBQUMsRUFBRS9CLE1BQU1pQixNQUFNLENBQUMsd0JBQXdCLEVBQUVJLFNBQVNULFVBQVUsQ0FBQ3VCLE9BQU8sQ0FBQyxHQUFHLHVCQUF1QixDQUFDO29CQUMxR0gsVUFBVTtvQkFDVlYsWUFBWTt3QkFBQzt3QkFBb0I7cUJBQWU7Z0JBQ2xEO1lBQ0Y7WUFFQSxvQkFBb0I7WUFDcEIsSUFBSUQsU0FBU04sUUFBUSxJQUFJLElBQUksQ0FBQ3FCLFdBQVcsQ0FBQyxJQUFJQyxRQUFRaEIsU0FBU04sUUFBUSxLQUFLLEdBQUc7Z0JBQzdFYSxPQUFPTCxJQUFJLENBQUM7b0JBQ1ZOLFFBQVFqQixNQUFNaUIsTUFBTTtvQkFDcEJhLE1BQU07b0JBQ05DLFNBQVMsQ0FBQyxFQUFFL0IsTUFBTWlCLE1BQU0sQ0FBQyxxQkFBcUIsRUFBRSxJQUFJLENBQUNtQixXQUFXLENBQUMsSUFBSUMsUUFBUWhCLFNBQVNOLFFBQVEsRUFBRSx5QkFBeUIsQ0FBQztvQkFDMUhpQixVQUFVO29CQUNWVixZQUFZLEVBQUU7Z0JBQ2hCO1lBQ0Y7WUFFQSxrQkFBa0I7WUFDbEIsSUFBSUQsU0FBU0gsUUFBUSxJQUFJLElBQUksQ0FBQ2tCLFdBQVcsQ0FBQyxJQUFJQyxRQUFRaEIsU0FBU0gsUUFBUSxDQUFDb0IsTUFBTSxLQUFLLEdBQUc7Z0JBQ3BGVixPQUFPTCxJQUFJLENBQUM7b0JBQ1ZOLFFBQVFqQixNQUFNaUIsTUFBTTtvQkFDcEJhLE1BQU07b0JBQ05DLFNBQVMsQ0FBQyxFQUFFL0IsTUFBTWlCLE1BQU0sQ0FBQyxxQkFBcUIsRUFBRSxJQUFJLENBQUNtQixXQUFXLENBQUMsSUFBSUMsUUFBUWhCLFNBQVNILFFBQVEsQ0FBQ29CLE1BQU0sRUFBRSxRQUFRLEVBQUVqQixTQUFTSCxRQUFRLENBQUNxQixNQUFNLENBQUMsQ0FBQyxDQUFDO29CQUM1SVAsVUFBVTtvQkFDVlYsWUFBWTt3QkFBQztxQkFBZTtnQkFDOUI7WUFDRjtRQUNGO1FBRUEsT0FBT00sT0FBT1ksSUFBSSxDQUFDLENBQUNDLEdBQUdDO1lBQ3JCLE1BQU1DLGdCQUFnQjtnQkFBRSxRQUFRO2dCQUFHLFVBQVU7Z0JBQUcsT0FBTztZQUFFO1lBQ3pELE9BQU9BLGFBQWEsQ0FBQ0QsRUFBRVYsUUFBUSxDQUFDLEdBQUdXLGFBQWEsQ0FBQ0YsRUFBRVQsUUFBUSxDQUFDO1FBQzlEO0lBQ0Y7SUFFQTs7O0dBR0MsR0FDRCxPQUFPWSxzQkFDTDVDLEtBQVksRUFDWnFCLFFBQXdCLEVBQ3hCd0IsUUFBeUIsRUFLekI7UUFFQSxNQUFNQyxlQUFlOUMsTUFBTXdCLEtBQUs7UUFDaEMsTUFBTWIsVUFBVVUsU0FBU1YsT0FBTztRQUNoQyxNQUFNQyxhQUFhUyxTQUFTVCxVQUFVO1FBRXRDLGlDQUFpQztRQUNqQyxPQUFRaUM7WUFDTixLQUFLO2dCQUNILElBQUlDLGVBQWVuQyxVQUFVLEtBQUs7b0JBQ2hDLE9BQU87d0JBQ0xvQyxRQUFRO3dCQUNSQyxRQUFRO3dCQUNSQyxhQUFhdEMsVUFBVTtvQkFDekI7Z0JBQ0Y7Z0JBQ0EsT0FBTztvQkFDTG9DLFFBQVE7b0JBQ1JDLFFBQVE7Z0JBQ1Y7WUFFRixLQUFLO2dCQUNILElBQUlGLGVBQWVsQyxhQUFhLEtBQUs7b0JBQ25DLE9BQU87d0JBQ0xtQyxRQUFRO3dCQUNSQyxRQUFRO3dCQUNSQyxhQUFhckMsYUFBYTtvQkFDNUI7Z0JBQ0Y7Z0JBQ0EsT0FBTztvQkFDTG1DLFFBQVE7b0JBQ1JDLFFBQVE7Z0JBQ1Y7WUFFRixLQUFLO2dCQUNILElBQUkzQixTQUFTaEIsY0FBYyxHQUFHLElBQUk7b0JBQ2hDLE9BQU87d0JBQ0wwQyxRQUFRO3dCQUNSQyxRQUFRO3dCQUNSQyxhQUFhSCxlQUFlO29CQUM5QjtnQkFDRjtnQkFDQSxPQUFPO29CQUNMQyxRQUFRO29CQUNSQyxRQUFRO2dCQUNWO1lBRUY7Z0JBQ0UsT0FBTztvQkFDTEQsUUFBUTtvQkFDUkMsUUFBUTtnQkFDVjtRQUNKO0lBQ0Y7SUFFQSx5QkFBeUI7SUFDekIsT0FBZTVDLGVBQWU4QyxJQUFxQyxFQUFxQztRQUN0RyxJQUFJQSxLQUFLQyxNQUFNLEdBQUcsSUFBSSxPQUFPO1FBRTdCLE1BQU1DLFNBQVNGLEtBQUtHLEtBQUssQ0FBQyxDQUFDO1FBQzNCLE1BQU1DLFFBQVFKLEtBQUtHLEtBQUssQ0FBQyxDQUFDLElBQUksQ0FBQztRQUUvQixNQUFNRSxZQUFZSCxPQUFPSSxNQUFNLENBQUMsQ0FBQ0MsS0FBS0MsSUFBTUQsTUFBTUMsRUFBRWxDLEtBQUssRUFBRSxLQUFLNEIsT0FBT0QsTUFBTTtRQUM3RSxNQUFNUSxXQUFXTCxNQUFNRSxNQUFNLENBQUMsQ0FBQ0MsS0FBS0MsSUFBTUQsTUFBTUMsRUFBRWxDLEtBQUssRUFBRSxLQUFLOEIsTUFBTUgsTUFBTTtRQUUxRSxNQUFNUyxTQUFTLENBQUNMLFlBQVlJLFFBQU8sSUFBS0E7UUFFeEMsSUFBSUMsU0FBUyxNQUFNLE9BQU87UUFDMUIsSUFBSUEsU0FBUyxDQUFDLE1BQU0sT0FBTztRQUMzQixPQUFPO0lBQ1Q7SUFFQSxPQUFldEQsd0JBQXdCNEMsSUFBcUMsRUFBVTtRQUNwRixJQUFJQSxLQUFLQyxNQUFNLEdBQUcsSUFBSSxPQUFPO1FBRTdCLDBCQUEwQjtRQUMxQixNQUFNVSxVQUFVLEVBQUU7UUFDbEIsSUFBSyxJQUFJQyxJQUFJLEdBQUdBLElBQUlaLEtBQUtDLE1BQU0sRUFBRVcsSUFBSztZQUNwQ0QsUUFBUXRDLElBQUksQ0FBQyxDQUFDMkIsSUFBSSxDQUFDWSxFQUFFLENBQUN0QyxLQUFLLEdBQUcwQixJQUFJLENBQUNZLElBQUUsRUFBRSxDQUFDdEMsS0FBSyxJQUFJMEIsSUFBSSxDQUFDWSxJQUFFLEVBQUUsQ0FBQ3RDLEtBQUs7UUFDbEU7UUFFQSw4Q0FBOEM7UUFDOUMsTUFBTXVDLGdCQUFnQkYsUUFBUVIsS0FBSyxDQUFDLENBQUM7UUFDckMsTUFBTVcsWUFBWSxJQUFJLENBQUNDLDBCQUEwQixDQUFDRixpQkFBaUJHLEtBQUtDLElBQUksQ0FBQztRQUU3RSxvQ0FBb0M7UUFDcEMsTUFBTUMsaUJBQWlCLEVBQUU7UUFDekIsSUFBSyxJQUFJTixJQUFJLElBQUlBLElBQUlELFFBQVFWLE1BQU0sRUFBRVcsSUFBSztZQUN4QyxNQUFNTyxnQkFBZ0JSLFFBQVFSLEtBQUssQ0FBQ1MsSUFBRSxJQUFJQTtZQUMxQ00sZUFBZTdDLElBQUksQ0FBQyxJQUFJLENBQUMwQywwQkFBMEIsQ0FBQ0ksaUJBQWlCSCxLQUFLQyxJQUFJLENBQUM7UUFDakY7UUFFQSxpQkFBaUI7UUFDakIsTUFBTUcsYUFBYUYsZUFBZUcsTUFBTSxDQUFDQyxDQUFBQSxNQUFPQSxNQUFNUixXQUFXYixNQUFNO1FBQ3ZFLE9BQU8sYUFBY2lCLGVBQWVqQixNQUFNLEdBQUk7SUFDaEQ7SUFFQSxPQUFlYywyQkFBMkJRLE1BQWdCLEVBQVU7UUFDbEUsTUFBTUMsT0FBT0QsT0FBT2pCLE1BQU0sQ0FBQyxDQUFDQyxLQUFLa0IsTUFBUWxCLE1BQU1rQixLQUFLLEtBQUtGLE9BQU90QixNQUFNO1FBQ3RFLE1BQU15QixlQUFlSCxPQUFPSSxHQUFHLENBQUNGLENBQUFBLE1BQU9ULEtBQUtZLEdBQUcsQ0FBQ0gsTUFBTUQsTUFBTTtRQUM1RCxNQUFNSyxpQkFBaUJILGFBQWFwQixNQUFNLENBQUMsQ0FBQ0MsS0FBS2tCLE1BQVFsQixNQUFNa0IsS0FBSyxLQUFLRixPQUFPdEIsTUFBTTtRQUN0RixPQUFPZSxLQUFLQyxJQUFJLENBQUNZO0lBQ25CO0lBRUEsT0FBZXZFLHNCQUNiTCxLQUF3QyxFQUN4Q0UsY0FBc0IsRUFDdEJMLEtBQVksRUFDRTtRQUVkLElBQUlHLFVBQVUsYUFBYUUsaUJBQWlCLElBQUksT0FBTztRQUN2RCxJQUFJRixVQUFVLGFBQWFFLGlCQUFpQixJQUFJLE9BQU87UUFDdkQsSUFBSUEsaUJBQWlCLElBQUksT0FBTztRQUNoQyxJQUFJQSxpQkFBaUIsSUFBSSxPQUFPO1FBQ2hDLElBQUlGLFVBQVUsYUFBYUUsaUJBQWlCLElBQUksT0FBTztRQUV2RCxPQUFPO0lBQ1Q7SUFFQSxPQUFlSyxzQkFBc0J3QyxJQUFxQyxFQUEyQztRQUNuSCxJQUFJQSxLQUFLQyxNQUFNLEdBQUcsSUFBSTtZQUNwQixNQUFNNkIsU0FBUzlCLEtBQUsyQixHQUFHLENBQUNuQixDQUFBQSxJQUFLQSxFQUFFbEMsS0FBSztZQUNwQyxPQUFPO2dCQUNMYixTQUFTdUQsS0FBS2UsR0FBRyxJQUFJRCxVQUFVO2dCQUMvQnBFLFlBQVlzRCxLQUFLZ0IsR0FBRyxJQUFJRixVQUFVO1lBQ3BDO1FBQ0Y7UUFFQSxNQUFNQSxTQUFTOUIsS0FBS0csS0FBSyxDQUFDLENBQUMsSUFBSXdCLEdBQUcsQ0FBQ25CLENBQUFBLElBQUtBLEVBQUVsQyxLQUFLO1FBQy9DLE1BQU0yRCxTQUFTO2VBQUlIO1NBQU8sQ0FBQ3hDLElBQUksQ0FBQyxDQUFDQyxHQUFHQyxJQUFNRCxJQUFJQztRQUU5QyxPQUFPO1lBQ0wvQixTQUFTd0UsTUFBTSxDQUFDakIsS0FBS2tCLEtBQUssQ0FBQ0QsT0FBT2hDLE1BQU0sR0FBRyxLQUFLO1lBQ2hEdkMsWUFBWXVFLE1BQU0sQ0FBQ2pCLEtBQUtrQixLQUFLLENBQUNELE9BQU9oQyxNQUFNLEdBQUcsS0FBSztRQUNyRDtJQUNGO0lBRUEsT0FBZW5DLG9CQUFvQkMsTUFBYyxFQUFvQjtRQUNuRSx3RUFBd0U7UUFDeEUsTUFBTW9FLG1CQUE4QztZQUNsRCxRQUFRO1lBQ1IsUUFBUTtZQUNSLFNBQVM7WUFDVCxRQUFRO1FBQ1Y7UUFFQSxNQUFNQyxVQUFVRCxnQkFBZ0IsQ0FBQ3BFLE9BQU87UUFDeEMsT0FBT3FFLFVBQVUsSUFBSWpELEtBQUtpRCxXQUFXQztJQUN2QztJQUVBLE9BQWVwRSxvQkFBb0JGLE1BQWMsRUFBZ0Q7UUFDL0Ysd0VBQXdFO1FBQ3hFLE1BQU11RSxtQkFBMEU7WUFDOUUsUUFBUTtnQkFBRWxELFFBQVE7Z0JBQWNDLFFBQVE7WUFBSztZQUM3QyxRQUFRO2dCQUFFRCxRQUFRO2dCQUFjQyxRQUFRO1lBQUs7WUFDN0MsT0FBTztnQkFBRUQsUUFBUTtnQkFBY0MsUUFBUTtZQUFLO1FBQzlDO1FBRUEsTUFBTWtELE9BQU9ELGdCQUFnQixDQUFDdkUsT0FBTztRQUNyQyxPQUFPd0UsT0FBTztZQUFFbkQsUUFBUSxJQUFJRCxLQUFLb0QsS0FBS25ELE1BQU07WUFBR0MsUUFBUWtELEtBQUtsRCxNQUFNO1FBQUMsSUFBSWdEO0lBQ3pFO0lBRUEsT0FBZW5ELFlBQVlzRCxLQUFXLEVBQUVDLEtBQVcsRUFBVTtRQUMzRCxNQUFNQyxXQUFXMUIsS0FBSzJCLEdBQUcsQ0FBQ0YsTUFBTUcsT0FBTyxLQUFLSixNQUFNSSxPQUFPO1FBQ3pELE9BQU81QixLQUFLNkIsSUFBSSxDQUFDSCxXQUFZLFFBQU8sS0FBSyxLQUFLLEVBQUM7SUFDakQ7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL2VuaGFuY2VkLW9wdGlvbnMtdHJhZGluZy1wcm9ncmFtLy4vbGliL21hcmtldC1hbmFseXNpcy50cz81OTI1Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIE1hcmtldCBBbmFseXNpcyBhbmQgQWxlcnQgU3lzdGVtXG4vLyBTbWFydCBtYXJrZXQgZGV0ZWN0aW9uIHdpdGhvdXQgY29tcGxleCBqYXJnb25cblxuaW1wb3J0IHsgU3RvY2ssIE1hcmtldEFuYWx5c2lzLCBNYXJrZXRSZWdpbWUsIFN0cmF0ZWd5UmVjb21tZW5kYXRpb24sIFRyYWRpbmdTdHJhdGVneSB9IGZyb20gJy4vdHlwZXMnO1xuXG5leHBvcnQgY2xhc3MgTWFya2V0QW5hbHl6ZXIge1xuXG4gIC8qKlxuICAgKiBBbmFseXplIGN1cnJlbnQgbWFya2V0IGNvbmRpdGlvbnMgZm9yIGEgc3RvY2tcbiAgICogU2ltcGxlIGV4cGxhbmF0aW9uOiBMaWtlIGEgd2VhdGhlciBmb3JlY2FzdCBmb3IgeW91ciBzdG9ja1xuICAgKi9cbiAgc3RhdGljIGFuYWx5emVNYXJrZXQoXG4gICAgc3RvY2s6IFN0b2NrLFxuICAgIGhpc3RvcmljYWxEYXRhOiB7IHByaWNlOiBudW1iZXI7IHZvbHVtZTogbnVtYmVyOyBkYXRlOiBEYXRlIH1bXSxcbiAgICBvcHRpb25zRGF0YT86IHsgaW1wbGllZFZvbGF0aWxpdHk6IG51bWJlcjsgaGlzdG9yaWNhbFZvbGF0aWxpdHk6IG51bWJlciB9XG4gICk6IE1hcmtldEFuYWx5c2lzIHtcbiAgICBcbiAgICBjb25zdCB0cmVuZCA9IHRoaXMuZGV0ZXJtaW5lVHJlbmQoaGlzdG9yaWNhbERhdGEpO1xuICAgIGNvbnN0IHZvbGF0aWxpdHlSYW5rID0gdGhpcy5jYWxjdWxhdGVWb2xhdGlsaXR5UmFuayhoaXN0b3JpY2FsRGF0YSk7XG4gICAgY29uc3QgcmVnaW1lID0gdGhpcy5kZXRlcm1pbmVNYXJrZXRSZWdpbWUodHJlbmQsIHZvbGF0aWxpdHlSYW5rLCBzdG9jayk7XG4gICAgY29uc3Qgc3VwcG9ydFJlc2lzdGFuY2UgPSB0aGlzLmZpbmRTdXBwb3J0UmVzaXN0YW5jZShoaXN0b3JpY2FsRGF0YSk7XG4gICAgXG4gICAgcmV0dXJuIHtcbiAgICAgIHJlZ2ltZSxcbiAgICAgIHZvbGF0aWxpdHlSYW5rLFxuICAgICAgdHJlbmQsXG4gICAgICBzdXBwb3J0OiBzdXBwb3J0UmVzaXN0YW5jZS5zdXBwb3J0LFxuICAgICAgcmVzaXN0YW5jZTogc3VwcG9ydFJlc2lzdGFuY2UucmVzaXN0YW5jZSxcbiAgICAgIGltcGxpZWRWb2xhdGlsaXR5OiBvcHRpb25zRGF0YT8uaW1wbGllZFZvbGF0aWxpdHkgfHwgMjUsXG4gICAgICBoaXN0b3JpY2FsVm9sYXRpbGl0eTogb3B0aW9uc0RhdGE/Lmhpc3RvcmljYWxWb2xhdGlsaXR5IHx8IDIwLFxuICAgICAgZWFybmluZ3M6IHRoaXMuZ2V0TmV4dEVhcm5pbmdzRGF0ZShzdG9jay5zeW1ib2wpLFxuICAgICAgZGl2aWRlbmQ6IHRoaXMuZ2V0TmV4dERpdmlkZW5kSW5mbyhzdG9jay5zeW1ib2wpXG4gICAgfTtcbiAgfVxuXG4gIC8qKlxuICAgKiBEZXRlcm1pbmUgdGhlIGJlc3QgdHJhZGluZyBzdHJhdGVnaWVzIGZvciBjdXJyZW50IG1hcmtldCBjb25kaXRpb25zXG4gICAqIFNpbXBsZSBleHBsYW5hdGlvbjogTGlrZSBhIEdQUyB0aGF0IHRlbGxzIHlvdSB0aGUgYmVzdCByb3V0ZSBiYXNlZCBvbiB0cmFmZmljXG4gICAqL1xuICBzdGF0aWMgcmVjb21tZW5kU3RyYXRlZ2llcyhhbmFseXNpczogTWFya2V0QW5hbHlzaXMsIHN0b2NrOiBTdG9jayk6IFRyYWRpbmdTdHJhdGVneVtdIHtcbiAgICBjb25zdCBzdHJhdGVnaWVzOiBUcmFkaW5nU3RyYXRlZ3lbXSA9IFtdO1xuXG4gICAgc3dpdGNoIChhbmFseXNpcy5yZWdpbWUpIHtcbiAgICAgIGNhc2UgJ05FVVRSQUxfVE9fQlVMTElTSCc6XG4gICAgICAgIHN0cmF0ZWdpZXMucHVzaCgnUE9PUl9NQU5TX0NPVkVSRURfQ0FMTCcsICdDQUxFTkRBUl9TUFJFQUQnLCAnQ09WRVJFRF9DQUxMJyk7XG4gICAgICAgIGlmIChhbmFseXNpcy52b2xhdGlsaXR5UmFuayA8IDMwKSB7XG4gICAgICAgICAgc3RyYXRlZ2llcy5wdXNoKCdMRUFQUycpO1xuICAgICAgICB9XG4gICAgICAgIGJyZWFrO1xuXG4gICAgICBjYXNlICdCRUFSSVNIX0hJR0hfSVYnOlxuICAgICAgICBzdHJhdGVnaWVzLnB1c2goJ0JFQVJfQ0FMTF9TUFJFQUQnLCAnQ0FTSF9TRUNVUkVEX1BVVCcpO1xuICAgICAgICBpZiAoYW5hbHlzaXMudm9sYXRpbGl0eVJhbmsgPiA3MCkge1xuICAgICAgICAgIHN0cmF0ZWdpZXMucHVzaCgnSVJPTl9DT05ET1InKTtcbiAgICAgICAgfVxuICAgICAgICBicmVhaztcblxuICAgICAgY2FzZSAnU1RST05HX0JVTExJU0gnOlxuICAgICAgICBzdHJhdGVnaWVzLnB1c2goJ0xFQVBTJywgJ0NBU0hfU0VDVVJFRF9QVVQnKTtcbiAgICAgICAgaWYgKHN0b2NrLnByaWNlID4gYW5hbHlzaXMuc3VwcG9ydCAqIDEuMDUpIHtcbiAgICAgICAgICBzdHJhdGVnaWVzLnB1c2goJ0NPVkVSRURfQ0FMTCcpO1xuICAgICAgICB9XG4gICAgICAgIGJyZWFrO1xuXG4gICAgICBjYXNlICdDSE9QUFlfUkFOR0UnOlxuICAgICAgICBzdHJhdGVnaWVzLnB1c2goJ1RIRV9XSEVFTCcsICdJUk9OX0NPTkRPUicsICdCVVRURVJGTFlfU1BSRUFEJyk7XG4gICAgICAgIGJyZWFrO1xuXG4gICAgICBjYXNlICdISUdIX1ZPTEFUSUxJVFknOlxuICAgICAgICBzdHJhdGVnaWVzLnB1c2goJ0lST05fQ09ORE9SJywgJ0JFQVJfQ0FMTF9TUFJFQUQnLCAnQlVUVEVSRkxZX1NQUkVBRCcpO1xuICAgICAgICBicmVhaztcblxuICAgICAgY2FzZSAnTE9XX1ZPTEFUSUxJVFknOlxuICAgICAgICBzdHJhdGVnaWVzLnB1c2goJ0NBTEVOREFSX1NQUkVBRCcsICdESUFHT05BTF9TUFJFQUQnLCAnTEVBUFMnKTtcbiAgICAgICAgYnJlYWs7XG4gICAgfVxuXG4gICAgcmV0dXJuIHN0cmF0ZWdpZXM7XG4gIH1cblxuICAvKipcbiAgICogR2VuZXJhdGUgdHJhZGluZyBhbGVydHMgYmFzZWQgb24gbWFya2V0IGNvbmRpdGlvbnNcbiAgICogU2ltcGxlIGV4cGxhbmF0aW9uOiBMaWtlIG5vdGlmaWNhdGlvbnMgdGhhdCB0ZWxsIHlvdSB3aGVuIGdvb2Qgb3Bwb3J0dW5pdGllcyBhcHBlYXJcbiAgICovXG4gIHN0YXRpYyBnZW5lcmF0ZVRyYWRpbmdBbGVydHMoXG4gICAgc3RvY2tzOiBTdG9ja1tdLFxuICAgIGFuYWx5c2VzOiB7IFtzeW1ib2w6IHN0cmluZ106IE1hcmtldEFuYWx5c2lzIH1cbiAgKTogQXJyYXk8e1xuICAgIHN5bWJvbDogc3RyaW5nO1xuICAgIHR5cGU6ICdPUFBPUlRVTklUWScgfCAnV0FSTklORycgfCAnSU5GTyc7XG4gICAgbWVzc2FnZTogc3RyaW5nO1xuICAgIHByaW9yaXR5OiAnSElHSCcgfCAnTUVESVVNJyB8ICdMT1cnO1xuICAgIHN0cmF0ZWdpZXM6IFRyYWRpbmdTdHJhdGVneVtdO1xuICB9PiB7XG4gICAgXG4gICAgY29uc3QgYWxlcnRzOiBBcnJheTx7XG4gICAgICBzeW1ib2w6IHN0cmluZztcbiAgICAgIHR5cGU6ICdPUFBPUlRVTklUWScgfCAnV0FSTklORycgfCAnSU5GTyc7XG4gICAgICBtZXNzYWdlOiBzdHJpbmc7XG4gICAgICBwcmlvcml0eTogJ0hJR0gnIHwgJ01FRElVTScgfCAnTE9XJztcbiAgICAgIHN0cmF0ZWdpZXM6IFRyYWRpbmdTdHJhdGVneVtdO1xuICAgIH0+ID0gW107XG5cbiAgICBzdG9ja3MuZm9yRWFjaChzdG9jayA9PiB7XG4gICAgICBjb25zdCBhbmFseXNpcyA9IGFuYWx5c2VzW3N0b2NrLnN5bWJvbF07XG4gICAgICBpZiAoIWFuYWx5c2lzKSByZXR1cm47XG5cbiAgICAgIC8vIEhpZ2ggdm9sYXRpbGl0eSBvcHBvcnR1bml0aWVzXG4gICAgICBpZiAoYW5hbHlzaXMudm9sYXRpbGl0eVJhbmsgPiA4MCkge1xuICAgICAgICBhbGVydHMucHVzaCh7XG4gICAgICAgICAgc3ltYm9sOiBzdG9jay5zeW1ib2wsXG4gICAgICAgICAgdHlwZTogJ09QUE9SVFVOSVRZJyxcbiAgICAgICAgICBtZXNzYWdlOiBgJHtzdG9jay5zeW1ib2x9IGhhcyB2ZXJ5IGhpZ2ggdm9sYXRpbGl0eSAtIGdyZWF0IGZvciBzZWxsaW5nIHByZW1pdW1gLFxuICAgICAgICAgIHByaW9yaXR5OiAnSElHSCcsXG4gICAgICAgICAgc3RyYXRlZ2llczogWydJUk9OX0NPTkRPUicsICdCRUFSX0NBTExfU1BSRUFEJywgJ0NBU0hfU0VDVVJFRF9QVVQnXVxuICAgICAgICB9KTtcbiAgICAgIH1cblxuICAgICAgLy8gTG93IHZvbGF0aWxpdHkgb3Bwb3J0dW5pdGllc1xuICAgICAgaWYgKGFuYWx5c2lzLnZvbGF0aWxpdHlSYW5rIDwgMjAgJiYgYW5hbHlzaXMudHJlbmQgPT09ICdCVUxMSVNIJykge1xuICAgICAgICBhbGVydHMucHVzaCh7XG4gICAgICAgICAgc3ltYm9sOiBzdG9jay5zeW1ib2wsXG4gICAgICAgICAgdHlwZTogJ09QUE9SVFVOSVRZJyxcbiAgICAgICAgICBtZXNzYWdlOiBgJHtzdG9jay5zeW1ib2x9IGhhcyBsb3cgdm9sYXRpbGl0eSBpbiB1cHRyZW5kIC0gZ29vZCBmb3IgYnV5aW5nIG9wdGlvbnNgLFxuICAgICAgICAgIHByaW9yaXR5OiAnTUVESVVNJyxcbiAgICAgICAgICBzdHJhdGVnaWVzOiBbJ0xFQVBTJywgJ0NBTEVOREFSX1NQUkVBRCddXG4gICAgICAgIH0pO1xuICAgICAgfVxuXG4gICAgICAvLyBTdXBwb3J0L1Jlc2lzdGFuY2UgYWxlcnRzXG4gICAgICBjb25zdCBkaXN0YW5jZUZyb21TdXBwb3J0ID0gKHN0b2NrLnByaWNlIC0gYW5hbHlzaXMuc3VwcG9ydCkgLyBzdG9jay5wcmljZTtcbiAgICAgIGNvbnN0IGRpc3RhbmNlRnJvbVJlc2lzdGFuY2UgPSAoYW5hbHlzaXMucmVzaXN0YW5jZSAtIHN0b2NrLnByaWNlKSAvIHN0b2NrLnByaWNlO1xuXG4gICAgICBpZiAoZGlzdGFuY2VGcm9tU3VwcG9ydCA8IDAuMDIpIHtcbiAgICAgICAgYWxlcnRzLnB1c2goe1xuICAgICAgICAgIHN5bWJvbDogc3RvY2suc3ltYm9sLFxuICAgICAgICAgIHR5cGU6ICdPUFBPUlRVTklUWScsXG4gICAgICAgICAgbWVzc2FnZTogYCR7c3RvY2suc3ltYm9sfSBpcyBuZWFyIHN1cHBvcnQgYXQgJCR7YW5hbHlzaXMuc3VwcG9ydC50b0ZpeGVkKDIpfSAtIHBvdGVudGlhbCBib3VuY2VgLFxuICAgICAgICAgIHByaW9yaXR5OiAnSElHSCcsXG4gICAgICAgICAgc3RyYXRlZ2llczogWydDQVNIX1NFQ1VSRURfUFVUJywgJ0xFQVBTJ11cbiAgICAgICAgfSk7XG4gICAgICB9XG5cbiAgICAgIGlmIChkaXN0YW5jZUZyb21SZXNpc3RhbmNlIDwgMC4wMikge1xuICAgICAgICBhbGVydHMucHVzaCh7XG4gICAgICAgICAgc3ltYm9sOiBzdG9jay5zeW1ib2wsXG4gICAgICAgICAgdHlwZTogJ1dBUk5JTkcnLFxuICAgICAgICAgIG1lc3NhZ2U6IGAke3N0b2NrLnN5bWJvbH0gaXMgbmVhciByZXNpc3RhbmNlIGF0ICQke2FuYWx5c2lzLnJlc2lzdGFuY2UudG9GaXhlZCgyKX0gLSBtYXkgc3RydWdnbGUgdG8gcmlzZWAsXG4gICAgICAgICAgcHJpb3JpdHk6ICdNRURJVU0nLFxuICAgICAgICAgIHN0cmF0ZWdpZXM6IFsnQkVBUl9DQUxMX1NQUkVBRCcsICdDT1ZFUkVEX0NBTEwnXVxuICAgICAgICB9KTtcbiAgICAgIH1cblxuICAgICAgLy8gRWFybmluZ3Mgd2FybmluZ3NcbiAgICAgIGlmIChhbmFseXNpcy5lYXJuaW5ncyAmJiB0aGlzLmRheXNCZXR3ZWVuKG5ldyBEYXRlKCksIGFuYWx5c2lzLmVhcm5pbmdzKSA8PSA3KSB7XG4gICAgICAgIGFsZXJ0cy5wdXNoKHtcbiAgICAgICAgICBzeW1ib2w6IHN0b2NrLnN5bWJvbCxcbiAgICAgICAgICB0eXBlOiAnV0FSTklORycsXG4gICAgICAgICAgbWVzc2FnZTogYCR7c3RvY2suc3ltYm9sfSByZXBvcnRzIGVhcm5pbmdzIGluICR7dGhpcy5kYXlzQmV0d2VlbihuZXcgRGF0ZSgpLCBhbmFseXNpcy5lYXJuaW5ncyl9IGRheXMgLSBleHBlY3Qgdm9sYXRpbGl0eWAsXG4gICAgICAgICAgcHJpb3JpdHk6ICdISUdIJyxcbiAgICAgICAgICBzdHJhdGVnaWVzOiBbXVxuICAgICAgICB9KTtcbiAgICAgIH1cblxuICAgICAgLy8gRGl2aWRlbmQgYWxlcnRzXG4gICAgICBpZiAoYW5hbHlzaXMuZGl2aWRlbmQgJiYgdGhpcy5kYXlzQmV0d2VlbihuZXcgRGF0ZSgpLCBhbmFseXNpcy5kaXZpZGVuZC5leERhdGUpIDw9IDUpIHtcbiAgICAgICAgYWxlcnRzLnB1c2goe1xuICAgICAgICAgIHN5bWJvbDogc3RvY2suc3ltYm9sLFxuICAgICAgICAgIHR5cGU6ICdJTkZPJyxcbiAgICAgICAgICBtZXNzYWdlOiBgJHtzdG9jay5zeW1ib2x9IGdvZXMgZXgtZGl2aWRlbmQgaW4gJHt0aGlzLmRheXNCZXR3ZWVuKG5ldyBEYXRlKCksIGFuYWx5c2lzLmRpdmlkZW5kLmV4RGF0ZSl9IGRheXMgKCQke2FuYWx5c2lzLmRpdmlkZW5kLmFtb3VudH0pYCxcbiAgICAgICAgICBwcmlvcml0eTogJ01FRElVTScsXG4gICAgICAgICAgc3RyYXRlZ2llczogWydDT1ZFUkVEX0NBTEwnXVxuICAgICAgICB9KTtcbiAgICAgIH1cbiAgICB9KTtcblxuICAgIHJldHVybiBhbGVydHMuc29ydCgoYSwgYikgPT4ge1xuICAgICAgY29uc3QgcHJpb3JpdHlPcmRlciA9IHsgJ0hJR0gnOiAzLCAnTUVESVVNJzogMiwgJ0xPVyc6IDEgfTtcbiAgICAgIHJldHVybiBwcmlvcml0eU9yZGVyW2IucHJpb3JpdHldIC0gcHJpb3JpdHlPcmRlclthLnByaW9yaXR5XTtcbiAgICB9KTtcbiAgfVxuXG4gIC8qKlxuICAgKiBDYWxjdWxhdGUgdGhlIG9wdGltYWwgZW50cnkgdGltaW5nIGZvciBhIHN0cmF0ZWd5XG4gICAqIFNpbXBsZSBleHBsYW5hdGlvbjogTGlrZSB0aW1pbmcgd2hlbiB0byBlbnRlciBhIGJ1c3kgcmVzdGF1cmFudCBmb3IgdGhlIGJlc3Qgc2VydmljZVxuICAgKi9cbiAgc3RhdGljIGNhbGN1bGF0ZU9wdGltYWxFbnRyeShcbiAgICBzdG9jazogU3RvY2ssXG4gICAgYW5hbHlzaXM6IE1hcmtldEFuYWx5c2lzLFxuICAgIHN0cmF0ZWd5OiBUcmFkaW5nU3RyYXRlZ3lcbiAgKToge1xuICAgIHRpbWluZzogJ0lNTUVESUFURScgfCAnV0FJVF9GT1JfRElQJyB8ICdXQUlUX0ZPUl9SSVNFJyB8ICdBVk9JRCc7XG4gICAgcmVhc29uOiBzdHJpbmc7XG4gICAgdGFyZ2V0UHJpY2U/OiBudW1iZXI7XG4gIH0ge1xuICAgIFxuICAgIGNvbnN0IGN1cnJlbnRQcmljZSA9IHN0b2NrLnByaWNlO1xuICAgIGNvbnN0IHN1cHBvcnQgPSBhbmFseXNpcy5zdXBwb3J0O1xuICAgIGNvbnN0IHJlc2lzdGFuY2UgPSBhbmFseXNpcy5yZXNpc3RhbmNlO1xuICAgIFxuICAgIC8vIFN0cmF0ZWd5LXNwZWNpZmljIHRpbWluZyBsb2dpY1xuICAgIHN3aXRjaCAoc3RyYXRlZ3kpIHtcbiAgICAgIGNhc2UgJ0NBU0hfU0VDVVJFRF9QVVQnOlxuICAgICAgICBpZiAoY3VycmVudFByaWNlID4gc3VwcG9ydCAqIDEuMSkge1xuICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICB0aW1pbmc6ICdXQUlUX0ZPUl9ESVAnLFxuICAgICAgICAgICAgcmVhc29uOiAnV2FpdCBmb3Igc3RvY2sgdG8gZ2V0IGNsb3NlciB0byBzdXBwb3J0IGZvciBiZXR0ZXIgZW50cnknLFxuICAgICAgICAgICAgdGFyZ2V0UHJpY2U6IHN1cHBvcnQgKiAxLjA1XG4gICAgICAgICAgfTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgIHRpbWluZzogJ0lNTUVESUFURScsXG4gICAgICAgICAgcmVhc29uOiAnR29vZCBwcmljZSBsZXZlbCBmb3Igc2VsbGluZyBwdXRzJ1xuICAgICAgICB9O1xuXG4gICAgICBjYXNlICdCRUFSX0NBTExfU1BSRUFEJzpcbiAgICAgICAgaWYgKGN1cnJlbnRQcmljZSA8IHJlc2lzdGFuY2UgKiAwLjkpIHtcbiAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgdGltaW5nOiAnV0FJVF9GT1JfUklTRScsXG4gICAgICAgICAgICByZWFzb246ICdXYWl0IGZvciBzdG9jayB0byBnZXQgY2xvc2VyIHRvIHJlc2lzdGFuY2UnLFxuICAgICAgICAgICAgdGFyZ2V0UHJpY2U6IHJlc2lzdGFuY2UgKiAwLjk1XG4gICAgICAgICAgfTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgIHRpbWluZzogJ0lNTUVESUFURScsXG4gICAgICAgICAgcmVhc29uOiAnR29vZCBsZXZlbCB0byBzZWxsIGNhbGxzIGFnYWluc3QgcmVzaXN0YW5jZSdcbiAgICAgICAgfTtcblxuICAgICAgY2FzZSAnTEVBUFMnOlxuICAgICAgICBpZiAoYW5hbHlzaXMudm9sYXRpbGl0eVJhbmsgPiA1MCkge1xuICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICB0aW1pbmc6ICdXQUlUX0ZPUl9ESVAnLFxuICAgICAgICAgICAgcmVhc29uOiAnSGlnaCB2b2xhdGlsaXR5IC0gd2FpdCBmb3IgYmV0dGVyIHByaWNlcycsXG4gICAgICAgICAgICB0YXJnZXRQcmljZTogY3VycmVudFByaWNlICogMC45NVxuICAgICAgICAgIH07XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICB0aW1pbmc6ICdJTU1FRElBVEUnLFxuICAgICAgICAgIHJlYXNvbjogJ0dvb2Qgdm9sYXRpbGl0eSBsZXZlbCBmb3IgYnV5aW5nIGxvbmctdGVybSBvcHRpb25zJ1xuICAgICAgICB9O1xuXG4gICAgICBkZWZhdWx0OlxuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgIHRpbWluZzogJ0lNTUVESUFURScsXG4gICAgICAgICAgcmVhc29uOiAnTWFya2V0IGNvbmRpdGlvbnMgYXJlIHN1aXRhYmxlJ1xuICAgICAgICB9O1xuICAgIH1cbiAgfVxuXG4gIC8vIFByaXZhdGUgaGVscGVyIG1ldGhvZHNcbiAgcHJpdmF0ZSBzdGF0aWMgZGV0ZXJtaW5lVHJlbmQoZGF0YTogeyBwcmljZTogbnVtYmVyOyBkYXRlOiBEYXRlIH1bXSk6ICdCVUxMSVNIJyB8ICdCRUFSSVNIJyB8ICdORVVUUkFMJyB7XG4gICAgaWYgKGRhdGEubGVuZ3RoIDwgMjApIHJldHVybiAnTkVVVFJBTCc7XG4gICAgXG4gICAgY29uc3QgcmVjZW50ID0gZGF0YS5zbGljZSgtMTApO1xuICAgIGNvbnN0IG9sZGVyID0gZGF0YS5zbGljZSgtMjAsIC0xMCk7XG4gICAgXG4gICAgY29uc3QgcmVjZW50QXZnID0gcmVjZW50LnJlZHVjZSgoc3VtLCBkKSA9PiBzdW0gKyBkLnByaWNlLCAwKSAvIHJlY2VudC5sZW5ndGg7XG4gICAgY29uc3Qgb2xkZXJBdmcgPSBvbGRlci5yZWR1Y2UoKHN1bSwgZCkgPT4gc3VtICsgZC5wcmljZSwgMCkgLyBvbGRlci5sZW5ndGg7XG4gICAgXG4gICAgY29uc3QgY2hhbmdlID0gKHJlY2VudEF2ZyAtIG9sZGVyQXZnKSAvIG9sZGVyQXZnO1xuICAgIFxuICAgIGlmIChjaGFuZ2UgPiAwLjA1KSByZXR1cm4gJ0JVTExJU0gnO1xuICAgIGlmIChjaGFuZ2UgPCAtMC4wNSkgcmV0dXJuICdCRUFSSVNIJztcbiAgICByZXR1cm4gJ05FVVRSQUwnO1xuICB9XG5cbiAgcHJpdmF0ZSBzdGF0aWMgY2FsY3VsYXRlVm9sYXRpbGl0eVJhbmsoZGF0YTogeyBwcmljZTogbnVtYmVyOyBkYXRlOiBEYXRlIH1bXSk6IG51bWJlciB7XG4gICAgaWYgKGRhdGEubGVuZ3RoIDwgMzApIHJldHVybiA1MDtcbiAgICBcbiAgICAvLyBDYWxjdWxhdGUgZGFpbHkgcmV0dXJuc1xuICAgIGNvbnN0IHJldHVybnMgPSBbXTtcbiAgICBmb3IgKGxldCBpID0gMTsgaSA8IGRhdGEubGVuZ3RoOyBpKyspIHtcbiAgICAgIHJldHVybnMucHVzaCgoZGF0YVtpXS5wcmljZSAtIGRhdGFbaS0xXS5wcmljZSkgLyBkYXRhW2ktMV0ucHJpY2UpO1xuICAgIH1cbiAgICBcbiAgICAvLyBDYWxjdWxhdGUgY3VycmVudCB2b2xhdGlsaXR5IChsYXN0IDIwIGRheXMpXG4gICAgY29uc3QgcmVjZW50UmV0dXJucyA9IHJldHVybnMuc2xpY2UoLTIwKTtcbiAgICBjb25zdCByZWNlbnRWb2wgPSB0aGlzLmNhbGN1bGF0ZVN0YW5kYXJkRGV2aWF0aW9uKHJlY2VudFJldHVybnMpICogTWF0aC5zcXJ0KDI1Mik7XG4gICAgXG4gICAgLy8gQ2FsY3VsYXRlIGhpc3RvcmljYWwgdm9sYXRpbGl0aWVzXG4gICAgY29uc3QgaGlzdG9yaWNhbFZvbHMgPSBbXTtcbiAgICBmb3IgKGxldCBpID0gMjA7IGkgPCByZXR1cm5zLmxlbmd0aDsgaSsrKSB7XG4gICAgICBjb25zdCBwZXJpb2RSZXR1cm5zID0gcmV0dXJucy5zbGljZShpLTIwLCBpKTtcbiAgICAgIGhpc3RvcmljYWxWb2xzLnB1c2godGhpcy5jYWxjdWxhdGVTdGFuZGFyZERldmlhdGlvbihwZXJpb2RSZXR1cm5zKSAqIE1hdGguc3FydCgyNTIpKTtcbiAgICB9XG4gICAgXG4gICAgLy8gQ2FsY3VsYXRlIHJhbmtcbiAgICBjb25zdCBsb3dlckNvdW50ID0gaGlzdG9yaWNhbFZvbHMuZmlsdGVyKHZvbCA9PiB2b2wgPCByZWNlbnRWb2wpLmxlbmd0aDtcbiAgICByZXR1cm4gKGxvd2VyQ291bnQgLyBoaXN0b3JpY2FsVm9scy5sZW5ndGgpICogMTAwO1xuICB9XG5cbiAgcHJpdmF0ZSBzdGF0aWMgY2FsY3VsYXRlU3RhbmRhcmREZXZpYXRpb24odmFsdWVzOiBudW1iZXJbXSk6IG51bWJlciB7XG4gICAgY29uc3QgbWVhbiA9IHZhbHVlcy5yZWR1Y2UoKHN1bSwgdmFsKSA9PiBzdW0gKyB2YWwsIDApIC8gdmFsdWVzLmxlbmd0aDtcbiAgICBjb25zdCBzcXVhcmVkRGlmZnMgPSB2YWx1ZXMubWFwKHZhbCA9PiBNYXRoLnBvdyh2YWwgLSBtZWFuLCAyKSk7XG4gICAgY29uc3QgYXZnU3F1YXJlZERpZmYgPSBzcXVhcmVkRGlmZnMucmVkdWNlKChzdW0sIHZhbCkgPT4gc3VtICsgdmFsLCAwKSAvIHZhbHVlcy5sZW5ndGg7XG4gICAgcmV0dXJuIE1hdGguc3FydChhdmdTcXVhcmVkRGlmZik7XG4gIH1cblxuICBwcml2YXRlIHN0YXRpYyBkZXRlcm1pbmVNYXJrZXRSZWdpbWUoXG4gICAgdHJlbmQ6ICdCVUxMSVNIJyB8ICdCRUFSSVNIJyB8ICdORVVUUkFMJyxcbiAgICB2b2xhdGlsaXR5UmFuazogbnVtYmVyLFxuICAgIHN0b2NrOiBTdG9ja1xuICApOiBNYXJrZXRSZWdpbWUge1xuICAgIFxuICAgIGlmICh0cmVuZCA9PT0gJ0JVTExJU0gnICYmIHZvbGF0aWxpdHlSYW5rIDwgMzApIHJldHVybiAnU1RST05HX0JVTExJU0gnO1xuICAgIGlmICh0cmVuZCA9PT0gJ0JFQVJJU0gnICYmIHZvbGF0aWxpdHlSYW5rID4gNzApIHJldHVybiAnQkVBUklTSF9ISUdIX0lWJztcbiAgICBpZiAodm9sYXRpbGl0eVJhbmsgPiA4MCkgcmV0dXJuICdISUdIX1ZPTEFUSUxJVFknO1xuICAgIGlmICh2b2xhdGlsaXR5UmFuayA8IDIwKSByZXR1cm4gJ0xPV19WT0xBVElMSVRZJztcbiAgICBpZiAodHJlbmQgPT09ICdORVVUUkFMJyAmJiB2b2xhdGlsaXR5UmFuayA+IDQwKSByZXR1cm4gJ0NIT1BQWV9SQU5HRSc7XG4gICAgXG4gICAgcmV0dXJuICdORVVUUkFMX1RPX0JVTExJU0gnO1xuICB9XG5cbiAgcHJpdmF0ZSBzdGF0aWMgZmluZFN1cHBvcnRSZXNpc3RhbmNlKGRhdGE6IHsgcHJpY2U6IG51bWJlcjsgZGF0ZTogRGF0ZSB9W10pOiB7IHN1cHBvcnQ6IG51bWJlcjsgcmVzaXN0YW5jZTogbnVtYmVyIH0ge1xuICAgIGlmIChkYXRhLmxlbmd0aCA8IDUwKSB7XG4gICAgICBjb25zdCBwcmljZXMgPSBkYXRhLm1hcChkID0+IGQucHJpY2UpO1xuICAgICAgcmV0dXJuIHtcbiAgICAgICAgc3VwcG9ydDogTWF0aC5taW4oLi4ucHJpY2VzKSAqIDAuOTgsXG4gICAgICAgIHJlc2lzdGFuY2U6IE1hdGgubWF4KC4uLnByaWNlcykgKiAxLjAyXG4gICAgICB9O1xuICAgIH1cbiAgICBcbiAgICBjb25zdCBwcmljZXMgPSBkYXRhLnNsaWNlKC01MCkubWFwKGQgPT4gZC5wcmljZSk7XG4gICAgY29uc3Qgc29ydGVkID0gWy4uLnByaWNlc10uc29ydCgoYSwgYikgPT4gYSAtIGIpO1xuICAgIFxuICAgIHJldHVybiB7XG4gICAgICBzdXBwb3J0OiBzb3J0ZWRbTWF0aC5mbG9vcihzb3J0ZWQubGVuZ3RoICogMC4xKV0sXG4gICAgICByZXNpc3RhbmNlOiBzb3J0ZWRbTWF0aC5mbG9vcihzb3J0ZWQubGVuZ3RoICogMC45KV1cbiAgICB9O1xuICB9XG5cbiAgcHJpdmF0ZSBzdGF0aWMgZ2V0TmV4dEVhcm5pbmdzRGF0ZShzeW1ib2w6IHN0cmluZyk6IERhdGUgfCB1bmRlZmluZWQge1xuICAgIC8vIFNpbXBsaWZpZWQgLSBpbiByZWFsIGltcGxlbWVudGF0aW9uLCBmZXRjaCBmcm9tIGVhcm5pbmdzIGNhbGVuZGFyIEFQSVxuICAgIGNvbnN0IGVhcm5pbmdzQ2FsZW5kYXI6IHsgW2tleTogc3RyaW5nXTogc3RyaW5nIH0gPSB7XG4gICAgICAnQUFQTCc6ICcyMDI0LTAxLTI1JyxcbiAgICAgICdNU0ZUJzogJzIwMjQtMDEtMjQnLFxuICAgICAgJ0dPT0dMJzogJzIwMjQtMDEtMzAnLFxuICAgICAgJ0FNWk4nOiAnMjAyNC0wMi0wMSdcbiAgICB9O1xuICAgIFxuICAgIGNvbnN0IGRhdGVTdHIgPSBlYXJuaW5nc0NhbGVuZGFyW3N5bWJvbF07XG4gICAgcmV0dXJuIGRhdGVTdHIgPyBuZXcgRGF0ZShkYXRlU3RyKSA6IHVuZGVmaW5lZDtcbiAgfVxuXG4gIHByaXZhdGUgc3RhdGljIGdldE5leHREaXZpZGVuZEluZm8oc3ltYm9sOiBzdHJpbmcpOiB7IGV4RGF0ZTogRGF0ZTsgYW1vdW50OiBudW1iZXIgfSB8IHVuZGVmaW5lZCB7XG4gICAgLy8gU2ltcGxpZmllZCAtIGluIHJlYWwgaW1wbGVtZW50YXRpb24sIGZldGNoIGZyb20gZGl2aWRlbmQgY2FsZW5kYXIgQVBJXG4gICAgY29uc3QgZGl2aWRlbmRDYWxlbmRhcjogeyBba2V5OiBzdHJpbmddOiB7IGV4RGF0ZTogc3RyaW5nOyBhbW91bnQ6IG51bWJlciB9IH0gPSB7XG4gICAgICAnQUFQTCc6IHsgZXhEYXRlOiAnMjAyNC0wMi0wOScsIGFtb3VudDogMC4yNCB9LFxuICAgICAgJ01TRlQnOiB7IGV4RGF0ZTogJzIwMjQtMDItMTUnLCBhbW91bnQ6IDAuNzUgfSxcbiAgICAgICdKUE0nOiB7IGV4RGF0ZTogJzIwMjQtMDEtMDUnLCBhbW91bnQ6IDEuMDUgfVxuICAgIH07XG4gICAgXG4gICAgY29uc3QgaW5mbyA9IGRpdmlkZW5kQ2FsZW5kYXJbc3ltYm9sXTtcbiAgICByZXR1cm4gaW5mbyA/IHsgZXhEYXRlOiBuZXcgRGF0ZShpbmZvLmV4RGF0ZSksIGFtb3VudDogaW5mby5hbW91bnQgfSA6IHVuZGVmaW5lZDtcbiAgfVxuXG4gIHByaXZhdGUgc3RhdGljIGRheXNCZXR3ZWVuKGRhdGUxOiBEYXRlLCBkYXRlMjogRGF0ZSk6IG51bWJlciB7XG4gICAgY29uc3QgZGlmZlRpbWUgPSBNYXRoLmFicyhkYXRlMi5nZXRUaW1lKCkgLSBkYXRlMS5nZXRUaW1lKCkpO1xuICAgIHJldHVybiBNYXRoLmNlaWwoZGlmZlRpbWUgLyAoMTAwMCAqIDYwICogNjAgKiAyNCkpO1xuICB9XG59XG4iXSwibmFtZXMiOlsiTWFya2V0QW5hbHl6ZXIiLCJhbmFseXplTWFya2V0Iiwic3RvY2siLCJoaXN0b3JpY2FsRGF0YSIsIm9wdGlvbnNEYXRhIiwidHJlbmQiLCJkZXRlcm1pbmVUcmVuZCIsInZvbGF0aWxpdHlSYW5rIiwiY2FsY3VsYXRlVm9sYXRpbGl0eVJhbmsiLCJyZWdpbWUiLCJkZXRlcm1pbmVNYXJrZXRSZWdpbWUiLCJzdXBwb3J0UmVzaXN0YW5jZSIsImZpbmRTdXBwb3J0UmVzaXN0YW5jZSIsInN1cHBvcnQiLCJyZXNpc3RhbmNlIiwiaW1wbGllZFZvbGF0aWxpdHkiLCJoaXN0b3JpY2FsVm9sYXRpbGl0eSIsImVhcm5pbmdzIiwiZ2V0TmV4dEVhcm5pbmdzRGF0ZSIsInN5bWJvbCIsImRpdmlkZW5kIiwiZ2V0TmV4dERpdmlkZW5kSW5mbyIsInJlY29tbWVuZFN0cmF0ZWdpZXMiLCJhbmFseXNpcyIsInN0cmF0ZWdpZXMiLCJwdXNoIiwicHJpY2UiLCJnZW5lcmF0ZVRyYWRpbmdBbGVydHMiLCJzdG9ja3MiLCJhbmFseXNlcyIsImFsZXJ0cyIsImZvckVhY2giLCJ0eXBlIiwibWVzc2FnZSIsInByaW9yaXR5IiwiZGlzdGFuY2VGcm9tU3VwcG9ydCIsImRpc3RhbmNlRnJvbVJlc2lzdGFuY2UiLCJ0b0ZpeGVkIiwiZGF5c0JldHdlZW4iLCJEYXRlIiwiZXhEYXRlIiwiYW1vdW50Iiwic29ydCIsImEiLCJiIiwicHJpb3JpdHlPcmRlciIsImNhbGN1bGF0ZU9wdGltYWxFbnRyeSIsInN0cmF0ZWd5IiwiY3VycmVudFByaWNlIiwidGltaW5nIiwicmVhc29uIiwidGFyZ2V0UHJpY2UiLCJkYXRhIiwibGVuZ3RoIiwicmVjZW50Iiwic2xpY2UiLCJvbGRlciIsInJlY2VudEF2ZyIsInJlZHVjZSIsInN1bSIsImQiLCJvbGRlckF2ZyIsImNoYW5nZSIsInJldHVybnMiLCJpIiwicmVjZW50UmV0dXJucyIsInJlY2VudFZvbCIsImNhbGN1bGF0ZVN0YW5kYXJkRGV2aWF0aW9uIiwiTWF0aCIsInNxcnQiLCJoaXN0b3JpY2FsVm9scyIsInBlcmlvZFJldHVybnMiLCJsb3dlckNvdW50IiwiZmlsdGVyIiwidm9sIiwidmFsdWVzIiwibWVhbiIsInZhbCIsInNxdWFyZWREaWZmcyIsIm1hcCIsInBvdyIsImF2Z1NxdWFyZWREaWZmIiwicHJpY2VzIiwibWluIiwibWF4Iiwic29ydGVkIiwiZmxvb3IiLCJlYXJuaW5nc0NhbGVuZGFyIiwiZGF0ZVN0ciIsInVuZGVmaW5lZCIsImRpdmlkZW5kQ2FsZW5kYXIiLCJpbmZvIiwiZGF0ZTEiLCJkYXRlMiIsImRpZmZUaW1lIiwiYWJzIiwiZ2V0VGltZSIsImNlaWwiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./lib/market-analysis.ts\n");

/***/ }),

/***/ "(rsc)/./lib/utils/rate-limiter.ts":
/*!***********************************!*\
  !*** ./lib/utils/rate-limiter.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RateLimiter: () => (/* binding */ RateLimiter),\n/* harmony export */   rateLimiter: () => (/* binding */ rateLimiter)\n/* harmony export */ });\n// Rate Limiter to prevent API overuse\n// Simple explanation: Prevents hitting API limits by spacing out requests\nclass RateLimiter {\n    /**\n   * Check if we can make a request\n   * Simple explanation: Like checking if you can make another phone call without going over your plan\n   */ canMakeRequest(service) {\n        const now = Date.now();\n        const limit = this.limits[service];\n        if (!this.requests[service]) {\n            this.requests[service] = [];\n        }\n        // Remove old requests outside the window\n        this.requests[service] = this.requests[service].filter((timestamp)=>now - timestamp < limit.window);\n        // Check if we're under the limit\n        return this.requests[service].length < limit.requests;\n    }\n    /**\n   * Record a request\n   * Simple explanation: Keep track of when you made a request\n   */ recordRequest(service) {\n        const now = Date.now();\n        if (!this.requests[service]) {\n            this.requests[service] = [];\n        }\n        this.requests[service].push(now);\n    }\n    /**\n   * Wait until we can make a request\n   * Simple explanation: Wait your turn if you've made too many requests\n   */ async waitForAvailability(service) {\n        while(!this.canMakeRequest(service)){\n            await new Promise((resolve)=>setTimeout(resolve, 1000)); // Wait 1 second\n        }\n    }\n    /**\n   * Get time until next available request\n   * Simple explanation: How long until you can make another request\n   */ getTimeUntilAvailable(service) {\n        if (this.canMakeRequest(service)) {\n            return 0;\n        }\n        const now = Date.now();\n        const limit = this.limits[service];\n        const oldestRequest = Math.min(...this.requests[service]);\n        return limit.window - (now - oldestRequest);\n    }\n    /**\n   * Get current usage stats\n   * Simple explanation: See how many requests you've made recently\n   */ getUsageStats(service) {\n        const now = Date.now();\n        const limit = this.limits[service];\n        if (!this.requests[service]) {\n            this.requests[service] = [];\n        }\n        // Clean old requests\n        this.requests[service] = this.requests[service].filter((timestamp)=>now - timestamp < limit.window);\n        const oldestRequest = this.requests[service].length > 0 ? Math.min(...this.requests[service]) : now;\n        return {\n            used: this.requests[service].length,\n            limit: limit.requests,\n            resetTime: oldestRequest + limit.window\n        };\n    }\n    constructor(){\n        this.requests = {};\n        this.limits = {\n            \"alpaca\": {\n                requests: 200,\n                window: 60000\n            },\n            \"fmp\": {\n                requests: 250,\n                window: 60000\n            }\n        };\n    }\n}\n// Global rate limiter instance\nconst rateLimiter = new RateLimiter();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/utils/rate-limiter.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/mime-db","vendor-chunks/axios","vendor-chunks/follow-redirects","vendor-chunks/debug","vendor-chunks/form-data","vendor-chunks/get-intrinsic","vendor-chunks/asynckit","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/function-bind","vendor-chunks/es-set-tostringtag","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/has-flag","vendor-chunks/gopd","vendor-chunks/es-define-property","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Faccount%2Froute&page=%2Fapi%2Faccount%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Faccount%2Froute.ts&appDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cericashacks%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CSJFit%5CDesktop%5Cericashacks&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();