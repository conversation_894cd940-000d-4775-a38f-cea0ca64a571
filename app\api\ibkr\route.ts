// API Route for Interactive Brokers Integration
// Provides professional-grade options trading through IBKR

import { NextRequest, NextResponse } from 'next/server';
import { IBKRClient } from '../../../lib/api/ibkr-client';

let ibkrClient: IBKRClient | null = null;

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');
    
    // Initialize IBKR client if not exists
    if (!ibkrClient) {
      ibkrClient = new IBKRClient();
    }

    switch (action) {
      case 'connect':
        const connected = await ibkrClient.connect();
        return NextResponse.json({
          success: connected,
          message: connected ? 'Connected to IBKR successfully' : 'Failed to connect to IBKR',
          data: { connected }
        });

      case 'status':
        return NextResponse.json({
          success: true,
          data: { 
            connected: ibkrClient.isConnected(),
            message: ibkrClient.isConnected() ? 'IBKR Connected' : 'IBKR Disconnected'
          }
        });

      case 'account':
        if (!ibkrClient.isConnected()) {
          return NextResponse.json({ 
            success: false, 
            error: 'Not connected to IBKR. Please connect first.' 
          }, { status: 400 });
        }
        
        const accountInfo = await ibkrClient.getAccountInfo();
        return NextResponse.json({
          success: true,
          data: accountInfo
        });

      case 'positions':
        if (!ibkrClient.isConnected()) {
          return NextResponse.json({ 
            success: false, 
            error: 'Not connected to IBKR. Please connect first.' 
          }, { status: 400 });
        }
        
        const positions = await ibkrClient.getPositions();
        return NextResponse.json({
          success: true,
          data: positions
        });

      case 'quotes':
        const symbols = searchParams.get('symbols');
        if (!symbols) {
          return NextResponse.json({ 
            success: false, 
            error: 'Symbols parameter required' 
          }, { status: 400 });
        }

        if (!ibkrClient.isConnected()) {
          return NextResponse.json({ 
            success: false, 
            error: 'Not connected to IBKR. Please connect first.' 
          }, { status: 400 });
        }

        const symbolList = symbols.split(',').map(s => s.trim().toUpperCase());
        const quotes = await ibkrClient.getStockQuotes(symbolList);
        return NextResponse.json({
          success: true,
          data: quotes
        });

      case 'options':
        const symbol = searchParams.get('symbol');
        if (!symbol) {
          return NextResponse.json({ 
            success: false, 
            error: 'Symbol parameter required' 
          }, { status: 400 });
        }

        if (!ibkrClient.isConnected()) {
          return NextResponse.json({ 
            success: false, 
            error: 'Not connected to IBKR. Please connect first.' 
          }, { status: 400 });
        }

        const expiration = searchParams.get('expiration') || undefined;
        const optionsChain = await ibkrClient.getOptionsChain(symbol, expiration);
        return NextResponse.json({
          success: true,
          data: optionsChain
        });

      default:
        return NextResponse.json({ 
          success: false, 
          error: 'Invalid action. Use: connect, status, account, positions, quotes, options' 
        }, { status: 400 });
    }

  } catch (error) {
    console.error('Error in IBKR API:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'IBKR API error',
        message: error instanceof Error ? error.message : 'Unknown error'
      }, 
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action } = body;

    // Initialize IBKR client if not exists
    if (!ibkrClient) {
      ibkrClient = new IBKRClient();
    }

    switch (action) {
      case 'place_order':
        const { 
          symbol, 
          strike, 
          expiration, 
          right, 
          orderAction, 
          quantity, 
          orderType, 
          limitPrice 
        } = body;

        if (!symbol || !strike || !expiration || !right || !orderAction || !quantity) {
          return NextResponse.json({ 
            success: false, 
            error: 'Missing required order parameters' 
          }, { status: 400 });
        }

        if (!ibkrClient.isConnected()) {
          return NextResponse.json({ 
            success: false, 
            error: 'Not connected to IBKR. Please connect first.' 
          }, { status: 400 });
        }

        const order = await ibkrClient.placeOptionsOrder(
          symbol,
          strike,
          expiration,
          right,
          orderAction,
          quantity,
          orderType || 'LMT',
          limitPrice
        );

        return NextResponse.json({
          success: true,
          data: order,
          message: `${orderAction} order placed successfully`
        });

      case 'cancel_order':
        const { orderId } = body;
        
        if (!orderId) {
          return NextResponse.json({ 
            success: false, 
            error: 'Order ID required' 
          }, { status: 400 });
        }

        if (!ibkrClient.isConnected()) {
          return NextResponse.json({ 
            success: false, 
            error: 'Not connected to IBKR. Please connect first.' 
          }, { status: 400 });
        }

        const cancelled = await ibkrClient.cancelOrder(orderId);
        return NextResponse.json({
          success: cancelled,
          message: cancelled ? 'Order cancelled successfully' : 'Failed to cancel order'
        });

      case 'order_status':
        const { orderIdToCheck } = body;
        
        if (!orderIdToCheck) {
          return NextResponse.json({ 
            success: false, 
            error: 'Order ID required' 
          }, { status: 400 });
        }

        if (!ibkrClient.isConnected()) {
          return NextResponse.json({ 
            success: false, 
            error: 'Not connected to IBKR. Please connect first.' 
          }, { status: 400 });
        }

        const status = await ibkrClient.getOrderStatus(orderIdToCheck);
        return NextResponse.json({
          success: true,
          data: status
        });

      default:
        return NextResponse.json({ 
          success: false, 
          error: 'Invalid action. Use: place_order, cancel_order, order_status' 
        }, { status: 400 });
    }

  } catch (error) {
    console.error('Error in IBKR POST API:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'IBKR API error',
        message: error instanceof Error ? error.message : 'Unknown error'
      }, 
      { status: 500 }
    );
  }
}
