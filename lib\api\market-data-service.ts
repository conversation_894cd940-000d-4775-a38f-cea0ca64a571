// Market Data Service - Combines Alpaca and FMP data
// Provides unified interface for all market data needs

import { AlpacaClient } from './alpaca-client';
import { FMPClient } from './fmp-client';
import { Stock, OptionsChain, MarketAnalysis, Position } from '../types';
import { MarketAnalyzer } from '../market-analysis';

export class MarketDataService {
  private alpaca: AlpacaClient;
  private fmp: FMPClient;

  constructor() {
    this.alpaca = new AlpacaClient();
    this.fmp = new FMPClient();
  }

  /**
   * Get comprehensive stock data combining both APIs
   * Simple explanation: Get complete stock information from multiple sources
   */
  async getStockData(symbols: string[]): Promise<{ [symbol: string]: Stock }> {
    try {
      // Get data from both sources
      const [alpacaData, fmpData] = await Promise.allSettled([
        this.alpaca.getStockPrices(symbols),
        this.fmp.getStockInfo(symbols)
      ]);

      const stocks: { [symbol: string]: Stock } = {};

      // Combine data, preferring real-time prices from Alpaca and company info from FMP
      symbols.forEach(symbol => {
        let stock: Stock = {
          symbol,
          name: `${symbol} Inc.`,
          price: 0,
          change: 0,
          changePercent: 0,
          volume: 0,
          marketCap: 0,
          sector: 'Unknown'
        };

        // Use FMP data as base (has company info)
        if (fmpData.status === 'fulfilled' && fmpData.value[symbol]) {
          stock = { ...stock, ...fmpData.value[symbol] };
        }

        // Override with Alpaca data if available (more real-time)
        if (alpacaData.status === 'fulfilled' && alpacaData.value[symbol]) {
          const alpacaStock = alpacaData.value[symbol];
          stock.price = alpacaStock.price;
          stock.change = alpacaStock.change;
          stock.changePercent = alpacaStock.changePercent;
          stock.volume = alpacaStock.volume;
        }

        stocks[symbol] = stock;
      });

      return stocks;
    } catch (error) {
      console.error('Error getting stock data:', error);
      throw new Error('Failed to fetch stock data');
    }
  }

  /**
   * Get options chains for multiple symbols
   * Simple explanation: Get all available option contracts for your stocks
   */
  async getOptionsChains(symbols: string[]): Promise<{ [symbol: string]: OptionsChain[] }> {
    try {
      const chains: { [symbol: string]: OptionsChain[] } = {};
      
      // Get options data from FMP (Alpaca doesn't provide options data in free tier)
      const promises = symbols.map(async (symbol) => {
        try {
          const chain = await this.fmp.getOptionsChain(symbol);
          chains[symbol] = chain;
        } catch (error) {
          console.error(`Error fetching options for ${symbol}:`, error);
          chains[symbol] = [];
        }
      });

      await Promise.all(promises);
      return chains;
    } catch (error) {
      console.error('Error getting options chains:', error);
      throw new Error('Failed to fetch options chains');
    }
  }

  /**
   * Get comprehensive market analysis for symbols
   * Simple explanation: Analyze market conditions to find trading opportunities
   */
  async getMarketAnalysis(symbols: string[]): Promise<{ [symbol: string]: MarketAnalysis }> {
    try {
      const analyses: { [symbol: string]: MarketAnalysis } = {};
      
      // Get historical data for analysis
      const promises = symbols.map(async (symbol) => {
        try {
          // Get historical data from FMP (more comprehensive)
          const historicalData = await this.fmp.getHistoricalPrices(symbol, '6month');
          
          // Convert to format expected by MarketAnalyzer
          const analysisData = historicalData.map(item => ({
            price: item.close,
            volume: item.volume,
            date: item.date
          }));

          // Get current stock data
          const stockData = await this.getStockData([symbol]);
          const stock = stockData[symbol];

          if (stock && analysisData.length > 0) {
            // Calculate implied and historical volatility
            const optionsData = {
              impliedVolatility: 25, // Default - would come from options chain
              historicalVolatility: this.calculateHistoricalVolatility(analysisData)
            };

            analyses[symbol] = MarketAnalyzer.analyzeMarket(
              stock,
              analysisData,
              optionsData
            );
          }
        } catch (error) {
          console.error(`Error analyzing ${symbol}:`, error);
          // Provide default analysis
          analyses[symbol] = {
            regime: 'NEUTRAL_TO_BULLISH',
            volatilityRank: 50,
            trend: 'NEUTRAL',
            support: 0,
            resistance: 0,
            impliedVolatility: 25,
            historicalVolatility: 20
          };
        }
      });

      await Promise.all(promises);
      return analyses;
    } catch (error) {
      console.error('Error getting market analysis:', error);
      throw new Error('Failed to perform market analysis');
    }
  }

  /**
   * Get account information and positions
   * Simple explanation: Check your account balance and current trades
   */
  async getAccountInfo(): Promise<{
    account: any;
    positions: any[];
  }> {
    try {
      const [account, positions] = await Promise.all([
        this.alpaca.getAccount(),
        this.alpaca.getPositions()
      ]);

      return { account, positions };
    } catch (error) {
      console.error('Error getting account info:', error);
      throw new Error('Failed to fetch account information');
    }
  }

  /**
   * Get earnings calendar for the next few weeks
   * Simple explanation: See when companies will report earnings
   */
  async getUpcomingEarnings(days: number = 14): Promise<any[]> {
    try {
      const today = new Date();
      const futureDate = new Date();
      futureDate.setDate(today.getDate() + days);

      const earnings = await this.fmp.getEarningsCalendar(
        today.toISOString().split('T')[0],
        futureDate.toISOString().split('T')[0]
      );

      return earnings;
    } catch (error) {
      console.error('Error getting earnings calendar:', error);
      return [];
    }
  }

  /**
   * Get dividend calendar for the next few weeks
   * Simple explanation: See when companies will pay dividends
   */
  async getUpcomingDividends(days: number = 30): Promise<any[]> {
    try {
      const today = new Date();
      const futureDate = new Date();
      futureDate.setDate(today.getDate() + days);

      const dividends = await this.fmp.getDividendCalendar(
        today.toISOString().split('T')[0],
        futureDate.toISOString().split('T')[0]
      );

      return dividends;
    } catch (error) {
      console.error('Error getting dividend calendar:', error);
      return [];
    }
  }

  /**
   * Get market overview (indices, sector performance)
   * Simple explanation: See how the overall market is doing
   */
  async getMarketOverview(): Promise<{
    indices: any[];
    sectors: any[];
    isMarketOpen: boolean;
  }> {
    try {
      const [indices, sectors, isMarketOpen] = await Promise.all([
        this.fmp.getMarketIndices(),
        this.fmp.getSectorPerformance(),
        this.alpaca.isMarketOpen()
      ]);

      return { indices, sectors, isMarketOpen };
    } catch (error) {
      console.error('Error getting market overview:', error);
      return { indices: [], sectors: [], isMarketOpen: false };
    }
  }

  /**
   * Place a stock order through Alpaca
   * Simple explanation: Buy or sell stocks
   */
  async placeOrder(
    symbol: string,
    quantity: number,
    side: 'buy' | 'sell',
    type: 'market' | 'limit' = 'market',
    limitPrice?: number
  ): Promise<any> {
    try {
      return await this.alpaca.placeStockOrder(symbol, quantity, side, type, limitPrice);
    } catch (error) {
      console.error('Error placing order:', error);
      throw new Error('Failed to place order');
    }
  }

  /**
   * Get order history
   * Simple explanation: See your past trades
   */
  async getOrderHistory(limit: number = 50): Promise<any[]> {
    try {
      return await this.alpaca.getOrders('all', limit);
    } catch (error) {
      console.error('Error getting order history:', error);
      return [];
    }
  }

  // Helper methods
  private calculateHistoricalVolatility(data: Array<{ price: number; date: Date }>): number {
    if (data.length < 20) return 20;

    // Calculate daily returns
    const returns = [];
    for (let i = 1; i < data.length; i++) {
      const dailyReturn = (data[i].price - data[i-1].price) / data[i-1].price;
      returns.push(dailyReturn);
    }

    // Calculate standard deviation
    const mean = returns.reduce((sum, ret) => sum + ret, 0) / returns.length;
    const squaredDiffs = returns.map(ret => Math.pow(ret - mean, 2));
    const variance = squaredDiffs.reduce((sum, diff) => sum + diff, 0) / returns.length;
    const stdDev = Math.sqrt(variance);

    // Annualize (252 trading days per year)
    return stdDev * Math.sqrt(252) * 100;
  }
}
