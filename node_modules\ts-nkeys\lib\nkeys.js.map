{"version": 3, "file": "nkeys.js", "sourceRoot": "", "sources": ["../../src/nkeys.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;GAaG;;;;;;;;;;;;;;;AAEH,mCAAsC;AACtC,2BAAwB;AACxB,mCAAmC;AACnC,iCAA8B;AAEjB,QAAA,OAAO,GAAG,QAAQ,CAAC;AAEhC,SAAgB,UAAU,CAAC,MAAc;IACrC,IAAI,OAAO,GAAG,OAAO,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC;IAC7C,IAAI,GAAG,GAAG,aAAK,CAAC,UAAU,CAAC,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;IACzD,OAAO,IAAI,OAAE,CAAC,GAAG,CAAC,CAAC;AACvB,CAAC;AAJD,gCAIC;AAED,SAAgB,aAAa;IACzB,OAAO,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AACtC,CAAC;AAFD,sCAEC;AAED,SAAgB,UAAU;IACtB,OAAO,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACnC,CAAC;AAFD,gCAEC;AAED,SAAgB,cAAc;IAC1B,OAAO,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;AACvC,CAAC;AAFD,wCAEC;AAED,SAAgB,aAAa;IACzB,OAAO,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AACtC,CAAC;AAFD,sCAEC;AAED,SAAgB,YAAY;IACxB,OAAO,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AACrC,CAAC;AAFD,oCAEC;AAED,SAAgB,UAAU,CAAC,GAAW;IAClC,IAAI,GAAG,GAAG,aAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;IAC7B,IAAI,MAAM,GAAG,QAAQ,CAAC,WAAW,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;IACpD,IAAI,QAAQ,CAAC,mBAAmB,CAAC,MAAM,CAAC,EAAE;QACtC,OAAO,IAAI,kBAAS,CAAC,GAAG,CAAC,CAAC;KAC7B;IACD,MAAM,IAAI,UAAU,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;AAC1D,CAAC;AAPD,gCAOC;AAED,SAAgB,QAAQ,CAAC,GAAW;IAChC,aAAK,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;IACtB,4BAA4B;IAC5B,OAAO,IAAI,OAAE,CAAC,GAAG,CAAC,CAAC;AACvB,CAAC;AAJD,4BAIC;AA6CD,IAAY,MAqBX;AArBD,WAAY,MAAM;IACd,sDAAsD;IACtD,qCAAc,CAAA;IAEd,0EAA0E;IAC1E,2CAAiB,CAAA;IAEjB,wEAAwE;IACxE,6CAAkB,CAAA;IAElB,oEAAoE;IACpE,yCAAgB,CAAA;IAEhB,sEAAsE;IACtE,0CAAgB,CAAA;IAEhB,sEAAsE;IACtE,yCAAW,CAAA;IAEX,gEAAgE;IAChE,qCAAc,CAAA;AAClB,CAAC,EArBW,MAAM,GAAN,cAAM,KAAN,cAAM,QAqBjB;AAED;;GAEG;AACH;IAAA;IAuCA,CAAC;IAtCU,4BAAmB,GAA1B,UAA2B,MAAc;QACrC,OAAO,MAAM,IAAI,MAAM,CAAC,MAAM;eACvB,MAAM,IAAI,MAAM,CAAC,QAAQ;eACzB,MAAM,IAAI,MAAM,CAAC,OAAO;eACxB,MAAM,IAAI,MAAM,CAAC,OAAO;eACxB,MAAM,IAAI,MAAM,CAAC,IAAI,CAAC;IACjC,CAAC;IAEM,8BAAqB,GAA5B,UAA6B,CAAS;QAClC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,CAAC;IAC5F,CAAC;IAEM,sBAAa,GAApB,UAAqB,MAAc;QAC/B,IAAI,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;QACjC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;IACnB,CAAC;IAEM,oBAAW,GAAlB,UAAmB,CAAS;QACxB,QAAQ,CAAC,EAAE;YACP,KAAK,MAAM,CAAC,IAAI;gBACZ,OAAO,MAAM,CAAC,IAAI,CAAC;YACvB,KAAK,MAAM,CAAC,OAAO;gBACf,OAAO,MAAM,CAAC,OAAO,CAAC;YAC1B,KAAK,MAAM,CAAC,QAAQ;gBAChB,OAAO,MAAM,CAAC,QAAQ,CAAC;YAC3B,KAAK,MAAM,CAAC,MAAM;gBACd,OAAO,MAAM,CAAC,MAAM,CAAC;YACzB,KAAK,MAAM,CAAC,OAAO;gBACf,OAAO,MAAM,CAAC,OAAO,CAAC;YAC1B,KAAK,MAAM,CAAC,OAAO;gBACf,OAAO,MAAM,CAAC,OAAO,CAAC;YAC1B,KAAK,MAAM,CAAC,IAAI;gBACZ,OAAO,MAAM,CAAC,IAAI,CAAC;YACvB;gBACI,OAAO,CAAC,CAAC,CAAC;SACjB;IACL,CAAC;IACL,eAAC;AAAD,CAAC,AAvCD,IAuCC;AAvCY,4BAAQ;AAyCrB,IAAY,cAaX;AAbD,WAAY,cAAc;IACtB,kEAAgD,CAAA;IAChD,mDAAwC,CAAA;IACxC,gEAA+C,CAAA;IAC/C,+DAAgD,CAAA;IAChD,qDAAyC,CAAA;IACzC,gEAAgD,CAAA;IAChD,2EAA0D,CAAA;IAC1D,8EAAmE,CAAA;IACnE,2EAA6D,CAAA;IAC7D,6DAA6C,CAAA;IAC7C,mEAAmD,CAAA;IACnD,+CAAsC,CAAA;AAC1C,CAAC,EAbW,cAAc,GAAd,sBAAc,KAAd,sBAAc,QAazB;AAED;IAAgC,8BAAK;IAKjC;;;;;;OAMG;IACH,oBAAY,IAAoB,EAAE,YAAoB;QAAtD,YACI,kBAAM,IAAI,CAAC,SAKd;QAJG,KAAK,CAAC,iBAAiB,CAAC,KAAI,EAAE,KAAI,CAAC,WAAW,CAAC,CAAC;QAChD,KAAI,CAAC,IAAI,GAAG,YAAY,CAAC;QACzB,KAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,KAAI,CAAC,YAAY,GAAG,YAAY,CAAC;;IACrC,CAAC;IACL,iBAAC;AAAD,CAAC,AAnBD,CAAgC,KAAK,GAmBpC;AAnBY,gCAAU"}