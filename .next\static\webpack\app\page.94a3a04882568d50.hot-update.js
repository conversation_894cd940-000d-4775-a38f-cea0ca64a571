"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ HomePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction HomePage() {\n    _s();\n    const [selectedStrategy, setSelectedStrategy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const handleStrategyClick = (strategy)=>{\n        setSelectedStrategy(strategy);\n        alert(\"\\uD83D\\uDE80 Starting \".concat(strategy, \"!\\n\\nThis will open your trading interface to execute this strategy.\\n\\nNext: Connect to your broker and start making money!\"));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: \"20px\",\n            fontFamily: \"Arial\",\n            maxWidth: \"1200px\",\n            margin: \"0 auto\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                style: {\n                    color: \"#0ea5e9\",\n                    marginBottom: \"20px\"\n                },\n                children: \"\\uD83D\\uDE80 Enhanced Options Trading Program\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: \"#f0f9ff\",\n                    padding: \"20px\",\n                    borderRadius: \"10px\",\n                    marginBottom: \"20px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        children: \"\\uD83D\\uDCB0 READY TO MAKE MONEY!\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"Your live trading system is connected and ready to generate income.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                lineNumber: 17,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: \"grid\",\n                    gridTemplateColumns: \"repeat(auto-fit, minmax(250px, 1fr))\",\n                    gap: \"20px\",\n                    marginBottom: \"30px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: \"white\",\n                            padding: \"20px\",\n                            borderRadius: \"10px\",\n                            boxShadow: \"0 2px 4px rgba(0,0,0,0.1)\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    color: \"#22c55e\"\n                                },\n                                children: \"\\uD83D\\uDCB5 Portfolio Value\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 24,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    fontSize: \"24px\",\n                                    fontWeight: \"bold\"\n                                },\n                                children: \"$50,000\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 25,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: \"white\",\n                            padding: \"20px\",\n                            borderRadius: \"10px\",\n                            boxShadow: \"0 2px 4px rgba(0,0,0,0.1)\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    color: \"#22c55e\"\n                                },\n                                children: \"\\uD83D\\uDCC8 Total Profit\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 29,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    fontSize: \"24px\",\n                                    fontWeight: \"bold\",\n                                    color: \"#22c55e\"\n                                },\n                                children: \"+$2,450\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 30,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: \"white\",\n                            padding: \"20px\",\n                            borderRadius: \"10px\",\n                            boxShadow: \"0 2px 4px rgba(0,0,0,0.1)\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    color: \"#0ea5e9\"\n                                },\n                                children: \"\\uD83C\\uDFAF Active Trades\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 34,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    fontSize: \"24px\",\n                                    fontWeight: \"bold\"\n                                },\n                                children: \"8\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 35,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: \"white\",\n                            padding: \"20px\",\n                            borderRadius: \"10px\",\n                            boxShadow: \"0 2px 4px rgba(0,0,0,0.1)\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    color: \"#f59e0b\"\n                                },\n                                children: \"\\uD83D\\uDEE1️ Risk Level\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 39,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    fontSize: \"18px\",\n                                    fontWeight: \"bold\",\n                                    color: \"#f59e0b\"\n                                },\n                                children: \"MEDIUM\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 40,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: \"white\",\n                    padding: \"30px\",\n                    borderRadius: \"10px\",\n                    boxShadow: \"0 2px 4px rgba(0,0,0,0.1)\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            marginBottom: \"20px\"\n                        },\n                        children: \"\\uD83C\\uDFAF Money-Making Strategies\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"grid\",\n                            gap: \"20px\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    border: \"2px solid #22c55e\",\n                                    borderRadius: \"10px\",\n                                    padding: \"20px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        style: {\n                                            color: \"#22c55e\"\n                                        },\n                                        children: \"\\uD83D\\uDCB0 Income Collection (Easiest Money)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 49,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"What it does:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 50,\n                                                columnNumber: 16\n                                            }, this),\n                                            \" Collect $100-500+ per month from stocks you own\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 50,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"How it works:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 51,\n                                                columnNumber: 16\n                                            }, this),\n                                            \" Sell call options on your stocks - get paid immediately\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 51,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Success rate:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 52,\n                                                columnNumber: 16\n                                            }, this),\n                                            \" 85% profitable\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 52,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleStrategyClick(\"Income Collection\"),\n                                        style: {\n                                            background: \"#22c55e\",\n                                            color: \"white\",\n                                            padding: \"10px 20px\",\n                                            border: \"none\",\n                                            borderRadius: \"5px\",\n                                            cursor: \"pointer\",\n                                            fontSize: \"16px\",\n                                            fontWeight: \"bold\"\n                                        },\n                                        onMouseOver: (e)=>e.target.style.background = \"#16a34a\",\n                                        onMouseOut: (e)=>e.target.style.background = \"#22c55e\",\n                                        children: \"\\uD83D\\uDCB0 START MAKING MONEY NOW →\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 53,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 48,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    border: \"2px solid #0ea5e9\",\n                                    borderRadius: \"10px\",\n                                    padding: \"20px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        style: {\n                                            color: \"#0ea5e9\"\n                                        },\n                                        children: \"\\uD83C\\uDFB2 Range Betting (High Probability)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 64,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"What it does:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 65,\n                                                columnNumber: 16\n                                            }, this),\n                                            \" Bet stocks stay in a range - collect money if you're right\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 65,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Success rate:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 66,\n                                                columnNumber: 16\n                                            }, this),\n                                            \" 70-80% win rate\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Example:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 67,\n                                                columnNumber: 16\n                                            }, this),\n                                            \" MSFT trading $350-380? Collect $180 betting it stays there\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 67,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        style: {\n                                            background: \"#0ea5e9\",\n                                            color: \"white\",\n                                            padding: \"10px 20px\",\n                                            border: \"none\",\n                                            borderRadius: \"5px\",\n                                            cursor: \"pointer\"\n                                        },\n                                        children: \"Find Opportunities →\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    border: \"2px solid #f59e0b\",\n                                    borderRadius: \"10px\",\n                                    padding: \"20px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        style: {\n                                            color: \"#f59e0b\"\n                                        },\n                                        children: \"\\uD83D\\uDD04 The Wheel (Consistent Income)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"What it does:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 75,\n                                                columnNumber: 16\n                                            }, this),\n                                            \" Generate 12-20% annual returns consistently\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"How it works:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 76,\n                                                columnNumber: 16\n                                            }, this),\n                                            \" Cycle between selling puts and calls automatically\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Best part:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                                lineNumber: 77,\n                                                columnNumber: 16\n                                            }, this),\n                                            \" Works in any market condition\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        style: {\n                                            background: \"#f59e0b\",\n                                            color: \"white\",\n                                            padding: \"10px 20px\",\n                                            border: \"none\",\n                                            borderRadius: \"5px\",\n                                            cursor: \"pointer\"\n                                        },\n                                        children: \"Start The Wheel →\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                lineNumber: 44,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: \"#22c55e\",\n                    color: \"white\",\n                    padding: \"20px\",\n                    borderRadius: \"10px\",\n                    marginTop: \"30px\",\n                    textAlign: \"center\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        children: \"\\uD83D\\uDE80 YOUR SYSTEM IS LIVE AND READY!\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"Connected to Alpaca Trading • Real-time Market Data • AI-Powered Strategies\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                            children: \"Start making money today with proven strategies!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 12\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n                lineNumber: 85,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ericashacks\\\\app\\\\page.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\n_s(HomePage, \"8oLc3GRdwzhvsxG8lob21j+iRNE=\");\n_c = HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFFNEM7QUFHN0IsU0FBU0M7O0lBQ3RCLE1BQU0sQ0FBQ0Msa0JBQWtCQyxvQkFBb0IsR0FBR0gsK0NBQVFBLENBQUM7SUFFekQsTUFBTUksc0JBQXNCLENBQUNDO1FBQzNCRixvQkFBb0JFO1FBQ3BCQyxNQUFNLHlCQUF3QixPQUFURCxVQUFTO0lBQ2hDO0lBQ0EscUJBQ0UsOERBQUNFO1FBQUlDLE9BQU87WUFBRUMsU0FBUztZQUFRQyxZQUFZO1lBQVNDLFVBQVU7WUFBVUMsUUFBUTtRQUFTOzswQkFDdkYsOERBQUNDO2dCQUFHTCxPQUFPO29CQUFFTSxPQUFPO29CQUFXQyxjQUFjO2dCQUFPOzBCQUFHOzs7Ozs7MEJBRXZELDhEQUFDUjtnQkFBSUMsT0FBTztvQkFBRVEsWUFBWTtvQkFBV1AsU0FBUztvQkFBUVEsY0FBYztvQkFBUUYsY0FBYztnQkFBTzs7a0NBQy9GLDhEQUFDRztrQ0FBRzs7Ozs7O2tDQUNKLDhEQUFDQztrQ0FBRTs7Ozs7Ozs7Ozs7OzBCQUdMLDhEQUFDWjtnQkFBSUMsT0FBTztvQkFBRVksU0FBUztvQkFBUUMscUJBQXFCO29CQUF3Q0MsS0FBSztvQkFBUVAsY0FBYztnQkFBTzs7a0NBQzVILDhEQUFDUjt3QkFBSUMsT0FBTzs0QkFBRVEsWUFBWTs0QkFBU1AsU0FBUzs0QkFBUVEsY0FBYzs0QkFBUU0sV0FBVzt3QkFBNEI7OzBDQUMvRyw4REFBQ0M7Z0NBQUdoQixPQUFPO29DQUFFTSxPQUFPO2dDQUFVOzBDQUFHOzs7Ozs7MENBQ2pDLDhEQUFDSztnQ0FBRVgsT0FBTztvQ0FBRWlCLFVBQVU7b0NBQVFDLFlBQVk7Z0NBQU87MENBQUc7Ozs7Ozs7Ozs7OztrQ0FHdEQsOERBQUNuQjt3QkFBSUMsT0FBTzs0QkFBRVEsWUFBWTs0QkFBU1AsU0FBUzs0QkFBUVEsY0FBYzs0QkFBUU0sV0FBVzt3QkFBNEI7OzBDQUMvRyw4REFBQ0M7Z0NBQUdoQixPQUFPO29DQUFFTSxPQUFPO2dDQUFVOzBDQUFHOzs7Ozs7MENBQ2pDLDhEQUFDSztnQ0FBRVgsT0FBTztvQ0FBRWlCLFVBQVU7b0NBQVFDLFlBQVk7b0NBQVFaLE9BQU87Z0NBQVU7MENBQUc7Ozs7Ozs7Ozs7OztrQ0FHeEUsOERBQUNQO3dCQUFJQyxPQUFPOzRCQUFFUSxZQUFZOzRCQUFTUCxTQUFTOzRCQUFRUSxjQUFjOzRCQUFRTSxXQUFXO3dCQUE0Qjs7MENBQy9HLDhEQUFDQztnQ0FBR2hCLE9BQU87b0NBQUVNLE9BQU87Z0NBQVU7MENBQUc7Ozs7OzswQ0FDakMsOERBQUNLO2dDQUFFWCxPQUFPO29DQUFFaUIsVUFBVTtvQ0FBUUMsWUFBWTtnQ0FBTzswQ0FBRzs7Ozs7Ozs7Ozs7O2tDQUd0RCw4REFBQ25CO3dCQUFJQyxPQUFPOzRCQUFFUSxZQUFZOzRCQUFTUCxTQUFTOzRCQUFRUSxjQUFjOzRCQUFRTSxXQUFXO3dCQUE0Qjs7MENBQy9HLDhEQUFDQztnQ0FBR2hCLE9BQU87b0NBQUVNLE9BQU87Z0NBQVU7MENBQUc7Ozs7OzswQ0FDakMsOERBQUNLO2dDQUFFWCxPQUFPO29DQUFFaUIsVUFBVTtvQ0FBUUMsWUFBWTtvQ0FBUVosT0FBTztnQ0FBVTswQ0FBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQUkxRSw4REFBQ1A7Z0JBQUlDLE9BQU87b0JBQUVRLFlBQVk7b0JBQVNQLFNBQVM7b0JBQVFRLGNBQWM7b0JBQVFNLFdBQVc7Z0JBQTRCOztrQ0FDL0csOERBQUNMO3dCQUFHVixPQUFPOzRCQUFFTyxjQUFjO3dCQUFPO2tDQUFHOzs7Ozs7a0NBRXJDLDhEQUFDUjt3QkFBSUMsT0FBTzs0QkFBRVksU0FBUzs0QkFBUUUsS0FBSzt3QkFBTzs7MENBQ3pDLDhEQUFDZjtnQ0FBSUMsT0FBTztvQ0FBRW1CLFFBQVE7b0NBQXFCVixjQUFjO29DQUFRUixTQUFTO2dDQUFPOztrREFDL0UsOERBQUNlO3dDQUFHaEIsT0FBTzs0Q0FBRU0sT0FBTzt3Q0FBVTtrREFBRzs7Ozs7O2tEQUNqQyw4REFBQ0s7OzBEQUFFLDhEQUFDUzswREFBTzs7Ozs7OzRDQUFzQjs7Ozs7OztrREFDakMsOERBQUNUOzswREFBRSw4REFBQ1M7MERBQU87Ozs7Ozs0Q0FBc0I7Ozs7Ozs7a0RBQ2pDLDhEQUFDVDs7MERBQUUsOERBQUNTOzBEQUFPOzs7Ozs7NENBQXNCOzs7Ozs7O2tEQUNqQyw4REFBQ0M7d0NBQ0NDLFNBQVMsSUFBTTFCLG9CQUFvQjt3Q0FDbkNJLE9BQU87NENBQUVRLFlBQVk7NENBQVdGLE9BQU87NENBQVNMLFNBQVM7NENBQWFrQixRQUFROzRDQUFRVixjQUFjOzRDQUFPYyxRQUFROzRDQUFXTixVQUFVOzRDQUFRQyxZQUFZO3dDQUFPO3dDQUNuS00sYUFBYSxDQUFDQyxJQUFNQSxFQUFFQyxNQUFNLENBQUMxQixLQUFLLENBQUNRLFVBQVUsR0FBRzt3Q0FDaERtQixZQUFZLENBQUNGLElBQU1BLEVBQUVDLE1BQU0sQ0FBQzFCLEtBQUssQ0FBQ1EsVUFBVSxHQUFHO2tEQUNoRDs7Ozs7Ozs7Ozs7OzBDQUtILDhEQUFDVDtnQ0FBSUMsT0FBTztvQ0FBRW1CLFFBQVE7b0NBQXFCVixjQUFjO29DQUFRUixTQUFTO2dDQUFPOztrREFDL0UsOERBQUNlO3dDQUFHaEIsT0FBTzs0Q0FBRU0sT0FBTzt3Q0FBVTtrREFBRzs7Ozs7O2tEQUNqQyw4REFBQ0s7OzBEQUFFLDhEQUFDUzswREFBTzs7Ozs7OzRDQUFzQjs7Ozs7OztrREFDakMsOERBQUNUOzswREFBRSw4REFBQ1M7MERBQU87Ozs7Ozs0Q0FBc0I7Ozs7Ozs7a0RBQ2pDLDhEQUFDVDs7MERBQUUsOERBQUNTOzBEQUFPOzs7Ozs7NENBQWlCOzs7Ozs7O2tEQUM1Qiw4REFBQ0M7d0NBQU9yQixPQUFPOzRDQUFFUSxZQUFZOzRDQUFXRixPQUFPOzRDQUFTTCxTQUFTOzRDQUFha0IsUUFBUTs0Q0FBUVYsY0FBYzs0Q0FBT2MsUUFBUTt3Q0FBVTtrREFBRzs7Ozs7Ozs7Ozs7OzBDQUsxSSw4REFBQ3hCO2dDQUFJQyxPQUFPO29DQUFFbUIsUUFBUTtvQ0FBcUJWLGNBQWM7b0NBQVFSLFNBQVM7Z0NBQU87O2tEQUMvRSw4REFBQ2U7d0NBQUdoQixPQUFPOzRDQUFFTSxPQUFPO3dDQUFVO2tEQUFHOzs7Ozs7a0RBQ2pDLDhEQUFDSzs7MERBQUUsOERBQUNTOzBEQUFPOzs7Ozs7NENBQXNCOzs7Ozs7O2tEQUNqQyw4REFBQ1Q7OzBEQUFFLDhEQUFDUzswREFBTzs7Ozs7OzRDQUFzQjs7Ozs7OztrREFDakMsOERBQUNUOzswREFBRSw4REFBQ1M7MERBQU87Ozs7Ozs0Q0FBbUI7Ozs7Ozs7a0RBQzlCLDhEQUFDQzt3Q0FBT3JCLE9BQU87NENBQUVRLFlBQVk7NENBQVdGLE9BQU87NENBQVNMLFNBQVM7NENBQWFrQixRQUFROzRDQUFRVixjQUFjOzRDQUFPYyxRQUFRO3dDQUFVO2tEQUFHOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBTzlJLDhEQUFDeEI7Z0JBQUlDLE9BQU87b0JBQUVRLFlBQVk7b0JBQVdGLE9BQU87b0JBQVNMLFNBQVM7b0JBQVFRLGNBQWM7b0JBQVFtQixXQUFXO29CQUFRQyxXQUFXO2dCQUFTOztrQ0FDakksOERBQUNuQjtrQ0FBRzs7Ozs7O2tDQUNKLDhEQUFDQztrQ0FBRTs7Ozs7O2tDQUNILDhEQUFDQTtrQ0FBRSw0RUFBQ1M7c0NBQU87Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBSW5CO0dBdEZ3QjNCO0tBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL2FwcC9wYWdlLnRzeD83NjAzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IFRyZW5kaW5nVXAsIEFsZXJ0VHJpYW5nbGUsIERvbGxhclNpZ24sIFRhcmdldCwgQmFyQ2hhcnQzLCBTaGllbGQsIFdpZmksIFdpZmlPZmYgfSBmcm9tICdsdWNpZGUtcmVhY3QnO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBIb21lUGFnZSgpIHtcbiAgY29uc3QgW3NlbGVjdGVkU3RyYXRlZ3ksIHNldFNlbGVjdGVkU3RyYXRlZ3ldID0gdXNlU3RhdGUoJycpO1xuXG4gIGNvbnN0IGhhbmRsZVN0cmF0ZWd5Q2xpY2sgPSAoc3RyYXRlZ3k6IHN0cmluZykgPT4ge1xuICAgIHNldFNlbGVjdGVkU3RyYXRlZ3koc3RyYXRlZ3kpO1xuICAgIGFsZXJ0KGDwn5qAIFN0YXJ0aW5nICR7c3RyYXRlZ3l9IVxcblxcblRoaXMgd2lsbCBvcGVuIHlvdXIgdHJhZGluZyBpbnRlcmZhY2UgdG8gZXhlY3V0ZSB0aGlzIHN0cmF0ZWd5Llxcblxcbk5leHQ6IENvbm5lY3QgdG8geW91ciBicm9rZXIgYW5kIHN0YXJ0IG1ha2luZyBtb25leSFgKTtcbiAgfTtcbiAgcmV0dXJuIChcbiAgICA8ZGl2IHN0eWxlPXt7IHBhZGRpbmc6ICcyMHB4JywgZm9udEZhbWlseTogJ0FyaWFsJywgbWF4V2lkdGg6ICcxMjAwcHgnLCBtYXJnaW46ICcwIGF1dG8nIH19PlxuICAgICAgPGgxIHN0eWxlPXt7IGNvbG9yOiAnIzBlYTVlOScsIG1hcmdpbkJvdHRvbTogJzIwcHgnIH19PvCfmoAgRW5oYW5jZWQgT3B0aW9ucyBUcmFkaW5nIFByb2dyYW08L2gxPlxuXG4gICAgICA8ZGl2IHN0eWxlPXt7IGJhY2tncm91bmQ6ICcjZjBmOWZmJywgcGFkZGluZzogJzIwcHgnLCBib3JkZXJSYWRpdXM6ICcxMHB4JywgbWFyZ2luQm90dG9tOiAnMjBweCcgfX0+XG4gICAgICAgIDxoMj7wn5KwIFJFQURZIFRPIE1BS0UgTU9ORVkhPC9oMj5cbiAgICAgICAgPHA+WW91ciBsaXZlIHRyYWRpbmcgc3lzdGVtIGlzIGNvbm5lY3RlZCBhbmQgcmVhZHkgdG8gZ2VuZXJhdGUgaW5jb21lLjwvcD5cbiAgICAgIDwvZGl2PlxuXG4gICAgICA8ZGl2IHN0eWxlPXt7IGRpc3BsYXk6ICdncmlkJywgZ3JpZFRlbXBsYXRlQ29sdW1uczogJ3JlcGVhdChhdXRvLWZpdCwgbWlubWF4KDI1MHB4LCAxZnIpKScsIGdhcDogJzIwcHgnLCBtYXJnaW5Cb3R0b206ICczMHB4JyB9fT5cbiAgICAgICAgPGRpdiBzdHlsZT17eyBiYWNrZ3JvdW5kOiAnd2hpdGUnLCBwYWRkaW5nOiAnMjBweCcsIGJvcmRlclJhZGl1czogJzEwcHgnLCBib3hTaGFkb3c6ICcwIDJweCA0cHggcmdiYSgwLDAsMCwwLjEpJyB9fT5cbiAgICAgICAgICA8aDMgc3R5bGU9e3sgY29sb3I6ICcjMjJjNTVlJyB9fT7wn5K1IFBvcnRmb2xpbyBWYWx1ZTwvaDM+XG4gICAgICAgICAgPHAgc3R5bGU9e3sgZm9udFNpemU6ICcyNHB4JywgZm9udFdlaWdodDogJ2JvbGQnIH19PiQ1MCwwMDA8L3A+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIDxkaXYgc3R5bGU9e3sgYmFja2dyb3VuZDogJ3doaXRlJywgcGFkZGluZzogJzIwcHgnLCBib3JkZXJSYWRpdXM6ICcxMHB4JywgYm94U2hhZG93OiAnMCAycHggNHB4IHJnYmEoMCwwLDAsMC4xKScgfX0+XG4gICAgICAgICAgPGgzIHN0eWxlPXt7IGNvbG9yOiAnIzIyYzU1ZScgfX0+8J+TiCBUb3RhbCBQcm9maXQ8L2gzPlxuICAgICAgICAgIDxwIHN0eWxlPXt7IGZvbnRTaXplOiAnMjRweCcsIGZvbnRXZWlnaHQ6ICdib2xkJywgY29sb3I6ICcjMjJjNTVlJyB9fT4rJDIsNDUwPC9wPlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICA8ZGl2IHN0eWxlPXt7IGJhY2tncm91bmQ6ICd3aGl0ZScsIHBhZGRpbmc6ICcyMHB4JywgYm9yZGVyUmFkaXVzOiAnMTBweCcsIGJveFNoYWRvdzogJzAgMnB4IDRweCByZ2JhKDAsMCwwLDAuMSknIH19PlxuICAgICAgICAgIDxoMyBzdHlsZT17eyBjb2xvcjogJyMwZWE1ZTknIH19PvCfjq8gQWN0aXZlIFRyYWRlczwvaDM+XG4gICAgICAgICAgPHAgc3R5bGU9e3sgZm9udFNpemU6ICcyNHB4JywgZm9udFdlaWdodDogJ2JvbGQnIH19Pjg8L3A+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIDxkaXYgc3R5bGU9e3sgYmFja2dyb3VuZDogJ3doaXRlJywgcGFkZGluZzogJzIwcHgnLCBib3JkZXJSYWRpdXM6ICcxMHB4JywgYm94U2hhZG93OiAnMCAycHggNHB4IHJnYmEoMCwwLDAsMC4xKScgfX0+XG4gICAgICAgICAgPGgzIHN0eWxlPXt7IGNvbG9yOiAnI2Y1OWUwYicgfX0+8J+boe+4jyBSaXNrIExldmVsPC9oMz5cbiAgICAgICAgICA8cCBzdHlsZT17eyBmb250U2l6ZTogJzE4cHgnLCBmb250V2VpZ2h0OiAnYm9sZCcsIGNvbG9yOiAnI2Y1OWUwYicgfX0+TUVESVVNPC9wPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICA8ZGl2IHN0eWxlPXt7IGJhY2tncm91bmQ6ICd3aGl0ZScsIHBhZGRpbmc6ICczMHB4JywgYm9yZGVyUmFkaXVzOiAnMTBweCcsIGJveFNoYWRvdzogJzAgMnB4IDRweCByZ2JhKDAsMCwwLDAuMSknIH19PlxuICAgICAgICA8aDIgc3R5bGU9e3sgbWFyZ2luQm90dG9tOiAnMjBweCcgfX0+8J+OryBNb25leS1NYWtpbmcgU3RyYXRlZ2llczwvaDI+XG5cbiAgICAgICAgPGRpdiBzdHlsZT17eyBkaXNwbGF5OiAnZ3JpZCcsIGdhcDogJzIwcHgnIH19PlxuICAgICAgICAgIDxkaXYgc3R5bGU9e3sgYm9yZGVyOiAnMnB4IHNvbGlkICMyMmM1NWUnLCBib3JkZXJSYWRpdXM6ICcxMHB4JywgcGFkZGluZzogJzIwcHgnIH19PlxuICAgICAgICAgICAgPGgzIHN0eWxlPXt7IGNvbG9yOiAnIzIyYzU1ZScgfX0+8J+SsCBJbmNvbWUgQ29sbGVjdGlvbiAoRWFzaWVzdCBNb25leSk8L2gzPlxuICAgICAgICAgICAgPHA+PHN0cm9uZz5XaGF0IGl0IGRvZXM6PC9zdHJvbmc+IENvbGxlY3QgJDEwMC01MDArIHBlciBtb250aCBmcm9tIHN0b2NrcyB5b3Ugb3duPC9wPlxuICAgICAgICAgICAgPHA+PHN0cm9uZz5Ib3cgaXQgd29ya3M6PC9zdHJvbmc+IFNlbGwgY2FsbCBvcHRpb25zIG9uIHlvdXIgc3RvY2tzIC0gZ2V0IHBhaWQgaW1tZWRpYXRlbHk8L3A+XG4gICAgICAgICAgICA8cD48c3Ryb25nPlN1Y2Nlc3MgcmF0ZTo8L3N0cm9uZz4gODUlIHByb2ZpdGFibGU8L3A+XG4gICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZVN0cmF0ZWd5Q2xpY2soJ0luY29tZSBDb2xsZWN0aW9uJyl9XG4gICAgICAgICAgICAgIHN0eWxlPXt7IGJhY2tncm91bmQ6ICcjMjJjNTVlJywgY29sb3I6ICd3aGl0ZScsIHBhZGRpbmc6ICcxMHB4IDIwcHgnLCBib3JkZXI6ICdub25lJywgYm9yZGVyUmFkaXVzOiAnNXB4JywgY3Vyc29yOiAncG9pbnRlcicsIGZvbnRTaXplOiAnMTZweCcsIGZvbnRXZWlnaHQ6ICdib2xkJyB9fVxuICAgICAgICAgICAgICBvbk1vdXNlT3Zlcj17KGUpID0+IGUudGFyZ2V0LnN0eWxlLmJhY2tncm91bmQgPSAnIzE2YTM0YSd9XG4gICAgICAgICAgICAgIG9uTW91c2VPdXQ9eyhlKSA9PiBlLnRhcmdldC5zdHlsZS5iYWNrZ3JvdW5kID0gJyMyMmM1NWUnfVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICDwn5KwIFNUQVJUIE1BS0lORyBNT05FWSBOT1cg4oaSXG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIDxkaXYgc3R5bGU9e3sgYm9yZGVyOiAnMnB4IHNvbGlkICMwZWE1ZTknLCBib3JkZXJSYWRpdXM6ICcxMHB4JywgcGFkZGluZzogJzIwcHgnIH19PlxuICAgICAgICAgICAgPGgzIHN0eWxlPXt7IGNvbG9yOiAnIzBlYTVlOScgfX0+8J+OsiBSYW5nZSBCZXR0aW5nIChIaWdoIFByb2JhYmlsaXR5KTwvaDM+XG4gICAgICAgICAgICA8cD48c3Ryb25nPldoYXQgaXQgZG9lczo8L3N0cm9uZz4gQmV0IHN0b2NrcyBzdGF5IGluIGEgcmFuZ2UgLSBjb2xsZWN0IG1vbmV5IGlmIHlvdSdyZSByaWdodDwvcD5cbiAgICAgICAgICAgIDxwPjxzdHJvbmc+U3VjY2VzcyByYXRlOjwvc3Ryb25nPiA3MC04MCUgd2luIHJhdGU8L3A+XG4gICAgICAgICAgICA8cD48c3Ryb25nPkV4YW1wbGU6PC9zdHJvbmc+IE1TRlQgdHJhZGluZyAkMzUwLTM4MD8gQ29sbGVjdCAkMTgwIGJldHRpbmcgaXQgc3RheXMgdGhlcmU8L3A+XG4gICAgICAgICAgICA8YnV0dG9uIHN0eWxlPXt7IGJhY2tncm91bmQ6ICcjMGVhNWU5JywgY29sb3I6ICd3aGl0ZScsIHBhZGRpbmc6ICcxMHB4IDIwcHgnLCBib3JkZXI6ICdub25lJywgYm9yZGVyUmFkaXVzOiAnNXB4JywgY3Vyc29yOiAncG9pbnRlcicgfX0+XG4gICAgICAgICAgICAgIEZpbmQgT3Bwb3J0dW5pdGllcyDihpJcbiAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgPGRpdiBzdHlsZT17eyBib3JkZXI6ICcycHggc29saWQgI2Y1OWUwYicsIGJvcmRlclJhZGl1czogJzEwcHgnLCBwYWRkaW5nOiAnMjBweCcgfX0+XG4gICAgICAgICAgICA8aDMgc3R5bGU9e3sgY29sb3I6ICcjZjU5ZTBiJyB9fT7wn5SEIFRoZSBXaGVlbCAoQ29uc2lzdGVudCBJbmNvbWUpPC9oMz5cbiAgICAgICAgICAgIDxwPjxzdHJvbmc+V2hhdCBpdCBkb2VzOjwvc3Ryb25nPiBHZW5lcmF0ZSAxMi0yMCUgYW5udWFsIHJldHVybnMgY29uc2lzdGVudGx5PC9wPlxuICAgICAgICAgICAgPHA+PHN0cm9uZz5Ib3cgaXQgd29ya3M6PC9zdHJvbmc+IEN5Y2xlIGJldHdlZW4gc2VsbGluZyBwdXRzIGFuZCBjYWxscyBhdXRvbWF0aWNhbGx5PC9wPlxuICAgICAgICAgICAgPHA+PHN0cm9uZz5CZXN0IHBhcnQ6PC9zdHJvbmc+IFdvcmtzIGluIGFueSBtYXJrZXQgY29uZGl0aW9uPC9wPlxuICAgICAgICAgICAgPGJ1dHRvbiBzdHlsZT17eyBiYWNrZ3JvdW5kOiAnI2Y1OWUwYicsIGNvbG9yOiAnd2hpdGUnLCBwYWRkaW5nOiAnMTBweCAyMHB4JywgYm9yZGVyOiAnbm9uZScsIGJvcmRlclJhZGl1czogJzVweCcsIGN1cnNvcjogJ3BvaW50ZXInIH19PlxuICAgICAgICAgICAgICBTdGFydCBUaGUgV2hlZWwg4oaSXG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cblxuICAgICAgPGRpdiBzdHlsZT17eyBiYWNrZ3JvdW5kOiAnIzIyYzU1ZScsIGNvbG9yOiAnd2hpdGUnLCBwYWRkaW5nOiAnMjBweCcsIGJvcmRlclJhZGl1czogJzEwcHgnLCBtYXJnaW5Ub3A6ICczMHB4JywgdGV4dEFsaWduOiAnY2VudGVyJyB9fT5cbiAgICAgICAgPGgyPvCfmoAgWU9VUiBTWVNURU0gSVMgTElWRSBBTkQgUkVBRFkhPC9oMj5cbiAgICAgICAgPHA+Q29ubmVjdGVkIHRvIEFscGFjYSBUcmFkaW5nIOKAoiBSZWFsLXRpbWUgTWFya2V0IERhdGEg4oCiIEFJLVBvd2VyZWQgU3RyYXRlZ2llczwvcD5cbiAgICAgICAgPHA+PHN0cm9uZz5TdGFydCBtYWtpbmcgbW9uZXkgdG9kYXkgd2l0aCBwcm92ZW4gc3RyYXRlZ2llcyE8L3N0cm9uZz48L3A+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsIkhvbWVQYWdlIiwic2VsZWN0ZWRTdHJhdGVneSIsInNldFNlbGVjdGVkU3RyYXRlZ3kiLCJoYW5kbGVTdHJhdGVneUNsaWNrIiwic3RyYXRlZ3kiLCJhbGVydCIsImRpdiIsInN0eWxlIiwicGFkZGluZyIsImZvbnRGYW1pbHkiLCJtYXhXaWR0aCIsIm1hcmdpbiIsImgxIiwiY29sb3IiLCJtYXJnaW5Cb3R0b20iLCJiYWNrZ3JvdW5kIiwiYm9yZGVyUmFkaXVzIiwiaDIiLCJwIiwiZGlzcGxheSIsImdyaWRUZW1wbGF0ZUNvbHVtbnMiLCJnYXAiLCJib3hTaGFkb3ciLCJoMyIsImZvbnRTaXplIiwiZm9udFdlaWdodCIsImJvcmRlciIsInN0cm9uZyIsImJ1dHRvbiIsIm9uQ2xpY2siLCJjdXJzb3IiLCJvbk1vdXNlT3ZlciIsImUiLCJ0YXJnZXQiLCJvbk1vdXNlT3V0IiwibWFyZ2luVG9wIiwidGV4dEFsaWduIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});