{"parent": null, "pid": 20287, "argv": ["/Users/<USER>/.nvm/versions/node/v16.1.0/bin/node", "/Users/<USER>/Dropbox/code/src/github.com/nats-io/node-nuid/node_modules/.bin/mocha", "--timeout", "10000", "--slow", "750"], "execArgv": [], "cwd": "/Users/<USER>/Dropbox/code/src/github.com/nats-io/node-nuid", "time": 1620653637082, "ppid": 20286, "coverageFilename": "/Users/<USER>/Dropbox/code/src/github.com/nats-io/node-nuid/.nyc_output/f49742fa-3d53-4d68-a2af-4ac14319e192.json", "externalId": "", "uuid": "f49742fa-3d53-4d68-a2af-4ac14319e192", "files": ["/Users/<USER>/Dropbox/code/src/github.com/nats-io/node-nuid/lib/nuid.js"]}