// API Route for Market Analysis
// Provides market regime detection and trading recommendations

import { NextRequest, NextResponse } from 'next/server';
import { MarketDataService } from '../../../lib/api/market-data-service';
import fs from 'fs';
import path from 'path';

const marketData = new MarketDataService();

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const symbolsParam = searchParams.get('symbols');
    
    let symbols: string[] = [];
    
    if (symbolsParam) {
      symbols = symbolsParam.split(',').map(s => s.trim().toUpperCase());
    } else {
      // Read from watchlist.txt
      try {
        const watchlistPath = path.join(process.cwd(), 'watchlist.txt');
        const watchlistContent = fs.readFileSync(watchlistPath, 'utf-8');
        symbols = watchlistContent
          .split('\n')
          .map(line => line.trim())
          .filter(line => line && !line.startsWith('#'))
          .slice(0, 10); // Limit for analysis
      } catch (error) {
        symbols = ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA'];
      }
    }

    if (symbols.length === 0) {
      return NextResponse.json({ error: 'No symbols provided' }, { status: 400 });
    }

    // Get market analysis
    const analysis = await marketData.getMarketAnalysis(symbols);
    
    // Get market overview
    const marketOverview = await marketData.getMarketOverview();
    
    // Get upcoming events
    const [earnings, dividends] = await Promise.all([
      marketData.getUpcomingEarnings(14),
      marketData.getUpcomingDividends(30)
    ]);
    
    return NextResponse.json({
      success: true,
      data: {
        analysis,
        marketOverview,
        upcomingEvents: {
          earnings: earnings.filter(e => symbols.includes(e.symbol)),
          dividends: dividends.filter(d => symbols.includes(d.symbol))
        }
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error in market analysis API:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch market analysis',
        message: error instanceof Error ? error.message : 'Unknown error'
      }, 
      { status: 500 }
    );
  }
}
